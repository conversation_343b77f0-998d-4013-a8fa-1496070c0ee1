import { world, system } from "@minecraft/server";

// List of forbidden block types
const FORBIDDEN_BLOCKS = [
    "minecraft:structure_block",
    "minecraft:structure_void",
    "minecraft:command_block",
    "minecraft:repeating_command_block",
    "minecraft:chain_command_block"
];

function cleanInventory(player) {
    try {
        // Skip players with "Primary" tag
        if (player.hasTag("Primary")) return;
        
        const inventory = player.getComponent("inventory").container;
        
        // Check all inventory slots
        for (let slot = 0; slot < inventory.size; slot++) {
            const item = inventory.getItem(slot);
            if (item && FORBIDDEN_BLOCKS.includes(item.typeId)) {
                inventory.setItem(slot, undefined);
            }
        }
    } catch (error) {
        console.warn("Inventory clean error: " + error);
    }
}

// Run every 20 ticks (1 second)
system.runInterval(() => {
    const players = world.getAllPlayers();
    for (const player of players) cleanInventory(player);
}, 20);

// Clean when player joins
world.afterEvents.playerSpawn.subscribe(({ player, initialSpawn }) => {
    if (initialSpawn) cleanInventory(player);
});