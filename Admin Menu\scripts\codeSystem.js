import { world, system } from "@minecraft/server";
import { ActionFormData, MessageFormData, ModalFormData } from "@minecraft/server-ui";
import { Database } from "./DB.js";

// Database setup
const codeDB = new Database("warp_codes");
const playerDB = new Database("warp_players");

// Constants
const CODE_LENGTH = 8;
const MAX_CODES = 50;
const CODE_TYPES = {
    BASIC: { tag: "WARP1", durations: { "30 Days": 30, "Yearly": 365 } },
    ENHANCED: { tag: "WARP2", durations: { "30 Days": 30, "Yearly": 365 } },
    // This is the updated line with the new durations for the build tag
    BUILD: { tag: "build", durations: { "3 Days": 3, "7 Days": 7, "15 Days": 15, "30 Days": 30, "60 Days": 60 } }
};
const TAG_TO_TYPE = {
    [CODE_TYPES.BASIC.tag]: CODE_TYPES.BASIC,
    [CODE_TYPES.ENHANCED.tag]: CODE_TYPES.ENHANCED,
    [CODE_TYPES.BUILD.tag]: CODE_TYPES.BUILD
};

function generateCode() {
    return Math.random().toString().slice(2, 2 + CODE_LENGTH).padEnd(CODE_LENGTH, '0');
}

function createBaseForm(title) {
    return new ActionFormData().title(title);
}

try {
    if (!world.beforeEvents || !world.beforeEvents.chatSend) {
        console.error("Error: world.beforeEvents.chatSend is undefined. Check your @minecraft/server version.");
        throw new Error("Chat event subscription not available.");
    }

    world.beforeEvents.chatSend.subscribe(event => {
        const player = event.sender;
        const message = event.message;

        if (message.startsWith("-toc ") && player.hasTag("build")) {
            event.cancel = true;
            const targetName = message.slice(5).trim();
            system.runTimeout(() => transferBuildTag(player, targetName), 60);
        }
    });
} catch (error) {
    console.error(`Failed to initialize chat event listener: ${error.message}`);
}

export function showMainMenu(player) {
    const form = createBaseForm("Code Manager")
        .body("Select an action:")
        .button("Generate Codes")
        .button("View Codes")
        .button("Edit Codes");

    form.show(player).then(response => {
        if (response.canceled) return;
        switch (response.selection) {
            case 0: showGenerateTypeMenu(player); break;
            case 1: showViewMenu(player); break;
            case 2: showEditMenu(player); break;
        }
    }).catch(error => console.error(`Error showing main menu: ${error.message}`));
}

function showGenerateTypeMenu(player) {
    const form = createBaseForm("Generate Codes")
        .body("Select code type:")
        .button("Basic Codes")
        .button("Enhanced Codes")
        .button("Build Codes");

    form.show(player).then(response => {
        if (response.canceled) return;
        let type;
        switch (response.selection) {
            case 0: type = CODE_TYPES.BASIC; break;
            case 1: type = CODE_TYPES.ENHANCED; break;
            case 2: type = CODE_TYPES.BUILD; break;
        }
        showDurationSelection(player, type);
    }).catch(error => console.error(`Error showing generate type menu: ${error.message}`));
}

function showDurationSelection(player, codeType) {
    const durationLabels = Object.keys(codeType.durations);
    const form = createBaseForm(`Generate ${codeType.tag} Codes`)
        .body("Select duration:");

    for (const label of durationLabels) {
        form.button(label);
    }

    form.show(player).then(response => {
        if (response.canceled || response.selection == null) return;
        const durationLabel = durationLabels[response.selection];
        const duration = codeType.durations[durationLabel];
        showCountSelection(player, codeType, duration, durationLabel);
    }).catch(error => console.error(`Error showing duration selection: ${error.message}`));
}

function showCountSelection(player, codeType, duration, durationLabel) {
    const form = new ModalFormData()
        .title(`Generate ${codeType.tag} ${durationLabel} Codes`)
        .textField("Number of codes (1–50)", "1");

    form.show(player).then(response => {
        if (response.canceled || !response.formValues) return;
        const input = response.formValues[0].trim();
        const count = parseInt(input, 10);
        if (isNaN(count) || count < 1 || count > MAX_CODES) {
            player.sendMessage("§cInvalid input! Please enter a number between 1 and 50.");
            return;
        }
        generateAndStoreCodes(player, codeType, count, duration, durationLabel);
    }).catch(error => console.error(`Error showing count selection: ${error.message}`));
}

function generateAndStoreCodes(player, codeType, count, duration, durationLabel) {
    const codes = [];
    for (let i = 0; i < count; i++) {
        let code;
        do {
            code = generateCode();
        } while (codeDB.has(code));

        codeDB.write(code, {
            type: codeType.tag,
            duration: duration,
            redeemed: false,
            generatedAt: Date.now()
        });
        codes.push(code);
    }
    console.log(`Generated ${count} ${codeType.tag} ${durationLabel} codes:\n${codes.join("\n")}`);
    player.sendMessage(`§aSuccessfully generated ${count} ${codeType.tag} ${durationLabel} codes. Check console.`);
}

function showViewMenu(player) {
    const [basic, enhanced, build] = getUnredeemedCodes();
    let bodyText = "Basic Codes:\n";
    bodyText += basic.join(", ") || "None";
    bodyText += "\n\nEnhanced Codes:\n";
    bodyText += enhanced.join(", ") || "None";
    bodyText += "\n\nBuild Codes:\n";
    bodyText += build.join(", ") || "None";

    const form = new MessageFormData()
        .title("Unredeemed Codes")
        .body(bodyText)
        .button1("OK");

    form.show(player).catch(error => console.error(`Error showing view menu: ${error.message}`));
}

function getUnredeemedCodes() {
    const basic = [], enhanced = [], build = [];
    codeDB.keys().forEach(key => {
        const data = codeDB.read(key);
        if (!data.redeemed) {
            switch (data.type) {
                case CODE_TYPES.BASIC.tag: basic.push(key); break;
                case CODE_TYPES.ENHANCED.tag: enhanced.push(key); break;
                case CODE_TYPES.BUILD.tag: build.push(key); break;
            }
        }
    });
    return [basic, enhanced, build];
}

function showEditMenu(player) {
    const codes = [...codeDB.keys()].filter(key => {
        const data = codeDB.read(key);
        return data && !data.redeemed;
    });

    if (codes.length === 0) {
        player.sendMessage("§cNo unredeemed codes available to delete.");
        return;
    }

    const form = new ModalFormData()
        .title("Delete Codes")
        .dropdown("Select code to delete", codes);

    form.show(player).then(response => {
        if (response.canceled || !response.formValues) return;
        const selectedCode = codes[response.formValues[0]];
        codeDB.delete(selectedCode);
        player.sendMessage(`§aDeleted code: ${selectedCode}`);
    }).catch(error => console.error(`Error showing edit menu: ${error.message}`));
}

export function showRedeemForm(player) {
    const form = new ModalFormData()
        .title("Redeem Code")
        .textField("Enter 8-digit code:", "12345678");

    form.show(player).then(response => {
        if (response.canceled || !response.formValues) return;
        processRedemption(player, response.formValues[0].trim());
    }).catch(error => console.error(`Error showing redeem form: ${error.message}`));
}

function processRedemption(player, code) {
    if (!codeDB.has(code)) return player.sendMessage("§cInvalid code!");

    const codeData = codeDB.read(code);
    if (codeData.redeemed) return player.sendMessage("§cCode already redeemed!");

    const codeType = TAG_TO_TYPE[codeData.type];
    if (!codeType) return player.sendMessage("§cInvalid code type!");

    codeData.redeemed = true;
    codeData.redeemedBy = player.id;
    codeData.redeemedAt = Date.now();
    codeDB.write(code, codeData);

    applyCodeEffect(player, codeType, codeData.duration);
}

function applyCodeEffect(player, codeType, duration) {
    player.addTag(codeType.tag);
    player.sendMessage(`§aSuccessfully redeemed ${codeType.tag} for ${duration} days!`);

    const expirationTime = duration * 24 * 60 * 60 * 1000;
    system.runTimeout(() => {
        if (player.isValid && player.hasTag(codeType.tag)) {
            player.removeTag(codeType.tag);
            player.sendMessage(`§eYour ${codeType.tag} access has expired.`);
        }
    }, expirationTime / 50);

    const playerData = playerDB.read(player.id) || { activeCodes: [] };
    playerData.activeCodes.push({
        type: codeType.tag,
        expiresAt: Date.now() + expirationTime
    });
    playerDB.write(player.id, playerData);
}

function transferBuildTag(sender, targetName) {
    const targetPlayer = world.getPlayers().find(p => p.name.toLowerCase() === targetName.toLowerCase());
    if (!targetPlayer || targetPlayer.id === sender.id) {
        sender.sendMessage(`§cInvalid target.`);
        return;
    }

    const senderData = playerDB.read(sender.id);
    if (!senderData || !senderData.activeCodes) return sender.sendMessage(`§cYou have no active build codes to transfer!`);

    const buildCode = senderData.activeCodes.find(code => code.type === CODE_TYPES.BUILD.tag && code.expiresAt > Date.now());
    if (!buildCode) return sender.sendMessage(`§cYou have no active build code to transfer!`);

    const now = Date.now();
    const remainingTime = buildCode.expiresAt - now;
    if (remainingTime <= 0) return sender.sendMessage(`§cYour build code has expired!`);

    senderData.activeCodes = senderData.activeCodes.filter(code => code !== buildCode);
    playerDB.write(sender.id, senderData);
    sender.removeTag(CODE_TYPES.BUILD.tag);
    sender.sendMessage(`§aTransferred build tag to ${targetPlayer.name}.`);

    const targetData = playerDB.read(targetPlayer.id) || { activeCodes: [] };
    targetData.activeCodes.push({
        type: CODE_TYPES.BUILD.tag,
        expiresAt: now + remainingTime
    });
    playerDB.write(targetPlayer.id, targetData);
    targetPlayer.addTag(CODE_TYPES.BUILD.tag);
    targetPlayer.sendMessage(`§aYou received the build tag from ${sender.name} for ${Math.ceil(remainingTime / (24 * 60 * 60 * 1000))} days!`);
}

// Hourly expiration cleanup
system.runInterval(() => {
    const now = Date.now();
    playerDB.keys().forEach(playerId => {
        const data = playerDB.read(playerId);
        if (!data || !data.activeCodes) return;

        const player = world.getPlayers().find(p => p.id === playerId);
        const validCodes = data.activeCodes.filter(code => code.expiresAt > now);
        if (validCodes.length !== data.activeCodes.length) {
            playerDB.write(playerId, { activeCodes: validCodes });
        }

        if (!player) return;

        const activeByTag = {};
        validCodes.forEach(code => {
            if (!activeByTag[code.type]) activeByTag[code.type] = [];
            activeByTag[code.type].push(code);
        });

        for (const tag in activeByTag) {
            if (!player.hasTag(tag)) {
                player.addTag(tag);
            }
        }

        Object.values(CODE_TYPES).forEach(({ tag }) => {
            const stillValid = activeByTag[tag] && activeByTag[tag].length > 0;
            if (!stillValid && player.hasTag(tag)) {
                player.removeTag(tag);
                player.sendMessage(`§eYour ${tag} access has expired.`);
            }
        });
    });
}, 72000);
