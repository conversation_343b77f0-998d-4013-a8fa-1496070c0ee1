/*
  customCommands.js — Bedrock 1.21.80 (Script API 2.0.0‑beta) By N1NJ4LL0
  -----------------------------------------------------------
  All custom commands for the Ninjos server. Permissions are enforced
  via CommandPermissionLevel. This script requires the Beta APIs
  experiment to be enabled in the world settings.
*/

import {
  world,
  system,
  CustomCommandParamType,
  CustomCommandStatus,
  CustomCommandOrigin,
  CustomCommandSource,
  CommandPermissionLevel,
  Player,
  GameMode,
  EffectType
} from "@minecraft/server";


import { getPlayerStats, getLeaderboardStats } from "../management/playerStats.js";

////////////////////////////////////////////////////////////////////////////////
// Dynamically load UI helpers
////////////////////////////////////////////////////////////////////////////////
let mainMenu;
let regionRequestMenu;
let showCodesMenu;
let showRedeemMenu;

system.run(async () => {
  try {
    // Import all modules in parallel
    const [uiModule, regionModule, codeModule] = await Promise.all([
      import("../ui/mainMenu.js"),
      import("../management/regionRequests.js"),
      import("../codeSystem.js") // Assumes codeSystem.js is in the same directory
    ]);

    // Grab the exported menus (null-safe in case any export is missing)
    mainMenu           = uiModule?.mainMenu;
    regionRequestMenu  = regionModule?.regionRequestMenu;
    showCodesMenu      = codeModule?.showMainMenu;
    showRedeemMenu     = codeModule?.showRedeemForm;

  } catch (err) {
    console.warn("[CustomCommands] Failed to load modules:", err);
  }
});

////////////////////////////////////////////////////////////////////////////////
// Helper functions
////////////////////////////////////////////////////////////////////////////////

/**
 * Attempt to resolve the executing player from a CustomCommandOrigin.
 */
function getPlayerFromOrigin(origin /** @type {CustomCommandOrigin} */) {
  if (!origin) return null;

  // Common case: command typed directly by a player
  if (
    origin.sourceType === CustomCommandSource.Entity &&
    origin.sourceEntity instanceof Player
  ) {
    return origin.sourceEntity;
  }

  // Some flows populate `initiator` instead
  if (origin.initiator instanceof Player) return origin.initiator;

  // Command blocks: look for nearest player within 8 blocks
  if (origin.sourceBlock) {
    const { x, y, z } = origin.sourceBlock.location;
    return world.getPlayers({ location: { x, y, z }, maxDistance: 8 })[0] ?? null;
  }

  return null;
}

/**
 * Defer a world‑mutating action until the next server tick (outside of the
 * read‑only command callback).
 */
function mutate(fn) {
  system.run(() => {
    try {
      fn();
    } catch (err) {
      console.error("[CustomCommands] Mutation error:", err);
    }
  });
}

////////////////////////////////////////////////////////////////////////////////
// Register commands at startup
////////////////////////////////////////////////////////////////////////////////

system.beforeEvents.startup.subscribe(({ customCommandRegistry }) => {
  console.log("§a[CustomCommands] Loaded v1.2");

  // -----------------------------------------------------------------------
  // /gmc — switch to Creative mode
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
  {
    name: "ninjos:gmc",
    description: "Switch to Creative mode",
    permissionLevel: CommandPermissionLevel.GameDirectors,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uGMC§7] §cPlayers only.",
      };
    }
    mutate(() => {
      try {
        player.setGameMode("Creative");
        // Verify the mode was set correctly
        if (player.getGameMode() === "Creative") {
          player.sendMessage("§7[§uGMC§7] §fSwitched to §bCreative§f mode.");
        } else {
          player.sendMessage("§7[§uGMC§7] §cFailed to switch to Creative mode.");
          console.warn("[CustomCommands] GMC failed to set creative mode for", player.name);
        }
      } catch (err) {
        player.sendMessage("§7[§uGMC§7] §cError switching to Creative mode.");
        console.error("[CustomCommands] GMC error:", err);
      }
    });
    return { status: CustomCommandStatus.Success };
  }
);


  // -----------------------------------------------------------------------
  // /gmc — switch to Survival mode
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
  {
    name: "ninjos:gms",
    description: "Switch to Survival mode",
    permissionLevel: CommandPermissionLevel.GameDirectors,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uGMS§7] §cPlayers only.",
      };
    }
    mutate(() => {
      try {
        player.setGameMode("Survival");
        // Verify the mode was set correctly
        if (player.getGameMode() === "Survival") {
          player.sendMessage("§7[§uGMS§7] §fSwitched to §bSurvival§f mode.");
        } else {
          player.sendMessage("§7[§uGMS§7] §cFailed to switch to Survival mode.");
          console.warn("[CustomCommands] GMS failed to set Survival mode for", player.name);
        }
      } catch (err) {
        player.sendMessage("§7[§uGMC§7] §cError switching to Survival mode.");
        console.error("[CustomCommands] GMS error:", err);
      }
    });
    return { status: CustomCommandStatus.Success };
  }
);

// -----------------------------------------------------------------------
  // /gmspec — switch to Spectator mode
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
  {
    name: "ninjos:gmspec",
    description: "Switch to Spectator mode",
    permissionLevel: CommandPermissionLevel.GameDirectors,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uGMSPEC§7] §cPlayers only.",
      };
    }
    mutate(() => {
      try {
        player.setGameMode("Spectator");
        // Verify the mode was set correctly
        if (player.getGameMode() === "Spectator") {
          player.sendMessage("§7[§uGMSPEC§7] §fSwitched to §bSpectator§f mode.");
        } else {
          player.sendMessage("§7[§uGMSPEC§7] §cFailed to switch to Spectator mode.");
          console.warn("[CustomCommands] GMSPEC failed to set spectator mode for", player.name);
        }
      } catch (err) {
        player.sendMessage("§7[§uGMSPEC§7] §cError switching to Spectator mode.");
        console.error("[CustomCommands] GMSPEC error:", err);
      }
    });
    return { status: CustomCommandStatus.Success };
  }
);
  
  // -----------------------------------------------------------------------
  // /serverhelp — list custom commands
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
    {
      name: "ninjos:serverhelp",
      description: "List all available custom commands",
      permissionLevel: CommandPermissionLevel.Any,
      mandatoryParameters: [],
      optionalParameters: [],
    },
    (origin) => {
      const player = getPlayerFromOrigin(origin);
      if (!(player instanceof Player)) {
        return {
          status: CustomCommandStatus.Failure,
          message: "§7[§uHelp§7] §cPlayers only.",
        };
      }

      const lines = [
        "§e=== Custom Commands ===",
        "§a/serverhelp §7- Show this list",
        "§a/online §7- Show online players",
        "§a/stats §7- View your player stats",
        "§a/leaderboard §7- View server leaderboards",
        "§a/redeem §7- Redeem a code for perks",
        "§a/claim §7- Open the region claim menu",
        "§a/menu §7- Main admin menu",
        "§a/codes §7- Admin code management",
        "§a/gmc §7- Creative mode",
        "§a/invis §7- Toggle invisibility",
        "§a/nightvision §7- Toggle night vision"
      ];

      player.sendMessage(lines.join("\n"));
      return { status: CustomCommandStatus.Success };
    }
  );

  // -----------------------------------------------------------------------
  // /invis — toggle invisibility
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
  {
    name: "ninjos:invis",
    description: "Toggle invisibility effect",
    permissionLevel: CommandPermissionLevel.GameDirectors,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uInvis§7] §cPlayers only.",
      };
    }
    mutate(() => {
      if (player.getEffect("invisibility")) {
        player.removeEffect("invisibility");
        player.sendMessage("§7[§uInvis§7] §fInvisibility §cdisabled§f.");
      } else {
        player.addEffect("invisibility", 999999, { showParticles: false });
        player.sendMessage("§7[§uInvis§7] §fInvisibility §aenabled§f.");
      }
    });
    return { status: CustomCommandStatus.Success };
  }
);

// -----------------------------------------------------------------------
// /nightvision — toggle night-vision effect
// -----------------------------------------------------------------------
customCommandRegistry.registerCommand(
  {
    name: "ninjos:nightvision",
    description: "Toggle night vision effect",
    permissionLevel: CommandPermissionLevel.GameDirectors,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uNV§7] §cPlayers only.",
      };
    }
    mutate(() => {
      if (player.getEffect("night_vision")) {
        player.removeEffect("night_vision");
        player.sendMessage("§7[§uNV§7] §fNight vision §cdisabled§f.");
      } else {
        player.addEffect("night_vision", 999999, { showParticles: false });
        player.sendMessage("§7[§uNV§7] §fNight vision §aenabled§f.");
      }
    });
    return { status: CustomCommandStatus.Success };
  }
);

  // -----------------------------------------------------------------------
  // /online — list currently online players
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
    {
      name: "ninjos:online",
      description: "List players currently online",
      permissionLevel: CommandPermissionLevel.Any,
      mandatoryParameters: [],
      optionalParameters: [],
    },
    (origin) => {
      const player = getPlayerFromOrigin(origin);
      if (!(player instanceof Player)) {
        return {
          status: CustomCommandStatus.Failure,
          message: "§7[§uOnline§7] §cPlayers only.",
        };
      }
      const list = world.getPlayers();
      if (list.length === 0) {
        player.sendMessage("§7[§uOnline§7] §fNo players are currently online.");
        return { status: CustomCommandStatus.Success };
      }
      const names = list.map((p) => p.name).join("§f, §6");
      player.sendMessage(`§7[§uOnline§7] §fOnline players (§6${list.length}§f): §6${names}`);
      return { status: CustomCommandStatus.Success };
    }
  );
  
  // -----------------------------------------------------------------------
  // /codes — open the code management UI
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
    {
      name: "ninjos:codes",
      description: "Open the code management menu",
      permissionLevel: CommandPermissionLevel.GameDirectors, // Admin command
      mandatoryParameters: [],
      optionalParameters: [],
    },
    (origin) => {
      const player = getPlayerFromOrigin(origin);
      if (!(player instanceof Player)) {
        return {
          status: CustomCommandStatus.Failure,
          message: "§7[§uCodes§7] §cPlayers only.",
        };
      }
      if (typeof showCodesMenu !== "function") {
        player.sendMessage("§7[§uCodes§7] §cCode UI not available (module load failed).");
        return { status: CustomCommandStatus.Failure };
      }
      mutate(() => {
        try {
          showCodesMenu(player);
        } catch (err) {
          console.error("[CustomCommands] showCodesMenu() error:", err);
          player.sendMessage("§7[§uCodes§7] §cFailed to open code menu.");
        }
      });
      return { status: CustomCommandStatus.Success };
    }
  );
  
  // -----------------------------------------------------------------------
  // /redeem — open the code redemption UI
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
    {
      name: "ninjos:redeem",
      description: "Redeem a perk code",
      permissionLevel: CommandPermissionLevel.Any, // Player command
      mandatoryParameters: [],
      optionalParameters: [],
    },
    (origin) => {
      const player = getPlayerFromOrigin(origin);
      if (!(player instanceof Player)) {
        return {
          status: CustomCommandStatus.Failure,
          message: "§7[§uRedeem§7] §cPlayers only.",
        };
      }
      if (typeof showRedeemMenu !== "function") {
        player.sendMessage("§7[§uRedeem§7] §cRedeem UI not available (module load failed).");
        return { status: CustomCommandStatus.Failure };
      }
      mutate(() => {
        try {
          showRedeemMenu(player);
        } catch (err) {
          console.error("[CustomCommands] showRedeemMenu() error:", err);
          player.sendMessage("§7[§uRedeem§7] §cFailed to open redeem menu.");
        }
      });
      return { status: CustomCommandStatus.Success };
    }
  );

  // -----------------------------------------------------------------------
  // /menu — open the custom UI main menu
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
    {
      name: "ninjos:menu",
      description: "Open the admin menu",
      permissionLevel: CommandPermissionLevel.GameDirectors,
      mandatoryParameters: [],
      optionalParameters: [],
    },
    (origin) => {
      const player = getPlayerFromOrigin(origin);
      if (!(player instanceof Player)) {
        return {
          status: CustomCommandStatus.Failure,
          message: "§7[§uMenu§7] §cPlayers only.",
        };
      }
      if (typeof mainMenu !== "function") {
        player.sendMessage("§7[§uMenu§7] §cMenu UI not available (module load failed).");
        return { status: CustomCommandStatus.Failure };
      }
      mutate(() => {
        try {
          mainMenu(player);
          player.sendMessage("§7[§uMenu§7] §fOpening main menu…");
        } catch (err) {
          console.error("[CustomCommands] mainMenu() error:", err);
          player.sendMessage("§7[§uMenu§7] §cFailed to open menu.");
        }
      });
      return { status: CustomCommandStatus.Success };
    }
  );

 // -----------------------------------------------------------------------
  // /claim — open the region claim main menu
  // -----------------------------------------------------------------------
  customCommandRegistry.registerCommand(
    {
      name: "ninjos:claim",
      description: "Open the region claim menu",
      permissionLevel: CommandPermissionLevel.Any,
      mandatoryParameters: [],
      optionalParameters: [],
    },
    (origin) => {
      const player = getPlayerFromOrigin(origin);
      if (!(player instanceof Player)) {
        return {
          status: CustomCommandStatus.Failure,
          message: "§7[§uClaim Menu§7] §cPlayers only.",
        };
      }
      if (typeof mainMenu !== "function") {
        player.sendMessage("§7[§uClaim Menu§7] §cMenu UI not available (module load failed).");
        return { status: CustomCommandStatus.Failure };
      }
      mutate(() => {
        try {
          mainMenu(player);
          player.sendMessage("§7[§uClaim Menu§7] §fOpening main menu…");
        } catch (err) {
          console.error("[CustomCommands] regionRequestMenu() error:", err);
          player.sendMessage("§7[§uClaim Menu§7] §cFailed to open menu.");
        }
      });
      return { status: CustomCommandStatus.Success };
    }
  );

 
customCommandRegistry.registerCommand(
  {
    name: "ninjos:leaderboard",
    description: "View Leaderboard (kills, deaths, blocks broken)",
    permissionLevel: CommandPermissionLevel.Any,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uLeaderboard§7] §cPlayers only.",
      };
    }
    
    try {
      const leaderboardText = getLeaderboardStats(); // This gets the formatted leaderboard
      player.sendMessage(leaderboardText); // Fixed: Changed statsText to leaderboardText
      return { status: CustomCommandStatus.Success };
    } catch (err) {
      console.error("[Leaderboard Command] Error:", err);
      player.sendMessage("§7[§uLeaderboard§7] §cFailed to load leaderboard. Try again later.");
      return { status: CustomCommandStatus.Failure };
    }
  }
);
  
// Then inside system.beforeEvents.startup.subscribe, add this command registration:
customCommandRegistry.registerCommand(
  {
    name: "ninjos:stats",
    description: "View your player stats (kills, deaths, blocks broken)",
    permissionLevel: CommandPermissionLevel.Any,
    mandatoryParameters: [],
    optionalParameters: [],
  },
  (origin) => {
    const player = getPlayerFromOrigin(origin);
    if (!(player instanceof Player)) {
      return {
        status: CustomCommandStatus.Failure,
        message: "§7[§uStats§7] §cPlayers only.",
      };
    }
    
    try {
      const statsText = getPlayerStats(player);
      player.sendMessage(statsText);
      return { status: CustomCommandStatus.Success };
    } catch (err) {
      console.error("[Stats Command] Error:", err);
      player.sendMessage("§7[§uStats§7] §cFailed to load stats. Try again later.");
      return { status: CustomCommandStatus.Failure };
    }
  }
);
});
