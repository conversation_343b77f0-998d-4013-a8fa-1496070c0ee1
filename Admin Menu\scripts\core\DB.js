import { world, ScoreboardObjective, ScoreboardIdentityType, ScoreboardIdentity, system, Entity } from "@minecraft/server";

const { FakePlayer } = ScoreboardIdentityType;
const databases = new Map();
const split = "\n_`Split`_\n";

function endTickCall(callback) {
    system.run(() => system.run(() => system.run(callback)));
}

export const DatabaseSavingModes = {
    OneTimeSave: "OneTimeSave",
    EndTickSave: "EndTickSave",
    TickInterval: "TickInterval"
};

const ChangeAction = {
    Change: 0,
    Remove: 1
};

function run(thisClass, key, value, action) {
    system.run(() => {
        if (thisClass._source_.has(key)) {
            thisClass._scoreboard_.removeParticipant(thisClass._source_.get(key));
        }
        if (action === ChangeAction.Remove) {
            thisClass._source_.delete(key);
        } else {
            thisClass._source_.set(key, value);
            thisClass._scoreboard_.setScore(value, 0);
        }
    });
}

const SavingModes = {
    [DatabaseSavingModes.OneTimeSave](thisClass, key, value, action) {
        run(thisClass, key, value, action);
    },
    [DatabaseSavingModes.EndTickSave](thisClass, key, value, action) {
        if (!thisClass.hasChanges) {
            endTickCall(() => {
                for (const [k, { action, value }] of thisClass._changes_.entries()) {
                    run(thisClass, k, value, action);
                }
                thisClass._changes_.clear();
                thisClass.hasChanges = false;
            });
        }
        thisClass.hasChanges = true;
        thisClass._changes_.set(key, { action, value });
    },
    [DatabaseSavingModes.TickInterval](thisClass, key, value, action) {
        thisClass.hasChanges = true;
        thisClass._changes_.set(key, { action, value });
    }
};

class ScoreboardDatabaseManager extends Map {
    _loaded_ = false;
    _saveMode_;
    hasChanges = false;
    _loadingPromise_;
    get maxLength() { return 30e3; }
    _scoreboard_;
    _source_ = new Map();
    _changes_ = new Map();
    get _parser_() { return JSON; }
    get savingMode() { return this._saveMode_; }

    constructor(objective, saveMode = DatabaseSavingModes.EndTickSave, interval = 5) {
    super();
    this._saveMode_ = saveMode;
    this._nameId_ = objective;
    this.interval = interval ?? 5;
    if (!objective) throw new RangeError("First parameter is not valid: " + objective);
    if (typeof objective !== "string" && !(objective instanceof ScoreboardObjective)) {
        throw new RangeError("First parameter is not valid: " + objective);
    }
    system.run(() => {
        this._scoreboard_ = typeof objective === "string"
            ? (world.scoreboard.getObjective(objective) ?? world.scoreboard.addObjective(objective, objective))
            : objective;
        // Move database registration inside system.run to ensure _scoreboard_ is set
        if (databases.has(this._scoreboard_.id)) {
            return databases.get(this._scoreboard_.id);
        }
        databases.set(this._scoreboard_.id, this);
        this._nameId_ = this._scoreboard_.id;
    });
    // Do not access this.id here; wait for system.run to complete
    if (this._saveMode_ === DatabaseSavingModes.TickInterval) {
        system.runInterval(() => {
            if (this.hasChanges) {
                endTickCall(() => {
                    for (const [k, { action, value }] of this._changes_.entries()) {
                        run(this, k, value, action);
                    }
                    this._changes_.clear();
                    this.hasChanges = false;
                });
            }
        }, this.interval);
    }
}

    load() {
        if (this._loaded_) return this;
        system.run(() => {
            for (const participant of this._scoreboard_.getParticipants()) {
                const { displayName, type } = participant;
                if (type !== FakePlayer) continue;
                const [name, data] = displayName.split(split);
                this._source_.set(name, participant);
                super.set(name, this._parser_.parse(data));
            }
            this._loaded_ = true;
        });
        return this;
    }

    async loadAsync() {
        if (this._loaded_) return this._loadingPromise_ ?? Promise.resolve(this);
        const promise = (async () => {
            await system.run(async () => {
                for (const participant of this._scoreboard_.getParticipants()) {
                    const { displayName, type } = participant;
                    if (type !== FakePlayer) continue;
                    const [name, data] = displayName.split(split);
                    this._source_.set(name, participant);
                    super.set(name, this._parser_.parse(data));
                }
                this._loaded_ = true;
            });
            return this;
        })();
        this._loadingPromise_ = promise;
        return promise;
    }

    set(key, value) {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        const newValue = `${key}${split}${this._parser_.stringify(value)}`;
        if (newValue.length > this.maxLength) throw new RangeError("Value is too large for one property");
        super.set(key, value);
        this._onChange_(key, newValue, ChangeAction.Change);
        return this;
    }

    delete(key) {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        this._onChange_(key, null, ChangeAction.Remove);
        return super.delete(key);
    }

    clear() {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        system.run(() => {
            for (const [key] of this.entries()) {
                this.delete(key);
            }
        });
    }

    forEach(callback) {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        for (const [key, value] of this.entries()) {
            callback(key, value);
        }
    }

    keys() {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        let keys = [];
        for (const [key] of this.entries()) keys.push(key);
        return keys;
    }

    values() {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        let values = [];
        for (const [_, value] of this.entries()) values.push(value);
        return values;
    }

    get length() {
        return this.size;
    }

    _onChange_(key, value, action) {
        if (!this._loaded_) throw new ReferenceError("Database is not loaded");
        SavingModes[this._saveMode_](this, key, value, action);
    }

    get objective() { return this._scoreboard_; }
    get id() { return this._scoreboard_.id; }
    get loaded() { return this._loaded_; }
    get type() { return "DefaultJsonType"; }

    get loadingAwaiter() { return this._loadingPromise_ ?? this.loadAsync(); }

    rebuild() {
        if (this.objective?.isValid()) return this;
        system.run(() => {
            const newScores = world.scoreboard.addObjective(this._nameId_, this._nameId_);
            this._scoreboard_ = newScores;
            this._source_ = new Map();
            for (const [k, v] of this.entries()) {
                const data = `${k}${split}${this._parser_.stringify(v)}`;
                newScores.setScore(data, 0);
                this._source_.set(k, data);
            }
        });
        return this;
    }

    async rebuildAsync() {
        if (this.objective?.isValid()) return this;
        await system.run(async () => {
            const newScores = world.scoreboard.addObjective(this._nameId_, this._nameId_);
            this._scoreboard_ = newScores;
            this._source_ = new Map();
            for (const [k, v] of this.entries()) {
                const data = `${k}${split}${this._parser_.stringify(v)}`;
                newScores.setScore(data, 0);
                this._source_.set(k, data);
                await null;
            }
        });
        return this;
    }
}

export class JsonDatabase extends ScoreboardDatabaseManager {
    get type() { return "JsonType"; }
}

export class Database {
    constructor(name) {
        this.Database = new JsonDatabase(name).load();
    }

    get length() {
        return this.Database.length;
    }

    read(key) {
        return this.Database.get(key);
    }

    write(key, value) {
        return this.Database.set(key, value);
    }

    has(key) {
        return this.Database.has(key);
    }

    delete(key) {
        return this.Database.delete(key);
    }

    clear() {
        return this.Database.clear();
    }

    keys() {
        return this.Database.keys();
    }

    values() {
        return this.Database.values();
    }

    forEach(callback) {
        this.Database.forEach(callback);
    }
}