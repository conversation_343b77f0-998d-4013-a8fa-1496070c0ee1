import { world, system } from "@minecraft/server";

// Use in-memory storage with periodic serialization to dynamic properties
export class PropertyDatabase {
    constructor(name) {
        this.name = name;
        this._data = new Map();
        this._loaded = false;
        this._init();
    }

    _init() {
        try {
            // Load data from dynamic property if it exists
            const savedData = world.getDynamicProperty(`db_${this.name}`);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                this._data = new Map(Object.entries(parsed));
            }
            this._loaded = true;
            console.log(`Initialized database: ${this.name}`);
        } catch (error) {
            console.error(`Failed to initialize database ${this.name}:`, error);
            this._loaded = true; // Set to true anyway to prevent blocking operations
        }
    }

    _save() {
        try {
            // Convert Map to object for serialization
            const serialized = Object.fromEntries(this._data);
            world.setDynamicProperty(`db_${this.name}`, JSON.stringify(serialized));
            console.log(`Saved database: ${this.name}`);
        } catch (error) {
            console.error(`Failed to save database ${this.name}:`, error);
        }
    }

    get(key) {
        return this._data.get(key);
    }

    set(key, value) {
        this._data.set(key, value);
        this._save();
        return this;
    }

    has(key) {
        return this._data.has(key);
    }

    delete(key) {
        const result = this._data.delete(key);
        this._save();
        return result;
    }

    clear() {
        this._data.clear();
        this._save();
    }

    keys() {
        return Array.from(this._data.keys());
    }

    values() {
        return Array.from(this._data.values());
    }

    entries() {
        return Array.from(this._data.entries());
    }

    get size() {
        return this._data.size;
    }
}

// Create a compatibility layer for the existing Database class
export class Database {
    constructor(name) {
        this.Database = new PropertyDatabase(name);
    }

    get length() {
        return this.Database.size;
    }

    read(key) {
        return this.Database.get(key);
    }

    write(key, value) {
        return this.Database.set(key, value);
    }

    has(key) {
        return this.Database.has(key);
    }
}

// Export the original database names to maintain compatibility
export const AdminDB = new PropertyDatabase("AdminDB");
export const WarpsDB = new PropertyDatabase("WarpsDB");
export const ClaimedRegionsDB = new PropertyDatabase("ClaimedRegionsDB");
export const playerRegionsDB = new PropertyDatabase("PlayerRegionsDB");

