import { world, system } from "@minecraft/server";
import { JsonDatabase } from "./DB.js";

function createDatabase(dbName) {
    
    // Create and load the database instance immediately
    const db = new JsonDatabase(dbName).load();
    
    // Schedule the data transfer to happen asynchronously
    system.run(() => {
        const dynamicPropertyKey = `db_${dbName}`;
        const savedData = world.getDynamicProperty(dynamicPropertyKey);
        
        if (savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                for (const [key, value] of Object.entries(parsedData)) {
                    db.set(key, value);
                }
                console.log(`Transferred data for ${dbName}`);
                world.setDynamicProperty(dynamicPropertyKey, undefined);
            } catch (error) {
                console.error(`Failed to transfer data for ${dbName}:`, error);
            }
        }
    });
    
    // Return the database instance immediately
    return db;
}

// Create database instances
export const AdminDB = createDatabase("AdminDB");
export const WarpsDB = createDatabase("WarpsDB");
export const ClaimedRegionsDB = createDatabase("ClaimedRegionsDB");
export const playerRegionsDB = createDatabase("PlayerRegionsDB");
export const playerStatsDB = createDatabase("playerStatsDB");

// Export Database class for compatibility
import { Database } from "./DB.js";
export { Database };

// Safe database operation wrapper
export function safeDBOperation(db, operation, key, value = null) {
    try {
        if (operation === 'get') return db.get(key);
        if (operation === 'set') return db.set(key, value);
        if (operation === 'delete') return db.delete(key);
        if (operation === 'keys') return Array.from(db.keys());
        if (operation === 'values') return Array.from(db.values());
        if (operation === 'has') return db.has(key);
    } catch (error) {
        console.error(`Database operation failed: ${operation}`, error);
        return null;
    }
}