import { world, system, ItemStack } from "@minecraft/server";
import { Database } from "./DB";

const generatorDB = new Database("item_generators");
const GENERATOR_BLOCK = 'minecraft:bedrock';
const COOLDOWN_TICKS = 60;
const ITEM_SPAWN_HEIGHT_OFFSET = 1;
const OWNER_TAG = "Owner";
const ACTIVATION_RADIUS = 20;
const LOADER_INTERVAL = 400;

const generators = new Map();
const activeChunks = new Map();

function positionToKey(position, dimensionId) {
    return `${dimensionId},${position.x},${position.y},${position.z}`;
}

function chunkKeyFromPosition(position, dimensionId) {
    const chunkX = Math.floor(position.x / 16);
    const chunkZ = Math.floor(position.z / 16);
    return `${dimensionId},${chunkX},${chunkZ}`;
}

function saveGenerator(key, data) {
    generatorDB.write(key, data);
}

function updateActiveGenerators() {
    const players = world.getAllPlayers();
    const nowActiveChunks = new Map();

    players.forEach(player => {
        const pos = player.location;
        const dimensionId = player.dimension.id;
        const chunkKey = chunkKeyFromPosition(pos, dimensionId);
        nowActiveChunks.set(chunkKey, true);
    });

    // Unload distant generators
    generators.forEach((gen, key) => {
        const chunkKey = chunkKeyFromPosition(gen.position, gen.dimensionId);
        if (!nowActiveChunks.has(chunkKey)) {
            saveGenerator(key, gen);
            generators.delete(key);
        }
    });

    // Load nearby generators
    const allKeys = generatorDB.keys();
    nowActiveChunks.forEach((_, chunkKey) => {
        const [dimensionId] = chunkKey.split(',');
        
        allKeys.filter(key => key.startsWith(`${dimensionId},`)).forEach(key => {
            if (generators.has(key)) return;
            
            const coords = key.split(',').slice(1).map(Number);
            const genPosition = {x: coords[0], y: coords[1], z: coords[2]};
            const genChunkKey = chunkKeyFromPosition(genPosition, dimensionId);
            
            if (genChunkKey === chunkKey) {
                try {
                    const data = generatorDB.read(key);
                    if (data) generators.set(key, data);
                } catch (e) {
                    console.warn("Failed to load nearby generator:", key, e);
                }
            }
        });
    });
}

world.afterEvents.playerSpawn.subscribe(event => {
    if (event.initialSpawn) {
        updateActiveGenerators();
    }
});

world.afterEvents.playerPlaceBlock.subscribe(event => {
    const block = event.block;
    if (block.typeId !== GENERATOR_BLOCK) return;
    
    const position = block.location;
    const dimensionId = block.dimension.id;
    const key = positionToKey(position, dimensionId);
    
    const generatorData = {
        position: { x: position.x, y: position.y, z: position.z },
        dimensionId: dimensionId,
        lastSpawn: 0,
        generatedItem: null,
        isActive: true,
        owner: event.player.id
    };
    
    generators.set(key, generatorData);
    saveGenerator(key, generatorData);
});

world.afterEvents.playerBreakBlock.subscribe(event => {
    const block = event.block;
    if (block.typeId !== GENERATOR_BLOCK) return;
    
    const key = positionToKey(block.location, block.dimension.id);
    generatorDB.delete(key);
    generators.delete(key);
});

world.beforeEvents.playerInteractWithBlock.subscribe(event => {
    const { block, player, itemStack } = event;
    if (block.typeId !== GENERATOR_BLOCK) return;
    
    const key = positionToKey(block.location, block.dimension.id);
    let generator = generators.get(key);
    
    if (!generator) {
        try {
            generator = generatorDB.read(key);
            if (!generator) return;
            generators.set(key, generator);
        } catch {
            return;
        }
    }
    
    const hasOwnerPermission = player.hasTag(OWNER_TAG) || player.id === generator.owner;
    
    if (!itemStack) {
        event.cancel = true;
        if (!hasOwnerPermission) {
            player.sendMessage("");
            return;
        }
        generator.isActive = !generator.isActive;
        player.sendMessage(`§aGenerator ${generator.isActive ? 'activated' : 'deactivated'}`);
        saveGenerator(key, generator);
        return;
    }
    
    if (!player.isSneaking || !hasOwnerPermission) return;
    
    try {
        new ItemStack(itemStack.typeId, 1);
    } catch (e) {
        player.sendMessage(`§cInvalid item type: ${itemStack.typeId}`);
        return;
    }
    
    event.cancel = true;
    generator.generatedItem = itemStack.typeId;
    player.sendMessage(`§aGenerator configured to produce: ${itemStack.typeId}`);
    saveGenerator(key, generator);
});

system.runInterval(() => {
    const currentTime = system.currentTick;
    
    generators.forEach((generator, key) => {
        if (!generator.generatedItem || !generator.isActive) return;
        
        try {
            const dimension = world.getDimension(generator.dimensionId);
            const block = dimension.getBlock(generator.position);
            if (!block || block.typeId !== GENERATOR_BLOCK) {
                generatorDB.delete(key);
                generators.delete(key);
                return;
            }
            
            const itemPos = {
                x: generator.position.x + 0.5,
                y: generator.position.y + ITEM_SPAWN_HEIGHT_OFFSET,
                z: generator.position.z + 0.5
            };
            
            const items = dimension.getEntities({
                location: itemPos,
                maxDistance: 0.5,
                type: "minecraft:item"
            });
            
            if (items.length === 0 && currentTime - generator.lastSpawn >= COOLDOWN_TICKS) {
                const itemStack = new ItemStack(generator.generatedItem, 1);
                dimension.spawnItem(itemStack, itemPos);
                generator.lastSpawn = currentTime;
                saveGenerator(key, generator);
            }
        } catch (e) {
            console.warn(`Generator error at ${key}:`, e);
        }
    });
}, 5);

system.runInterval(updateActiveGenerators, LOADER_INTERVAL);

system.runInterval(() => {
    generators.forEach((gen, key) => saveGenerator(key, gen));
}, 6000);