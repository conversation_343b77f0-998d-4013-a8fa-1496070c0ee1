import { world, system, Player, WorldAfterEvents } from "@minecraft/server";
import { AdminDB, WarpsDB, ClaimedRegionsDB, playerRegionsDB } from "./core/database.js";
import { getPlayerRole } from "./core/utilities.js";
import { mainMenu } from "./ui/mainMenu.js";
import { protectedAccess, adminMenu } from "./ui/adminMenu.js";
import { generateRegionParticles, findApplicableRegion, isPlayerAllowedInRegion, restrictPlayerMovement, applyInteractionRestrictions, isInteractiveOrVariant, getAllRegionsAtLocation } from "./systems/particleSystem.js";
import { warpsMenu } from "./management/warpManagement.js";
//import './systems/playerTransfer.js';
import { regionRequestMenu } from "./management/regionRequests.js";

// Debug world object
console.log("world:", world);
console.log("world.afterEvents:", world?.afterEvents);
if (!world?.afterEvents) {
    console.error("world.afterEvents is undefined. Check @minecraft/server module.");
}

// Databases
export { AdminDB, WarpsDB, ClaimedRegionsDB, playerRegionsDB };

// Core Constants
const playerCurrentRegion = new Map();

// Listen for chat commands
world.beforeEvents.chatSend.subscribe((event) => {
    const player = event.sender;
    const message = event.message.trim();

    // Check for -claim command
    if (message === "-claim") {
        event.cancel = true; // Prevent the message from appearing in chat
        
        // Add a short delay before showing the form
        system.runTimeout(() => {
            regionRequestMenu(player);
        }, 30);
    }
    
    // Handle other chat commands
});

// UI & Menu Interactions
system.runTimeout(() => {
    if (!world?.afterEvents) {
        console.error("world.afterEvents is still undefined after delay.");
        return;
    }

    world.afterEvents.itemUse.subscribe(data => {
        const player = data.source;
        if (data.itemStack?.typeId === "minecraft:compass") {
            const role = getPlayerRole(player);
            if (role) {
                mainMenu(player);
            }
        }
    });

    world.afterEvents.entityHitEntity.subscribe((data) => {
        const { hitEntity: entity, damagingEntity } = data;
        if (!entity || !damagingEntity || !(damagingEntity instanceof Player)) return;
        const inventory = damagingEntity.getComponent("minecraft:inventory");
        if (!inventory) return;
        const heldItem = inventory.container.getItem(damagingEntity.selectedSlotIndex);
        if (heldItem?.typeId === "minecraft:compass" && damagingEntity.isSneaking) {
            if (damagingEntity.hasTag("Admin") || damagingEntity.hasTag("Owner") || damagingEntity.hasTag("Ravensthorpe")) {
                try {
                    entity.nameTag = ".";
                    damagingEntity.sendMessage(`§aName tag applied to entity.`);
                } catch (error) {
                    damagingEntity.sendMessage(`§cFailed to apply name tag: ${error.message}`);
                }
            }
        }
    });

    // Region Restrictions
    world.beforeEvents.playerBreakBlock.subscribe((event) => {
        try {
            const player = event.player;
            const block = event.block;
            if (!player || !block) return;
            const dimensionId = player.dimension.id;
            if (dimensionId === 'minecraft:nether' || dimensionId === 'minecraft:the_end') return;
            const region = findApplicableRegion(block.location, dimensionId);
            if (region?.protections?.blockBreak && !isPlayerAllowedInRegion(player, region)) {
                event.cancel = true;
                player.onScreenDisplay.setActionBar("§cYou cannot break blocks in this protected area.");
                restrictPlayerMovement(player, region.owner);
            }
        } catch (error) {
            console.error("Error in playerBreakBlock event handler:", error.message, error.stack);
        }
    });

    world.beforeEvents.playerPlaceBlock.subscribe((event) => {
        try {
            const player = event.player;
            const block = event.block;
            if (!player || !block) return;
            const dimensionId = player.dimension.id;
            if (dimensionId === 'minecraft:nether' || dimensionId === 'minecraft:the_end') return;
            const region = findApplicableRegion(block.location, dimensionId);
            if (region?.protections?.blockPlace && !isPlayerAllowedInRegion(player, region)) {
                event.cancel = true;
                player.onScreenDisplay.setActionBar("§cYou cannot place blocks in this protected area.");
                restrictPlayerMovement(player, region.owner);
            }
        } catch (error) {
            console.error("Error in playerPlaceBlock event handler:", error.message, error.stack);
        }
    });

    world.beforeEvents.playerInteractWithBlock.subscribe(async (event) => {
        try {
            const { source: player, block } = event;
            if (!(player instanceof Player) || !block) return;

            const dimensionId = player.dimension.id;
            if (
                dimensionId === "minecraft:nether" ||
                dimensionId === "minecraft:the_end"
            ) return;

            const region = await findApplicableRegion(block.location, dimensionId);
            if (
                region?.protections?.interaction &&
                !isPlayerAllowedInRegion(player, region)
            ) {
                const blockPerm = block.permutation;
                if (isInteractiveOrVariant(block) || blockPerm.tags?.includes("minecraft:interactive")) {
                    event.cancel = true;
                    player.onScreenDisplay.setActionBar("§cYou cannot interact here.");
                    applyInteractionRestrictions(player);
                }
            }
        } catch (error) {
            console.error("Interaction handler error:", error.message, error.stack);
        }
    });

    // Region Enter/Exit Notifications
    world.afterEvents.playerSpawn.subscribe((event) => {
        try {
            const player = event.player;
            const dimensionId = player.dimension.id;
            const location = player.location;
            
            // Get all regions at this location
            const regionsAtLocation = getAllRegionsAtLocation(location, dimensionId);
            
            if (regionsAtLocation.length > 0) {
                // Sort regions by size (smallest first) to prioritize nested regions
                regionsAtLocation.sort((a, b) => {
                    const aSize = getRegionSize(a);
                    const bSize = getRegionSize(b);
                    return aSize - bSize;
                });
                
                // Get the smallest region (most specific)
                const currentRegion = regionsAtLocation[0];
                
                player.sendMessage(`§aNow Entering: ${currentRegion.name}`);
                playerCurrentRegion.set(player.name, currentRegion.name);
            } else {
                playerCurrentRegion.delete(player.name);
            }
        } catch (error) {
            console.error("Error in playerSpawn event handler:", error.message, error.stack);
        }
    });

    // Periodic Region Check
    system.runInterval(() => {
        try {
            const players = Array.from(world.getPlayers());
            players.forEach((player) => {
                const dimensionId = player.dimension.id;
                const location = player.location;
                
                // Get all regions at this location
                const regionsAtLocation = getAllRegionsAtLocation(location, dimensionId);
                
                if (regionsAtLocation.length > 0) {
                    // Sort regions by size (smallest first) to prioritize nested regions
                    regionsAtLocation.sort((a, b) => {
                        const aSize = getRegionSize(a);
                        const bSize = getRegionSize(b);
                        return aSize - bSize;
                    });
                    
                    // Get the smallest region (most specific)
                    const currentRegion = regionsAtLocation[0];
                    
                    if (playerCurrentRegion.get(player.name) !== currentRegion.name) {
                        player.sendMessage(`§aNow Entering: ${currentRegion.name}`);
                        playerCurrentRegion.set(player.name, currentRegion.name);
                    }
                } else {
                    if (playerCurrentRegion.get(player.name)) {
                        player.sendMessage(`§aNow Entering The Wilderness`);
                        playerCurrentRegion.delete(player.name);
                    }
                }
            });
        } catch (error) {
            console.error("Error in periodic region check:", error.message, error.stack);
        }
    }, 20);

    // Helper function to calculate region size
    function getRegionSize(region) {
        const width = Math.abs(region.corner1.x - region.corner2.x);
        const length = Math.abs(region.corner1.z - region.corner2.z);
        return width * length;
    }

    // Initialize Particle System
    generateRegionParticles();
});

    // Import other scripts (unchanged)
    import "./systems/creeperProtection.js";
    import "./ui/modMenu.js";
    import "./systems/spawnerControl.js";
   // import "./trims/functions.js";
  //  import "./trims/shop.js";
  //  import "./trims/index.js";
    import "./testCommandBlocks.js";
    import "./witherArena.js";
    import "./moneyTransfer.js";
    //import "./PvP.js";
    //import "./smalls.js";
    import "./codeSystem.js";
    import "./coinSystem.js";
    import "./dono.js";
    import "./stats.js";
    import "./gens.js";
    import "./blockCleaner.js";
  


