// playerStats.js - Complete script with Owner tag verification and tick-based confirmation
import { world, Player, GameMode, system } from "@minecraft/server";
import { playerStatsDB } from "../core/database.js";

// Default stats structure
const DEFAULT_STATS = {
    kills: 0,
    deaths: 0,
    blocksPlaced: 0,
    blocksBroken: 0,
    kdRatio: 0
};

// Track confirmation states with tick timestamps
const clearConfirmations = new Map();

// Helper function to check Owner tag
function isOwner(player) {
    return player.hasTag("Owner");
}

// Helper function to get or initialize player stats
function getPlayerStatsData(playerName) {
    const name = playerName.toLowerCase();
    const stats = playerStatsDB.get(name);
    return stats ? {...stats} : {...DEFAULT_STATS};
}

// Initialize stat tracking on join
world.afterEvents.playerJoin.subscribe((event) => {
    try {
        const player = event.player;
        if (!player?.name) return;
        
        const name = player.name.toLowerCase();
        if (!playerStatsDB.has(name)) {
            playerStatsDB.set(name, {...DEFAULT_STATS});
        }
    } catch (e) {
        console.error("PlayerJoin Error:", e);
    }
});

// Track PvP kills and deaths
world.afterEvents.entityDie.subscribe((event) => {
    try {
        const { damageSource, deadEntity } = event;
        const killer = getKillingPlayer(damageSource);
        const victim = deadEntity;
        
        if (victim instanceof Player && victim.name && killer) {
            const victimName = victim.name.toLowerCase();
            const victimData = getPlayerStatsData(victimName);
            victimData.deaths++;
            victimData.kdRatio = victimData.kills > 0 
                ? (victimData.kills / victimData.deaths).toFixed(2) 
                : 0;
            playerStatsDB.set(victimName, victimData);
        }

        if (killer instanceof Player && killer.name && 
            deadEntity instanceof Player && deadEntity.name) {
            updateKillStats(killer);
        }
    } catch (e) {
        console.error("EntityDie Error:", e);
    }
});

function getKillingPlayer(damageSource) {
    if (!damageSource) return null;
    
    if (damageSource.damagingEntity instanceof Player) {
        return damageSource.damagingEntity;
    }
    
    if (damageSource.type === "projectile" && 
        damageSource.damagingEntity?.source instanceof Player) {
        return damageSource.damagingEntity.source;
    }
    
    return null;
}

function updateKillStats(player) {
    try {
        const name = player.name.toLowerCase();
        const data = getPlayerStatsData(name);
        data.kills++;
        data.kdRatio = data.deaths > 0 
            ? (data.kills / data.deaths).toFixed(2) 
            : data.kills;
        playerStatsDB.set(name, data);
    } catch (e) {
        console.error("KillStats Error:", e);
    }
}

// Track blocks placed
world.afterEvents.playerPlaceBlock.subscribe((event) => {
    try {
        const player = event.player;
        if (!player?.name || player.gameMode === GameMode.creative) return;
        
        const name = player.name.toLowerCase();
        const data = getPlayerStatsData(name);
        data.blocksPlaced++;
        playerStatsDB.set(name, data);
    } catch (e) {
        console.error("PlaceBlock Error:", e);
    }
});

// Track blocks broken
world.beforeEvents.playerBreakBlock.subscribe((event) => {
    try {
        const { player } = event;
        if (!player?.name || player.gameMode === GameMode.creative) return;

        const name = player.name.toLowerCase();
        const data = getPlayerStatsData(name);
        data.blocksBroken++;
        playerStatsDB.set(name, data);
    } catch (e) {
        console.error("BreakBlock Error:", e);
    }
});

// Command to view stats
export function getPlayerStats(player) {
    try {
        if (!player?.name) return "§cError: Invalid player";
        
        const name = player.name.toLowerCase();
        const stats = getPlayerStatsData(name);
        const ratio = stats.deaths === 0 ? stats.kills : (stats.kills / stats.deaths).toFixed(2);
        
        return `§aPlayer Stats for ${player.name}:
§fPvP Kills: §e${stats.kills}
§fPvP Deaths: §e${stats.deaths}
§fK/D Ratio: §e${ratio}
§fBlocks Placed: §e${stats.blocksPlaced}
§fBlocks Broken: §e${stats.blocksBroken}`;
    } catch (e) {
        console.error("GetStats Error:", e);
        return "§cError loading stats";
    }
}

// Leaderboard function
export function getLeaderboardStats() {
    try {
        const allStats = Array.from(playerStatsDB.entries()).map(([key, value]) => ({ 
            key, 
            value: {
                ...value,
                kdRatio: typeof value.kdRatio === 'number' ? value.kdRatio : parseFloat(value.kdRatio) || 0
            }
        }));
        
        const sortByKills = (a, b) => b.value.kills - a.value.kills;
        const sortByDeaths = (a, b) => b.value.deaths - a.value.deaths;
        const sortByBlocksPlaced = (a, b) => b.value.blocksPlaced - a.value.blocksPlaced;
        const sortByBlocksBroken = (a, b) => b.value.blocksBroken - a.value.blocksBroken;
        const sortByKDRatio = (a, b) => b.value.kdRatio - a.value.kdRatio;
        
        const getTop5 = (data, sortFn, stat, allowZero = false) => {
            return data
                .filter(entry => allowZero ? true : entry.value[stat] > 0)
                .sort(sortFn)
                .slice(0, 5);
        };

        const topKillers = getTop5(allStats, sortByKills, 'kills');
        const topDeaths = getTop5(allStats, sortByDeaths, 'deaths');
        const topBuilders = getTop5(allStats, sortByBlocksPlaced, 'blocksPlaced');
        const topMiners = getTop5(allStats, sortByBlocksBroken, 'blocksBroken');
        const topKDRatios = getTop5(allStats, sortByKDRatio, 'kdRatio', true);
        
        const formatLeaderboard = (title, entries, statKey, isRatio = false) => {
            if (entries.length === 0) return `§6§l${title}§r\n§7No data available\n`;
            
            let text = `§6§l${title}§r\n`;
            entries.forEach((entry, index) => {
                const playerName = entry.key;
                let statValue = entry.value[statKey];
                if (isRatio && statValue === Infinity) statValue = "∞";
                const color = index === 0 ? '6' : index === 1 ? 'e' : index === 2 ? 'f' : '7';
                text += `§${color}${index + 1}. §f${playerName}: §a${statValue}\n`;
            });
            return text;
        };

        return `§l§a----- SERVER LEADERBOARDS -----§r\n\n` +
               `${formatLeaderboard("TOP PvP KILLERS", topKillers, "kills")}\n` +
               `${formatLeaderboard("TOP K/D RATIO", topKDRatios, "kdRatio", true)}\n` +
               `${formatLeaderboard("TOP DEATHS", topDeaths, "deaths")}\n` +
               `${formatLeaderboard("TOP BUILDERS", topBuilders, "blocksPlaced")}\n` +
               `${formatLeaderboard("TOP MINERS", topMiners, "blocksBroken")}`;
    } catch (e) {
        console.error("Leaderboard Error:", e);
        return "§cError loading leaderboard data";
    }
}

// Handle -statclear command
world.beforeEvents.chatSend.subscribe((event) => {
    const message = event.message.trim();
    const player = event.sender;
    
    if (message === "-statclear") {
        event.cancel = true;
        
        if (!isOwner(player)) {
            return player.sendMessage("§cError: You must have the 'Owner' tag to use this command!");
        }

        const playerId = player.id;
        const currentTick = system.currentTick;
        
        if (!clearConfirmations.has(playerId)) {
            // Set expiration for 30 seconds later (600 ticks)
            clearConfirmations.set(playerId, currentTick + 600);
            
            // Set up periodic check
            const intervalId = system.runInterval(() => {
                if (clearConfirmations.has(playerId)) {
                    if (system.currentTick >= clearConfirmations.get(playerId)) {
                        clearConfirmations.delete(playerId);
                        system.clearRun(intervalId);
                    }
                } else {
                    system.clearRun(intervalId);
                }
            }, 20); // Check every second
            
            return player.sendMessage("§6WARNING: This will reset ALL player stats!\nType §c-statclear §6again within 30 seconds to confirm.");
        }
        
        // Confirmation received
        clearConfirmations.delete(playerId);
        playerStatsDB.clear();
        
        // Reinitialize for online players
        for (const onlinePlayer of world.getPlayers()) {
            if (onlinePlayer?.name) {
                const name = onlinePlayer.name.toLowerCase();
                playerStatsDB.set(name, {...DEFAULT_STATS});
            }
        }
        
        player.sendMessage("§aAll player stats and leaderboards have been reset!");
        console.warn(`Player stats reset by ${player.name} (Owner)`);
    }
});