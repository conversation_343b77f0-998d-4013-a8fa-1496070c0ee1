import { ModalFormData, ActionFormData } from "@minecraft/server-ui";
import { safeDBOperation, ClaimedRegionsDB } from "../core/database.js";
import { mainMenu } from "../ui/mainMenu.js";

export function areaManagementMenu(player) {
    const areaForm = new ActionFormData()
        .title("§3-=- §uArea Management §3-=-")
        .body("Select an option:")
        .button("§uCreate Region §r\n§7Click to Create")
        .button("§uDelete Region §r\n§7Click to Delete")
        .button("§uEdit Region §r\n§7Click to Edit")
        .button("§uCheck Region Proximity §r\n§7Click to Check")
        .button("§cBack §r\n§7Click to go Back");
    areaForm.show(player).then(r => {
        if (r.canceled) {
            mainMenu(player);
            return;
        }
        if (r.selection === 0) {
            createRegion(player);
        } else if (r.selection === 1) {
            deleteRegion(player);
        } else if (r.selection === 2) {
            editRegion(player);
        } else if (r.selection === 3) {
            checkRegionProximity(player);
        } else {
            mainMenu(player);
        }
    });
}

function createRegion(player) {
    const { x, y, z } = player.location;
    const defaultX = isNaN(x) ? "0" : Math.floor(x).toString();
    const defaultY = isNaN(y) ? "64" : Math.floor(y).toString();
    const defaultZ = isNaN(z) ? "0" : Math.floor(z).toString();

    // Get all existing regions for parent selection
    const allRegions = safeDBOperation(ClaimedRegionsDB, 'keys') || [];
    const parentOptions = ["None (Create Parent Region)", ...allRegions];

    try {
        new ModalFormData()
            .title("§3-=- §uCreate Region §3-=-")
            .dropdown("Region Type", ["Parent Region", "Child Region"])
            .dropdown("Parent Region", parentOptions, { defaultValue: 0 })
            .textField("Region Name", "Enter region name", { defaultValue: "" })
            .textField("Corner 1 X", "X coordinate", { defaultValue: defaultX })
            .textField("Corner 1 Y", "Y coordinate", { defaultValue: defaultY })
            .textField("Corner 1 Z", "Z coordinate", { defaultValue: defaultZ })
            .textField("Corner 2 X", "X coordinate", { defaultValue: defaultX })
            .textField("Corner 2 Y", "Y coordinate", { defaultValue: defaultY })
            .textField("Corner 2 Z", "Z coordinate", { defaultValue: defaultZ })
            .toggle("Block Break Protection", { defaultValue: true })
            .toggle("Block Place Protection", { defaultValue: true })
            .toggle("Interaction Protection", { defaultValue: true })
            .toggle("Show Particles", { defaultValue: true })
            .show(player)
            .then(response => {
                if (response.canceled) {
                    areaManagementMenu(player);
                    return;
                }
                
                const [regionType, parentSelection, name, x1, y1, z1, x2, y2, z2, breakProt, placeProt, interactProt, particles] = response.formValues;
                const isChildRegion = regionType === 1; // 1 = Child Region
                const parentRegion = isChildRegion && parentSelection > 0 ? parentOptions[parentSelection] : null;
                
                if (!name) {
                    player.sendMessage("§cRegion name cannot be empty.");
                    createRegion(player);
                    return;
                }
                
                if (safeDBOperation(ClaimedRegionsDB, 'has', name)) {
                    player.sendMessage("§cA region with this name already exists.");
                    createRegion(player);
                    return;
                }
                
                if (isChildRegion && !parentRegion) {
                    player.sendMessage("§cYou must select a parent region for a child region.");
                    createRegion(player);
                    return;
                }
                
                const corner1 = { x: parseFloat(x1), y: parseFloat(y1), z: parseFloat(z1) };
                const corner2 = { x: parseFloat(x2), y: parseFloat(y2), z: parseFloat(z2) };
                
                if (isNaN(corner1.x) || isNaN(corner1.y) || isNaN(corner1.z) || isNaN(corner2.x) || isNaN(corner2.y) || isNaN(corner2.z)) {
                    player.sendMessage("§cInvalid coordinates. Please enter valid numbers.");
                    createRegion(player);
                    return;
                }
                
                // If it's a child region, verify it's within the parent region
                if (isChildRegion && parentRegion) {
                    const parentData = safeDBOperation(ClaimedRegionsDB, 'get', parentRegion);
                    if (!isRegionWithinParent(corner1, corner2, parentData)) {
                        player.sendMessage("§cChild region must be completely within the parent region.");
                        createRegion(player);
                        return;
                    }
                }
                
                safeDBOperation(ClaimedRegionsDB, 'set', name, {
                    name,
                    corner1,
                    corner2,
                    dimension: player.dimension.id,
                    owner: player.name,
                    protections: {
                        blockBreak: breakProt,
                        blockPlace: placeProt,
                        interaction: interactProt
                    },
                    particlesEnabled: particles,
                    allowedPlayers: [],
                    allowedTags: [],
                    parent: parentRegion
                });
                
                player.sendMessage(`§aRegion ${name} created successfully${parentRegion ? ` as child of ${parentRegion}` : ''}.`);
                areaManagementMenu(player);
            })
            .catch(error => {
                console.error("Error displaying create region form:", error);
                player.sendMessage("§cFailed to create region. Please try again.");
                areaManagementMenu(player);
            });
    } catch (error) {
        console.error("Error creating region form:", error);
        player.sendMessage("§cFailed to open create region form. Please try again.");
        areaManagementMenu(player);
    }
}

function deleteRegion(player) {
    const allRegions = safeDBOperation(ClaimedRegionsDB, 'keys') || [];
    if (allRegions.length === 0) {
        player.sendMessage("§cThere are no regions to delete.");
        areaManagementMenu(player);
        return;
    }
    const regionForm = new ActionFormData()
        .title("§3-=- §uDelete Region §3-=-")
        .body("Select a region to delete:");
    allRegions.forEach(region => {
        const regionData = safeDBOperation(ClaimedRegionsDB, 'get', region);
        if (regionData.owner === player.name || player.hasTag("Admin") || player.hasTag("Owner")) {
            regionForm.button(`${region} (${regionData.dimension})`);
        }
    });
    regionForm.show(player).then(response => {
        if (response.canceled) {
            areaManagementMenu(player);
            return;
        }
        const regionName = allRegions[response.selection];
        safeDBOperation(ClaimedRegionsDB, 'delete', regionName);
        player.sendMessage(`§aRegion ${regionName} deleted successfully.`);
        areaManagementMenu(player);
    });
}

function editRegion(player) {
    const allRegions = safeDBOperation(ClaimedRegionsDB, 'keys') || [];
    if (allRegions.length === 0) {
        player.sendMessage("§cThere are no regions to edit.");
        areaManagementMenu(player);
        return;
    }
    const regionForm = new ActionFormData()
        .title("§3-=- §uEdit Region §3-=-")
        .body("Select a region to edit:");
    const validRegions = [];
    allRegions.forEach(region => {
        const regionData = safeDBOperation(ClaimedRegionsDB, 'get', region);
        if (regionData.owner === player.name || player.hasTag("Admin") || player.hasTag("Owner")) {
            regionForm.button(`${region} (${regionData.dimension})${regionData.parent ? ` - Child of ${regionData.parent}` : ''}`);
            validRegions.push(region);
        }
    });
    regionForm.show(player).then(response => {
        if (response.canceled) {
            areaManagementMenu(player);
            return;
        }
        const regionName = validRegions[response.selection];
        const regionData = safeDBOperation(ClaimedRegionsDB, 'get', regionName);
        if (!regionData || !regionData.protections) {
            console.error("Invalid region data for:", regionName, regionData);
            player.sendMessage("§cFailed to load region data. Please try again.");
            areaManagementMenu(player);
            return;
        }
        
        // Get potential parent regions (excluding current region and its children)
        const potentialParents = ["None"].concat(allRegions.filter(r => {
            if (r === regionName) return false;
            
            // Check if this region is a child of the current region
            const rData = safeDBOperation(ClaimedRegionsDB, 'get', r);
            return rData.parent !== regionName;
        }));
        
        const parentIndex = regionData.parent ? potentialParents.indexOf(regionData.parent) : 0;
        
        // Validate toggle values with fallbacks
        const blockBreak = typeof regionData.protections.blockBreak === 'boolean' ? regionData.protections.blockBreak : true;
        const blockPlace = typeof regionData.protections.blockPlace === 'boolean' ? regionData.protections.blockPlace : true;
        const interaction = typeof regionData.protections.interaction === 'boolean' ? regionData.protections.interaction : true;
        const particlesEnabled = typeof regionData.particlesEnabled === 'boolean' ? regionData.particlesEnabled : true;

        try {
            new ModalFormData()
                .title("§3-=- §uEdit Region §3-=-")
                .textField("Region Name", "Enter new region name", { defaultValue: regionName || "" })
                .dropdown("Parent Region", potentialParents, { defaultValue: parentIndex })
                .textField("Corner 1 X", "X coordinate", { defaultValue: regionData.corner1?.x?.toString() || "0" })
                .textField("Corner 1 Y", "Y coordinate", { defaultValue: regionData.corner1?.y?.toString() || "64" })
                .textField("Corner 1 Z", "Z coordinate", { defaultValue: regionData.corner1?.z?.toString() || "0" })
                .textField("Corner 2 X", "X coordinate", { defaultValue: regionData.corner2?.x?.toString() || "0" })
                .textField("Corner 2 Y", "Y coordinate", { defaultValue: regionData.corner2?.y?.toString() || "64" })
                .textField("Corner 2 Z", "Z coordinate", { defaultValue: regionData.corner2?.z?.toString() || "0" })
                .toggle("Block Break Protection", { defaultValue: blockBreak })
                .toggle("Block Place Protection", { defaultValue: blockPlace })
                .toggle("Interaction Protection", { defaultValue: interaction })
                .toggle("Show Particles", { defaultValue: particlesEnabled })
                .show(player)
                .then(response => {
                    if (response.canceled) {
                        areaManagementMenu(player);
                        return;
                    }
                    const [newName, parentSelection, x1, y1, z1, x2, y2, z2, breakProt, placeProt, interactProt, particles] = response.formValues;
                    const newParent = parentSelection > 0 ? potentialParents[parentSelection] : null;
                    
                    if (!newName) {
                        player.sendMessage("§cRegion name cannot be empty.");
                        editRegion(player);
                        return;
                    }
                    if (newName !== regionName && safeDBOperation(ClaimedRegionsDB, 'has', newName)) {
                        player.sendMessage("§cA region with this name already exists.");
                        editRegion(player);
                        return;
                    }
                    const corner1 = { x: parseFloat(x1), y: parseFloat(y1), z: parseFloat(z1) };
                    const corner2 = { x: parseFloat(x2), y: parseFloat(y2), z: parseFloat(z2) };
                    if (isNaN(corner1.x) || isNaN(corner1.y) || isNaN(corner1.z) || isNaN(corner2.x) || isNaN(corner2.y) || isNaN(corner2.z)) {
                        player.sendMessage("§cInvalid coordinates. Please enter valid numbers.");
                        editRegion(player);
                        return;
                    }
                    
                    // If parent is selected, verify region is within parent
                    if (newParent) {
                        const parentData = safeDBOperation(ClaimedRegionsDB, 'get', newParent);
                        if (!isRegionWithinParent(corner1, corner2, parentData)) {
                            player.sendMessage("§cRegion must be completely within the parent region.");
                            editRegion(player);
                            return;
                        }
                    }
                    
                    if (newName !== regionName) {
                        safeDBOperation(ClaimedRegionsDB, 'delete', regionName);
                    }
                    safeDBOperation(ClaimedRegionsDB, 'set', newName, {
                        name: newName,
                        corner1,
                        corner2,
                        dimension: regionData.dimension,
                        owner: regionData.owner,
                        protections: {
                            blockBreak: breakProt,
                            blockPlace: placeProt,
                            interaction: interactProt
                        },
                        particlesEnabled: particles,
                        allowedPlayers: regionData.allowedPlayers || [],
                        allowedTags: regionData.allowedTags || [],
                        parent: newParent
                    });
                    player.sendMessage(`§aRegion ${newName} updated successfully.`);
                    areaManagementMenu(player);
                })
                .catch(error => {
                    console.error("Error displaying edit region form:", error);
                    player.sendMessage("§cFailed to edit region. Please try again.");
                    areaManagementMenu(player);
                });
        } catch (error) {
            console.error("Error creating edit region form:", error);
            player.sendMessage("§cFailed to open edit region form. Please try again.");
            areaManagementMenu(player);
        }
    });
}

function checkRegionProximity(player) {
    const { x, y, z } = player.location;
    const defaultX = isNaN(x) ? "0" : Math.floor(x).toString();
    const defaultY = isNaN(y) ? "64" : Math.floor(y).toString();
    const defaultZ = isNaN(z) ? "0" : Math.floor(z).toString();

    try {
        new ModalFormData()
            .title("§3-=- §uCheck Region Proximity §3-=-")
            .textField("Corner 1 X", "X coordinate", { defaultValue: defaultX })
            .textField("Corner 1 Y", "Y coordinate", { defaultValue: defaultY })
            .textField("Corner 1 Z", "Z coordinate", { defaultValue: defaultZ })
            .textField("Corner 2 X", "X coordinate", { defaultValue: defaultX })
            .textField("Corner 2 Y", "Y coordinate", { defaultValue: defaultY })
            .textField("Corner 2 Z", "Z coordinate", { defaultValue: defaultZ })
            .show(player)
            .then(response => {
                if (response.canceled) {
                    areaManagementMenu(player);
                    return;
                }
                const [x1, y1, z1, x2, y2, z2] = response.formValues;
                const corner1 = { x: parseFloat(x1), y: parseFloat(y1), z: parseFloat(z1) };
                const corner2 = { x: parseFloat(x2), y: parseFloat(y2), z: parseFloat(z2) };
                if (isNaN(corner1.x) || isNaN(corner1.y) || isNaN(corner1.z) || isNaN(corner2.x) || isNaN(corner2.y) || isNaN(corner2.z)) {
                    player.sendMessage("§cInvalid coordinates. Please enter valid numbers.");
                    checkRegionProximity(player);
                    return;
                }
                const allRegions = safeDBOperation(ClaimedRegionsDB, 'keys')?.map(name => safeDBOperation(ClaimedRegionsDB, 'get', name)) || [];
                const nonParentRegions = allRegions.filter(region => region.parent !== null);
                let isTooClose = false;
                let closeRegionName = null;
                for (const region of nonParentRegions) {
                    if (region.dimension !== player.dimension.id) continue;
                    const regionMinX = Math.min(region.corner1.x, region.corner2.x);
                    const regionMaxX = Math.max(region.corner1.x, region.corner2.x);
                    const regionMinZ = Math.min(region.corner1.z, region.corner2.z);
                    const regionMaxZ = Math.max(region.corner1.z, region.corner2.z);
                    const newMinX = Math.min(corner1.x, corner2.x);
                    const newMaxX = Math.max(corner1.x, corner2.x);
                    const newMinZ = Math.min(corner1.z, corner2.z);
                    const newMaxZ = Math.max(corner1.z, corner2.z);
                    const closestX1 = Math.max(regionMinX, Math.min(newMinX, regionMaxX));
                    const closestX2 = Math.max(newMinX, Math.min(regionMinX, newMaxX));
                    const closestZ1 = Math.max(regionMinZ, Math.min(newMinZ, regionMaxZ));
                    const closestZ2 = Math.max(newMinZ, Math.min(regionMinZ, newMaxZ));
                    const distance = Math.sqrt(
                        Math.pow(closestX1 - closestX2, 2) +
                        Math.pow(closestZ1 - closestZ2, 2)
                    );
                    if (distance <= 150) {
                        isTooClose = true;
                        closeRegionName = region.name;
                        break;
                    }
                }
                if (isTooClose) {
                    player.sendMessage(`§cThe specified region is within 150 blocks of ${closeRegionName}.`);
                } else {
                    player.sendMessage(`§aThe specified region is not within 150 blocks of any non-parent region.`);
                }
                areaManagementMenu(player);
            })
            .catch(error => {
                console.error("Error displaying proximity check form:", error);
                player.sendMessage("§cFailed to check region proximity. Please try again.");
                areaManagementMenu(player);
            });
    } catch (error) {
        console.error("Error creating proximity check form:", error);
        player.sendMessage("§cFailed to open proximity check form. Please try again.");
        areaManagementMenu(player);
    }
}

// Helper function to check if a region is within a parent region
function isRegionWithinParent(corner1, corner2, parentRegion) {
    const childMinX = Math.min(corner1.x, corner2.x);
    const childMaxX = Math.max(corner1.x, corner2.x);
    const childMinZ = Math.min(corner1.z, corner2.z);
    const childMaxZ = Math.max(corner1.z, corner2.z);
    
    const parentMinX = Math.min(parentRegion.corner1.x, parentRegion.corner2.x);
    const parentMaxX = Math.max(parentRegion.corner1.x, parentRegion.corner2.x);
    const parentMinZ = Math.min(parentRegion.corner1.z, parentRegion.corner2.z);
    const parentMaxZ = Math.max(parentRegion.corner1.z, parentRegion.corner2.z);
    
    return childMinX >= parentMinX && childMaxX <= parentMaxX && 
           childMinZ >= parentMinZ && childMaxZ <= parentMaxZ;
}

