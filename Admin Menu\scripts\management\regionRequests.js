import { ModalFormData, ActionFormData } from "@minecraft/server-ui";
import { safeDBOperation, ClaimedRegionsDB, AdminDB } from "../core/database.js";
import { mainMenu } from "../ui/mainMenu.js";

// Database key for region requests
const REGION_REQUESTS_KEY = "regionRequests";

export function regionRequestMenu(player) {
    const requestForm = new ActionFormData()
        .title("§3-=- §uRegion Request §3-=-")
        .body("Request a region in the area where you're standing:")
        .button("§uRequest New Region §r\n§7Click to Request")
        .button("§uView My Requests §r\n§7Click to View")
        .button("§cBack §r\n§7Click to go Back");
    
    requestForm.show(player).then(r => {
        if (r.canceled) {
            mainMenu(player);
            return;
        }
        
        if (r.selection === 0) {
            createRegionRequest(player);
        } else if (r.selection === 1) {
            viewMyRequests(player);
        } else {
            mainMenu(player);
        }
    });
}

// Admin menu to manage region requests
export function adminRegionRequestMenu(player) {
    if (!player.hasTag("Admin") && !player.hasTag("Owner")) {
        player.sendMessage("§cYou don't have permission to access this menu.");
        return;
    }
    
    const requests = safeDBOperation(AdminDB, 'get', REGION_REQUESTS_KEY) || [];
    
    if (requests.length === 0) {
        player.sendMessage("§aThere are no pending region requests.");
        return;
    }
    
    const requestForm = new ActionFormData()
        .title("§3-=- §uPending Region Requests §3-=-")
        .body("Select a request to review:");
    
    requests.forEach(request => {
        requestForm.button(`§u${request.playerName}§r: ${request.regionName}\n§7${request.dimension}`);
    });
    
    requestForm.show(player).then(r => {
        if (r.canceled) return;
        
        reviewRegionRequest(player, requests[r.selection]);
    });
}

function createRegionRequest(player) {
    const { x, y, z } = player.location;
    const defaultX = isNaN(x) ? "0" : Math.floor(x).toString();
    const defaultY = isNaN(y) ? "64" : Math.floor(y).toString();
    const defaultZ = isNaN(z) ? "0" : Math.floor(z).toString();
    
    // Find parent regions at the player's current location
    const dimensionId = player.dimension.id;
    const allRegions = safeDBOperation(ClaimedRegionsDB, 'keys')
        ?.map(name => safeDBOperation(ClaimedRegionsDB, 'get', name))
        .filter(region => region?.dimension === dimensionId) || [];
    
    // Filter for parent regions that contain the player's position
    const parentRegions = allRegions.filter(region => {
        if (region.parent) return false; // Skip child regions
        
        const minX = Math.min(region.corner1.x, region.corner2.x);
        const maxX = Math.max(region.corner1.x, region.corner2.x);
        const minZ = Math.min(region.corner1.z, region.corner2.z);
        const maxZ = Math.max(region.corner1.z, region.corner2.z);
        
        return x >= minX && x <= maxX && z >= minZ && z <= maxZ;
    });
    
    if (parentRegions.length === 0) {
        player.sendMessage("§cYou must be standing in a parent region to request a child region.");
        regionRequestMenu(player);
        return;
    }
    
    const parentOptions = parentRegions.map(region => region.name);
    
    try {
        new ModalFormData()
            .title("§3-=- §uRequest Region §3-=-")
            .dropdown("Parent Region", parentOptions)
            .textField("Region Name", "Enter region name", { defaultValue: `${player.name}'s Region` })
            .textField("Corner 1 X", "X coordinate", { defaultValue: defaultX })
            .textField("Corner 1 Y", "Y coordinate", { defaultValue: defaultY })
            .textField("Corner 1 Z", "Z coordinate", { defaultValue: defaultZ })
            .textField("Corner 2 X", "X coordinate", { defaultValue: defaultX })
            .textField("Corner 2 Y", "Y coordinate", { defaultValue: defaultY })
            .textField("Corner 2 Z", "Z coordinate", { defaultValue: defaultZ })
            .textField("Reason", "Why do you need this region?", { defaultValue: "" })
            .show(player)
            .then(response => {
                if (response.canceled) {
                    regionRequestMenu(player);
                    return;
                }
                
                const [parentSelection, name, x1, y1, z1, x2, y2, z2, reason] = response.formValues;
                const parentRegion = parentOptions[parentSelection];
                
                if (!name) {
                    player.sendMessage("§cRegion name cannot be empty.");
                    createRegionRequest(player);
                    return;
                }
                
                if (safeDBOperation(ClaimedRegionsDB, 'has', name)) {
                    player.sendMessage("§cA region with this name already exists.");
                    createRegionRequest(player);
                    return;
                }
                
                const corner1 = { x: parseFloat(x1), y: parseFloat(y1), z: parseFloat(z1) };
                const corner2 = { x: parseFloat(x2), y: parseFloat(y2), z: parseFloat(z2) };
                
                if (isNaN(corner1.x) || isNaN(corner1.y) || isNaN(corner1.z) || isNaN(corner2.x) || isNaN(corner2.y) || isNaN(corner2.z)) {
                    player.sendMessage("§cInvalid coordinates. Please enter valid numbers.");
                    createRegionRequest(player);
                    return;
                }
                
                // Verify it's within the parent region
                const parentData = safeDBOperation(ClaimedRegionsDB, 'get', parentRegion);
                if (!isRegionWithinParent(corner1, corner2, parentData)) {
                    player.sendMessage("§cRequested region must be completely within the parent region.");
                    createRegionRequest(player);
                    return;
                }
                
                // Check proximity to other child regions
                if (!isRegionDistanceValid(corner1, corner2, player.dimension.id)) {
                    player.sendMessage("§cYour requested region is too close to another child region (must be at least 150 blocks away).");
                    createRegionRequest(player);
                    return;
                }
                
                // Create the request
                const request = {
                    id: Date.now().toString(),
                    playerName: player.name,
                    regionName: name,
                    corner1,
                    corner2,
                    dimension: player.dimension.id,
                    parentRegion,
                    reason,
                    timestamp: Date.now()
                };
                
                // Save the request
                const requests = safeDBOperation(AdminDB, 'get', REGION_REQUESTS_KEY) || [];
                requests.push(request);
                safeDBOperation(AdminDB, 'set', REGION_REQUESTS_KEY, requests);
                
                player.sendMessage("§aYour region request has been submitted for admin approval.");
                
                // Notify online admins
                const players = Array.from(world.getPlayers());
                players.forEach(p => {
                    if (p.hasTag("Admin") || p.hasTag("Owner")) {
                        p.sendMessage(`§e${player.name} has submitted a new region request: ${name}`);
                    }
                });
                
                regionRequestMenu(player);
            })
            .catch(error => {
                console.error("Error displaying region request form:", error);
                player.sendMessage("§cFailed to submit region request. Please try again.");
                regionRequestMenu(player);
            });
    } catch (error) {
        console.error("Error creating region request form:", error);
        player.sendMessage("§cFailed to open region request form. Please try again.");
        regionRequestMenu(player);
    }
}

function viewMyRequests(player) {
    const requests = safeDBOperation(AdminDB, 'get', REGION_REQUESTS_KEY) || [];
    const myRequests = requests.filter(req => req.playerName === player.name);
    
    if (myRequests.length === 0) {
        player.sendMessage("§aYou don't have any pending region requests.");
        regionRequestMenu(player);
        return;
    }
    
    const requestForm = new ActionFormData()
        .title("§3-=- §uMy Region Requests §3-=-")
        .body("Your pending requests:");
    
    myRequests.forEach(request => {
        const date = new Date(request.timestamp);
        requestForm.button(`§u${request.regionName}§r\n§7Submitted: ${date.toLocaleDateString()}`);
    });
    
    requestForm.show(player).then(r => {
        if (r.canceled) {
            regionRequestMenu(player);
            return;
        }
        
        const request = myRequests[r.selection];
        
        new ActionFormData()
            .title(`§3-=- §u${request.regionName} §3-=-`)
            .body(`Parent: ${request.parentRegion}\nDimension: ${request.dimension}\nCorner 1: ${request.corner1.x}, ${request.corner1.y}, ${request.corner1.z}\nCorner 2: ${request.corner2.x}, ${request.corner2.y}, ${request.corner2.z}\nReason: ${request.reason}\nSubmitted: ${new Date(request.timestamp).toLocaleString()}`)
            .button("§cCancel Request")
            .button("§aBack")
            .show(player).then(result => {
                if (result.canceled || result.selection === 1) {
                    viewMyRequests(player);
                    return;
                }
                
                // Cancel the request
                const allRequests = safeDBOperation(AdminDB, 'get', REGION_REQUESTS_KEY) || [];
                const updatedRequests = allRequests.filter(req => req.id !== request.id);
                safeDBOperation(AdminDB, 'set', REGION_REQUESTS_KEY, updatedRequests);
                
                player.sendMessage(`§aYour request for region "${request.regionName}" has been canceled.`);
                regionRequestMenu(player);
            });
    });
}

function reviewRegionRequest(player, request) {
    new ActionFormData()
        .title(`§3-=- §uReview Request: ${request.regionName} §3-=-`)
        .body(`Player: ${request.playerName}\nParent: ${request.parentRegion}\nDimension: ${request.dimension}\nCorner 1: ${request.corner1.x}, ${request.corner1.y}, ${request.corner1.z}\nCorner 2: ${request.corner2.x}, ${request.corner2.y}, ${request.corner2.z}\nReason: ${request.reason}\nSubmitted: ${new Date(request.timestamp).toLocaleString()}`)
        .button("§aApprove")
        .button("§cDeny")
        .button("§7Back")
        .show(player).then(result => {
            if (result.canceled || result.selection === 2) {
                adminRegionRequestMenu(player);
                return;
            }
            
            // Remove the request
            const requests = safeDBOperation(AdminDB, 'get', REGION_REQUESTS_KEY) || [];
            const updatedRequests = requests.filter(req => req.id !== request.id);
            safeDBOperation(AdminDB, 'set', REGION_REQUESTS_KEY, updatedRequests);
            
            if (result.selection === 0) {
                // Approve - create the region
                safeDBOperation(ClaimedRegionsDB, 'set', request.regionName, {
                    name: request.regionName,
                    corner1: request.corner1,
                    corner2: request.corner2,
                    dimension: request.dimension,
                    owner: request.playerName,
                    protections: {
                        blockBreak: true,
                        blockPlace: true,
                        interaction: true
                    },
                    particlesEnabled: true,
                    allowedPlayers: [],
                    allowedTags: [],
                    parent: request.parentRegion
                });
                
                player.sendMessage(`§aYou approved ${request.playerName}'s region request: ${request.regionName}`);
                
                // Notify the requester if they're online
                const requester = Array.from(world.getPlayers()).find(p => p.name === request.playerName);
                if (requester) {
                    requester.sendMessage(`§aYour region request for "${request.regionName}" has been approved!`);
                }
            } else {
                // Deny
                player.sendMessage(`§cYou denied ${request.playerName}'s region request: ${request.regionName}`);
                
                // Notify the requester if they're online
                const requester = Array.from(world.getPlayers()).find(p => p.name === request.playerName);
                if (requester) {
                    requester.sendMessage(`§cYour region request for "${request.regionName}" has been denied.`);
                }
            }
            
            adminRegionRequestMenu(player);
        });
}

// Helper function to check if a region is within a parent region
function isRegionWithinParent(corner1, corner2, parentRegion) {
    const childMinX = Math.min(corner1.x, corner2.x);
    const childMaxX = Math.max(corner1.x, corner2.x);
    const childMinZ = Math.min(corner1.z, corner2.z);
    const childMaxZ = Math.max(corner1.z, corner2.z);
    
    const parentMinX = Math.min(parentRegion.corner1.x, parentRegion.corner2.x);
    const parentMaxX = Math.max(parentRegion.corner1.x, parentRegion.corner2.x);
    const parentMinZ = Math.min(parentRegion.corner1.z, parentRegion.corner2.z);
    const parentMaxZ = Math.max(parentRegion.corner1.z, parentRegion.corner2.z);
    
    return childMinX >= parentMinX && childMaxX <= parentMaxX && 
           childMinZ >= parentMinZ && childMaxZ <= parentMaxZ;
}

// Helper function to check if a region is at least 150 blocks away from other child regions
function isRegionDistanceValid(corner1, corner2, dimensionId) {
    const newMinX = Math.min(corner1.x, corner2.x);
    const newMaxX = Math.max(corner1.x, corner2.x);
    const newMinZ = Math.min(corner1.z, corner2.z);
    const newMaxZ = Math.max(corner1.z, corner2.z);
    
    const allRegions = safeDBOperation(ClaimedRegionsDB, 'keys')
        ?.map(name => safeDBOperation(ClaimedRegionsDB, 'get', name))
        .filter(region => region?.dimension === dimensionId && region.parent !== null) || [];
    
    for (const region of allRegions) {
        const regionMinX = Math.min(region.corner1.x, region.corner2.x);
        const regionMaxX = Math.max(region.corner1.x, region.corner2.x);
        const regionMinZ = Math.min(region.corner1.z, region.corner2.z);
        const regionMaxZ = Math.max(region.corner1.z, region.corner2.z);
        
        // Calculate the closest points between the two regions
        const closestX1 = Math.max(regionMinX, Math.min(newMinX, regionMaxX));
        const closestX2 = Math.max(newMinX, Math.min(regionMinX, newMaxX));
        const closestZ1 = Math.max(regionMinZ, Math.min(newMinZ, regionMaxZ));
        const closestZ2 = Math.max(newMinZ, Math.min(regionMinZ, newMaxZ));
        
        const distance = Math.sqrt(
            Math.pow(closestX1 - closestX2, 2) +
            Math.pow(closestZ1 - closestZ2, 2)
        );
        
        if (distance <= 150) {
            return false;
        }
    }
    
    return true;
}