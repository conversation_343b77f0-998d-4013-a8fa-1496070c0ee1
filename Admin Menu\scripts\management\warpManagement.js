import { ModalFormData, ActionFormData } from "@minecraft/server-ui";
import { safeDBOperation, WarpsDB } from "../core/database.js";
import { world } from "@minecraft/server";
import { mainMenu } from "../ui/mainMenu.js";

export function warpsMenu(player) {
    const rawWarpData = safeDBOperation(WarpsDB, 'values') || [];
    console.log("Raw warp data:", rawWarpData);
    const categories = Array.from(new Set(rawWarpData.map(w => w?.category || 'Uncategorized').filter(c => typeof c === 'string')));
    console.log("Categories:", categories);
    const dropdownOptions = categories.length > 0
        ? [{ text: 'All' }, ...categories.map(c => ({ text: c }))]
        : [{ text: 'All' }];
    console.log("Dropdown options:", JSON.stringify(dropdownOptions));

    try {
        new ModalFormData()
            .title("§3-=- §uWarps §3-=-")
            .dropdown("Select Category", dropdownOptions)
            .show(player)
            .then(response => {
                if (response.canceled) {
                    mainMenu(player);
                    return;
                }
                const selectedCategory = response.formValues[0] === 0 ? null : categories[response.formValues[0] - 1];
                const allWarps = safeDBOperation(WarpsDB, 'keys')?.map(warpName => [warpName, safeDBOperation(WarpsDB, 'get', warpName)]) || [];
                const availableWarps = allWarps.filter(([_, warpData]) => {
                    return !selectedCategory || warpData?.category === selectedCategory;
                }).filter(([_, warpData]) => {
                    if (!warpData) return false;
                    return !warpData.requiredTag || player.hasTag(warpData.requiredTag);
                });

                if (availableWarps.length === 0) {
                    player.sendMessage('§cThere are no warps available to you in this category.');
                    warpsMenu(player);
                    return;
                }

                const warpForm = new ActionFormData()
                    .title("§3-=- §uAvailable Warps §3-=-")
                    .body("Select a warp to teleport to:");
                availableWarps.forEach(([warpName, warpData]) => {
                    warpForm.button(`${warpName} (${warpData.category || 'Uncategorized'})`);
                });
                warpForm.show(player).then(response => {
                    if (response.canceled) {
                        warpsMenu(player);
                    } else {
                        const [selectedWarpName, selectedWarpData] = availableWarps[response.selection];
                        const warpLocation = { x: selectedWarpData.x, y: selectedWarpData.y, z: selectedWarpData.z };
                        const dimension = world.getDimension(selectedWarpData.dimension);
                        player.teleport(warpLocation, {
                            dimension,
                            rotation: player.rotation
                        });
                        player.sendMessage(`§aTeleported to ${selectedWarpName} in the ${selectedWarpData.category || 'Uncategorized'} category.`);
                    }
                });
            })
            .catch(error => {
                console.error("Error displaying warps menu:", error);
                player.sendMessage("§cFailed to open warps menu. Please try again.");
                mainMenu(player);
            });
    } catch (error) {
        console.error("Error creating warps menu:", error);
        player.sendMessage("§cFailed to open warps menu. Please try again.");
        mainMenu(player);
    }
}

export function manageWarps(player) {
    const warpForm = new ActionFormData()
        .title("§3-=- §uManage Warps §3-=-")
        .body("Select an option:")
        .button("§uCreate Warp §r\n§7Click to Create")
        .button("§uDelete Warp §r\n§7Click to Delete")
        .button("§uEdit Warp §r\n§7Click to Edit")
        .button("§cBack §r\n§7Click to go Back");
    warpForm.show(player).then(r => {
        if (r.canceled) {
            mainMenu(player);
            return;
        }
        if (r.selection === 0) {
            createWarp(player);
        } else if (r.selection === 1) {
            deleteWarp(player);
        } else if (r.selection === 2) {
            editWarp(player);
        } else {
            mainMenu(player);
        }
    });
}

function createWarp(player) {
    new ModalFormData()
        .title("§3-=- §uCreate Warp §3-=-")
        .textField("Warp Name", "Enter warp name", { defaultValue: "" })
        .textField("Category", "Enter category (optional)", { defaultValue: "" })
        .textField("Required Tag", "Enter tag (optional)", { defaultValue: "" })
        .show(player)
        .then(response => {
            if (response.canceled) {
                manageWarps(player);
                return;
            }
            const [warpName, category, requiredTag] = response.formValues;
            if (!warpName) {
                player.sendMessage("§cWarp name cannot be empty.");
                createWarp(player);
                return;
            }
            if (safeDBOperation(WarpsDB, 'has', warpName)) {
                player.sendMessage("§cA warp with this name already exists.");
                createWarp(player);
                return;
            }
            const { x, y, z } = player.location;
            const dimension = player.dimension.id;
            safeDBOperation(WarpsDB, 'set', warpName, {
                x, y, z,
                dimension,
                category: category || null,
                requiredTag: requiredTag || null
            });
            player.sendMessage(`§aWarp ${warpName} created successfully.`);
            manageWarps(player);
        });
}

function deleteWarp(player) {
    const allWarps = safeDBOperation(WarpsDB, 'keys') || [];
    if (allWarps.length === 0) {
        player.sendMessage("§cThere are no warps to delete.");
        manageWarps(player);
        return;
    }
    const warpForm = new ActionFormData()
        .title("§3-=- §uDelete Warp §3-=-")
        .body("Select a warp to delete:");
    allWarps.forEach(warp => {
        const warpData = safeDBOperation(WarpsDB, 'get', warp);
        warpForm.button(`${warp} (${warpData.category || 'Uncategorized'})`);
    });
    warpForm.show(player).then(response => {
        if (response.canceled) {
            manageWarps(player);
            return;
        }
        const warpName = allWarps[response.selection];
        safeDBOperation(WarpsDB, 'delete', warpName);
        player.sendMessage(`§aWarp ${warpName} deleted successfully.`);
        manageWarps(player);
    });
}

function editWarp(player) {
    const allWarps = safeDBOperation(WarpsDB, 'keys') || [];
    if (allWarps.length === 0) {
        player.sendMessage("§cThere are no warps to edit.");
        manageWarps(player);
        return;
    }
    const warpForm = new ActionFormData()
        .title("§3-=- §uEdit Warp §3-=-")
        .body("Select a warp to edit:");
    allWarps.forEach(warp => {
        const warpData = safeDBOperation(WarpsDB, 'get', warp);
        warpForm.button(`${warp} (${warpData.category || 'Uncategorized'})`);
    });
    warpForm.show(player).then(response => {
        if (response.canceled) {
            manageWarps(player);
            return;
        }
        const warpName = allWarps[response.selection];
        const warpData = safeDBOperation(WarpsDB, 'get', warpName);
        new ModalFormData()
            .title("§3-=- §uEdit Warp §3-=-")
            .textField("Warp Name", "Enter new warp name", { defaultValue: warpName })
            .textField("Category", "Enter new category (optional)", { defaultValue: warpData.category || "" })
            .textField("Required Tag", "Enter new tag (optional)", { defaultValue: warpData.requiredTag || "" })
            .show(player)
            .then(response => {
                if (response.canceled) {
                    manageWarps(player);
                    return;
                }
                const [newWarpName, newCategory, newRequiredTag] = response.formValues;
                if (!newWarpName) {
                    player.sendMessage("§cWarp name cannot be empty.");
                    editWarp(player);
                    return;
                }
                if (newWarpName !== warpName && safeDBOperation(WarpsDB, 'has', newWarpName)) {
                    player.sendMessage("§cA warp with this name already exists.");
                    editWarp(player);
                    return;
                }
                safeDBOperation(WarpsDB, 'delete', warpName);
                safeDBOperation(WarpsDB, 'set', newWarpName, {
                    x: warpData.x,
                    y: warpData.y,
                    z: warpData.z,
                    dimension: warpData.dimension,
                    category: newCategory || null,
                    requiredTag: newRequiredTag || null
                });
                player.sendMessage(`§aWarp ${newWarpName} updated successfully.`);
                manageWarps(player);
            });
    });
}