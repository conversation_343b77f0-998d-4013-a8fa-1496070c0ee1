import { system, world, DimensionTypes } from '@minecraft/server';

/** Update interval (in ticks) at which TPS is recalculated. 20 ticks = 1 second. */
const interval = 20;

const types = {
    items: 0,
    players: 1,
    namedMobs: 2,   // Mobs with name tags
    unnamedMobs: 3, // Mobs without name tags
    tps: 4         // Ticks per second
};

const counters = [
    0,   // items
    0,   // players
    0,   // namedMobs
    0,   // unnamedMobs
    20   // tps (default)
];

/** Lock flag to prevent updates during counter synchronization */
let counting = false;

/** Safely synchronize counters with current world status */
function setCounters() {
    counting = true;
    
    // Reset counters
    counters[types.items] = 0;
    counters[types.namedMobs] = 0;
    counters[types.unnamedMobs] = 0;

    // Process all dimensions with error handling
    for (const dimensionInfo of DimensionTypes.getAll()) {
        try {
            const dimension = world.getDimension(dimensionInfo.typeId);
            
            // Count items
            try {
                counters[types.items] += dimension.getEntities({ type: "minecraft:item" }).length;
            } catch (e) {
                console.warn(`Failed to count items in ${dimensionInfo.typeId}: ${e}`);
            }
            
            // Count mobs (non-player, non-item entities)
            try {
                const entities = dimension.getEntities({ 
                    excludeTypes: ["minecraft:item", "minecraft:player"] 
                });
                
                for (const entity of entities) {
                    try {
                        if (entity.nameTag) {
                            counters[types.namedMobs]++;
                        } else {
                            counters[types.unnamedMobs]++;
                        }
                    } catch (e) {
                        console.warn(`Failed to process entity in ${dimensionInfo.typeId}: ${e}`);
                        continue;
                    }
                }
            } catch (e) {
                console.warn(`Failed to get mob entities in ${dimensionInfo.typeId}: ${e}`);
            }
        } catch (e) {
            console.warn(`Failed to access dimension ${dimensionInfo.typeId}: ${e}`);
        }
    }
    
    counting = false;
}

// Initial counter setup
try {
    counters[types.players] = world.getAllPlayers().length;
    setCounters();
} catch (e) {
    console.warn(`Initial setup failed: ${e}`);
}

// Update counters on entity spawn
world.afterEvents.entitySpawn.subscribe(e => {
    if (counting) return;
    
    try {
        const entity = e.entity;
        switch (entity.typeId) {
            case "minecraft:item":
                counters[types.items]++;
                break;
            case "minecraft:player":
                // Handled by playerJoin event
                break;
            default:
                try {
                    if (entity.nameTag) {
                        counters[types.namedMobs]++;
                    } else {
                        counters[types.unnamedMobs]++;
                    }
                } catch (e) {
                    console.warn(`Failed to check nameTag for spawned entity: ${e}`);
                }
        }
    } catch (e) {
        console.warn(`Failed to process entity spawn: ${e}`);
    }
});

// Update counters on entity removal
world.beforeEvents.entityRemove.subscribe(e => {
    if (counting) return;
    
    try {
        const entity = e.removedEntity;
        switch (entity.typeId) {
            case "minecraft:item":
                counters[types.items]--;
                break;
            case "minecraft:player":
                // Handled by playerLeave event
                break;
            default:
                try {
                    if (entity.nameTag) {
                        counters[types.namedMobs]--;
                    } else {
                        counters[types.unnamedMobs]--;
                    }
                } catch (e) {
                    console.warn(`Failed to check nameTag for removed entity: ${e}`);
                }
        }
    } catch (e) {
        console.warn(`Failed to process entity removal: ${e}`);
    }
});

// Player management events
world.afterEvents.playerJoin.subscribe(() => {
    try {
        counters[types.players]++;
    } catch (e) {
        console.warn(`Failed to increment player count: ${e}`);
    }
});

world.afterEvents.playerLeave.subscribe(() => {
    try {
        counters[types.players]--;
    } catch (e) {
        console.warn(`Failed to decrement player count: ${e}`);
    }
});

world.afterEvents.playerSpawn.subscribe(() => {
    try {
        setCounters(); // Resync counters when new areas load
    } catch (e) {
        console.warn(`Failed to resync counters on player spawn: ${e}`);
    }
});

// Chat command handler
world.beforeEvents.chatSend.subscribe((eventData) => {
    const { message, sender } = eventData;
    if (!sender) return;

    if (message.toLowerCase().trim() === "-stats") {
        eventData.cancel = true;

        try {
            // Measure ping
            const startTime = Date.now();
            try {
                sender.runCommand("say Checking ping...");
            } catch {}
            const ping = Date.now() - startTime;

            // Calculate mob totals
            const totalMobs = counters[types.namedMobs] + counters[types.unnamedMobs];
            
            // Display statistics
            sender.sendMessage(
                "§eServer statistics:\n" +
                `- Current TPS: ${counters[types.tps].toFixed(2)}\n` +
                `- Online Players: ${counters[types.players]}\n` +
                `- Total Mobs: ${totalMobs}\n` +
                `  - Named: ${counters[types.namedMobs]}\n` +
                `  - Unnamed: ${counters[types.unnamedMobs]}\n` +
                `- Dropped Items: ${counters[types.items]}\n` +
                `- Ping: ${ping} ms`
            );

            // Optional actionbar ping display
            try {
                sender.runCommand(`title @s actionbar "Ping: ${ping} ms"`);
            } catch {}
        } catch (e) {
            console.warn(`Failed to process stats command: ${e}`);
            try {
                sender.sendMessage("§cFailed to retrieve server statistics. Please try again.");
            } catch {}
        }
    }
});

// TPS calculation
let last_check = Date.now();
let last_tick = system.currentTick;

system.runInterval(() => {
    try {
        counters[types.tps] = 1000 * (system.currentTick - last_tick) / (Date.now() - last_check);
        last_check = Date.now();
        last_tick = system.currentTick;
    } catch (e) {
        console.warn(`TPS calculation failed: ${e}`);
        counters[types.tps] = 20; // Reset to default if calculation fails
    }
}, interval);