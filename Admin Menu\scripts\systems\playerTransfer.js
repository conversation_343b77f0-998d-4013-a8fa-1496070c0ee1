import { world, system, Player } from "@minecraft/server";
import { transferPlayer } from "@minecraft/server-admin";
import { ModalFormData, ActionFormData } from "@minecraft/server-ui";

// Configuration storage
const config = {
    isConfigured: false,
    transferOptions: [],
    autoTransferOnSpawn: null,
    adminMenuItem: "minecraft:dirt",
    knownPlayers: [],
    transferHistoryLimit: 100,
};

// Transfer statistics (will be loaded/saved via dynamic properties)
const transferStats = {
    totalTransfers: 0,
    byDestination: new Map(),
    startTime: 0,
    transferHistory: [],
};
const transferCooldowns = new Map();

function loadConfig() {
    const savedConfig = world.getDynamicProperty("transferConfig");
    if (savedConfig) {
        const parsed = JSON.parse(savedConfig);
        config.isConfigured = parsed.isConfigured || false;
        config.transferOptions = parsed.transferOptions || [];
        config.transferOptions.forEach(opt => {
            if (opt.requireConfirmation === undefined) opt.requireConfirmation = false;
            if (opt.accessType === undefined) opt.accessType = "none";
            if (!Array.isArray(opt.accessList)) opt.accessList = [];
            // Added: Ensure maintenanceAccessList exists and is an array
            if (!Array.isArray(opt.maintenanceAccessList)) opt.maintenanceAccessList = [];
            opt.accessList = opt.accessList.map(name => name.toLowerCase());
            opt.maintenanceAccessList = opt.maintenanceAccessList.map(name => name.toLowerCase());
            if (opt.triggerType) {
                opt.triggerType = opt.triggerType.toLowerCase().trim();
                if (!["hit", "interact", "both"].includes(opt.triggerType)) {
                    console.warn(`Invalid triggerType for destination ${opt.name}: ${opt.triggerType}. Defaulting to "hit".`);
                    opt.triggerType = "hit";
                }
            } else {
                console.warn(`Missing triggerType for destination ${opt.name}. Defaulting to "hit".`);
                opt.triggerType = "hit";
            }
            if (!opt.name || typeof opt.name !== "string") {
                console.warn(`Invalid or missing name for destination: ${JSON.stringify(opt)}. Setting to "Unknown".`);
                opt.name = "Unknown";
            }
            if (!opt.ip || typeof opt.ip !== "string") {
                console.warn(`Invalid or missing IP for destination ${opt.name}. Setting to "0.0.0.0".`);
                opt.ip = "0.0.0.0";
            }
            if (!opt.port || typeof opt.port !== "number" || opt.port < 0 || opt.port > 65535) {
                console.warn(`Invalid or missing port for destination ${opt.name}. Setting to 19132.`);
                opt.port = 19132;
            }
            if (!opt.entityTag || typeof opt.entityTag !== "string") {
                opt.entityTag = "";
            }
        });
        config.autoTransferOnSpawn = parsed.autoTransferOnSpawn || null;
        config.adminMenuItem = parsed.adminMenuItem || "minecraft:dirt";
        config.knownPlayers = Array.isArray(parsed.knownPlayers) ? parsed.knownPlayers.map(name => name.toLowerCase()) : [];
        config.transferHistoryLimit = parsed.transferHistoryLimit || 100;
    }
    console.log("Configuration loaded:", JSON.stringify(config.transferOptions, null, 2));
    loadStats();
}

function saveConfig() {
    const serializableConfig = {
        isConfigured: config.isConfigured,
        transferOptions: config.transferOptions,
        autoTransferOnSpawn: config.autoTransferOnSpawn,
        adminMenuItem: config.adminMenuItem,
        knownPlayers: config.knownPlayers,
        transferHistoryLimit: config.transferHistoryLimit,
    };
    world.setDynamicProperty("transferConfig", JSON.stringify(serializableConfig));
    config.isConfigured = true;
    console.log("Configuration saved.");
    saveStats();
}

function loadStats() {
    const savedStats = world.getDynamicProperty("transferStats");
    if (savedStats) {
        const parsed = JSON.parse(savedStats);
        transferStats.totalTransfers = parsed.totalTransfers || 0;
        transferStats.byDestination = new Map(parsed.byDestination ? Object.entries(parsed.byDestination) : []);
        transferStats.startTime = parsed.startTime || system.currentTick;
        transferStats.transferHistory = Array.isArray(parsed.transferHistory) ? parsed.transferHistory : [];
    } else {
        transferStats.startTime = system.currentTick;
    }
    console.log("Transfer statistics loaded.");
}

function saveStats() {
    const serializableStats = {
        totalTransfers: transferStats.totalTransfers,
        byDestination: Object.fromEntries(transferStats.byDestination),
        startTime: transferStats.startTime,
        transferHistory: transferStats.transferHistory,
    };
    world.setDynamicProperty("transferStats", JSON.stringify(serializableStats));
    console.log("Transfer statistics saved.");
}

function isAdmin(player) {
    return player.hasTag("admin");
}

function canPlayerTransfer(player, option) {
    if (isAdmin(player)) {
        return { allowed: true, reason: "" };
    }

    // Check maintenance mode first
    if (option.accessType === "maintenance") {
        const playerName = player.name.toLowerCase();
        const isMaintenanceAllowed = option.maintenanceAccessList.map(name => name.toLowerCase()).includes(playerName);
        return isMaintenanceAllowed
            ? { allowed: true, reason: "" }
            : { allowed: false, reason: "§cCurrently Under Maintenance" };
    }

    // If not in maintenance mode, proceed with normal access checks
    if (option.accessType === "none") {
        return { allowed: true, reason: "" };
    }

    const playerName = player.name.toLowerCase();
    const isListed = option.accessList.map(name => name.toLowerCase()).includes(playerName);
    if (option.accessType === "whitelist") {
        return isListed
            ? { allowed: true, reason: "" }
            : { allowed: false, reason: "§cYou're not whitelisted, please contact an admin" };
    }
    // If accessType is "blacklist"
    return isListed
        ? { allowed: false, reason: "§cYou're not allowed here" }
        : { allowed: true, reason: "" };
}

function isValidIp(ip) {
    if (ip.toLowerCase() === "test") return true;
    const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

function tryShowForm(player, form, maxTicks = 200, delayTicks = 5, onSuccess) {
    const startTick = system.currentTick;
    const THRESHOLD_TICKS = 20;

    function attempt() {
        if (system.currentTick - startTick > maxTicks) {
            player.sendMessage("§cFailed to open the form after 10 seconds.");
            return;
        }

        const showStartTick = system.currentTick;
        system.run(() => {
            form.show(player).then((response) => {
                const duration = system.currentTick - showStartTick;
                if (response.canceled && duration < THRESHOLD_TICKS) {
                    system.runTimeout(attempt, delayTicks);
                } else {
                    onSuccess(response);
                }
            });
        });
    }

    attempt();
}

function showAdminMenu(player) {
    if (!isAdmin(player)) {
        player.sendMessage("§cNo permission to configure transfers.");
        return;
    }
    const form = new ActionFormData()
        .title("Transfer Configuration")
        .body("Select an option:")
        .button("Manage Transfers")
        .button("Single Transfer on Spawn")
        .button("View Stats")
        .button("Open Transfer Item")
        .button("Close");

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || response.selection === 4) return;
        switch (response.selection) {
            case 0: showDestinationConfig(player); break;
            case 1: showGlobalSettings(player); break;
            case 2: showStatistics(player); break;
            case 3: showAdminItemConfig(player); break;
        }
    });
}

function showDestinationConfig(player) {
    const form = new ActionFormData()
        .title("Transfers")
        .body("Select a destination to manage or add a new one:")
        .button("Add New Destination");
    config.transferOptions.forEach((option) => {
        const triggerText = option.triggerType ? `Action: (${option.triggerType})` : "No Trigger";
        const confirmationText = option.requireConfirmation ? "Confirm: Yes" : "Confirm: No";
        const accessText = option.accessType !== "none" ? `${option.accessType}: ${option.accessList.length}` : "";
        // Added: Display maintenance White list count if in maintenance mode
        const maintenanceText = option.accessType === "maintenance" && option.maintenanceAccessList.length > 0 
            ? `Maint: ${option.maintenanceAccessList.length}` 
            : "";
        form.button(`${option.name} Tag: ${option.entityTag || "None"} ${triggerText} ${confirmationText} ${accessText} ${maintenanceText}`);
    });

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled) return;
        if (response.selection === 0) {
            showAddDestinationForm(player);
        } else {
            showDestinationOptions(player, response.selection - 1);
        }
    });
}

function showDestinationOptions(player, index) {
    const option = config.transferOptions[index];
    const form = new ActionFormData()
        .title(`Manage ${option.name}`)
        .body("Choose an action:")
        .button("Edit")
        .button("Back")
        .button("Delete");

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || response.selection === 1) {
            showDestinationConfig(player);
            return;
        }
        if (response.selection === 0) {
            showEditDestinationForm(player, index);
        } else if (response.selection === 2) {
            const confirmForm = new ActionFormData()
                .title("Confirm Deletion")
                .body(`Are you sure you want to delete "${option.name}"? This action cannot be undone.`)
                .button("Yes, Delete")
                .button("No, Cancel");

            tryShowForm(player, confirmForm, 200, 5, (confirmResponse) => {
                if (confirmResponse.canceled || confirmResponse.selection === 1) {
                    showDestinationOptions(player, index);
                    return;
                }
                if (confirmResponse.selection === 0) {
                    const deletedName = config.transferOptions[index].name;
                    config.transferOptions.splice(index, 1);
                    if (config.autoTransferOnSpawn === deletedName) {
                        config.autoTransferOnSpawn = null;
                    }
                    saveConfig();
                    player.sendMessage(`§aDeleted destination: ${deletedName}`);
                    showDestinationConfig(player);
                }
            });
        }
    });
}

function mapDropdownToTriggerType(dropdownValue) {
    switch (dropdownValue) {
        case "Hit": return "hit";
        case "Interact": return "interact";
        case "Both": return "both";
        default: return "hit";
    }
}

function showAddDestinationForm(player) {
    const availablePlayers = config.knownPlayers.filter(name => !config.transferOptions.some(opt => opt.accessList.includes(name)));
    const availableForMaintenance = config.knownPlayers; // Players available for maintenance access
    const triggerTypes = ["hit", "interact", "both"];
    const defaultTriggerTypeIndex = 0;
    let dropdownItems = ["Hit", "Interact", "Both"];
    dropdownItems = Array(3).fill(null).map((_, i) => ["Hit", "Interact", "Both"][(i + defaultTriggerTypeIndex) % 3]);
    const adjustedDefaultIndex = 0;

    const accessTypes = ["none", "whitelist", "blacklist", "maintenance"];
    const defaultAccessTypeIndex = 0;
    const accessDropdownItems = ["Everyone", "Whitelist", "Blacklist", "Maintenance"];
    const reorderedAccessItems = Array(4).fill(null).map((_, i) => accessDropdownItems[(i + defaultAccessTypeIndex) % 4]);

    const form = new ModalFormData()
        .title("Add Transfer Destination")
        .textField("Destination Name", "e.g., Hub", { defaultValue: "Hub" })
        .textField("IP Address", "e.g., *********** or 'test'", { defaultValue: "" })
        .textField("Port", "e.g., 19132", { defaultValue: "19132" })
        .textField("Entity Tag", "e.g., hubTransfer", { defaultValue: "" })
        .dropdown("Trigger Type", dropdownItems, { defaultValue: adjustedDefaultIndex })
        .toggle("Ask Confirmation Form Before Transfer?", { defaultValue: false })
        .dropdown("Access Control", reorderedAccessItems, { defaultValue: 0 })
        .dropdown("Current White List", ["None"], { defaultValue: 0 })
        .dropdown("Add Player to White list", ["None", ...availablePlayers], { defaultValue: 0 })
        .dropdown("Remove Player from White list", ["None"], { defaultValue: 0 })
        .dropdown("Current Maintenance White list", ["None"], { defaultValue: 0 }) // Added: Maintenance White list
        .dropdown("Add Player to Maintenance Access", ["None", ...availableForMaintenance], { defaultValue: 0 }) // Added: Add to maintenance list
        .dropdown("Remove Player from Maintenance Access", ["None"], { defaultValue: 0 }) // Added: Remove from maintenance list
        .textField("Add Player Name Not Listed", "e.g., NewPlayer", { defaultValue: "" });

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || !response.formValues) return;
        const [name, ip, port, entityTag, triggerTypeIndex, requireConfirmation, accessTypeIndex, currentListIndex, addPlayerIndex, removePlayerIndex, currentMaintListIndex, addMaintPlayerIndex, removeMaintPlayerIndex, customName] = response.formValues;

        if (config.transferOptions.some(opt => opt.name === name)) {
            player.sendMessage("§cA destination with this name already exists. Please choose a different name.");
            showAddDestinationForm(player);
            return;
        }

        const portNumber = parseInt(port);
        if (isNaN(portNumber) || portNumber < 0 || portNumber > 65535) {
            player.sendMessage("§cInvalid port number. Please enter a number between 0 and 65535.");
            showAddDestinationForm(player);
            return;
        }

        if (!isValidIp(ip)) {
            player.sendMessage("§cInvalid IP address. Please enter a valid IPv4 address (e.g., ***********) or 'test' for debugging.");
            showAddDestinationForm(player);
            return;
        }

        const triggerType = mapDropdownToTriggerType(dropdownItems[triggerTypeIndex]);
        const accessType = accessTypes[accessDropdownItems.indexOf(reorderedAccessItems[accessTypeIndex])];
        let accessListArray = [];
        let maintenanceAccessListArray = [];

        if (addPlayerIndex > 0) {
            const playerToAdd = availablePlayers[addPlayerIndex - 1].toLowerCase();
            if (!accessListArray.includes(playerToAdd)) {
                accessListArray.push(playerToAdd);
            }
        }

        if (addMaintPlayerIndex > 0) {
            const playerToAdd = availableForMaintenance[addMaintPlayerIndex - 1].toLowerCase();
            if (!maintenanceAccessListArray.includes(playerToAdd)) {
                maintenanceAccessListArray.push(playerToAdd);
            }
        }

        if (customName) {
            const customNameLower = customName.toLowerCase();
            if (!accessListArray.includes(customNameLower)) {
                accessListArray.push(customNameLower);
            }
            if (!maintenanceAccessListArray.includes(customNameLower)) {
                maintenanceAccessListArray.push(customNameLower);
            }
            if (!config.knownPlayers.includes(customNameLower)) {
                config.knownPlayers.push(customNameLower);
            }
        }

        config.transferOptions.push({
            name,
            ip,
            port: portNumber,
            entityTag: entityTag || "",
            triggerType,
            requireConfirmation,
            accessType,
            accessList: accessListArray,
            maintenanceAccessList: maintenanceAccessListArray, // Added: Maintenance White list
        });
        saveConfig();
        player.sendMessage(`§aAdded new destination: ${name} with tag: ${entityTag || "None"}, trigger: ${triggerType}, confirmation: ${requireConfirmation ? "Yes" : "No"}, access: ${accessType}${accessListArray.length > 0 ? " (" + accessListArray.join(", ") + ")" : ""}${maintenanceAccessListArray.length > 0 ? ", maint access: (" + maintenanceAccessListArray.join(", ") + ")" : ""}`);
        showDestinationConfig(player);
    });
}

function showEditDestinationForm(player, index) {
    const option = config.transferOptions[index];
    const oldName = option.name;
    
    console.log(`Editing destination: ${option.name}, current triggerType: ${option.triggerType}`);
    
    const triggerTypes = ["hit", "interact", "both"];
    const triggerTypeIndex = triggerTypes.indexOf(option.triggerType);
    const defaultTriggerTypeIndex = triggerTypeIndex !== -1 ? triggerTypeIndex : 0;
    
    console.log(`triggerTypeIndex: ${triggerTypeIndex}, defaultTriggerTypeIndex: ${defaultTriggerTypeIndex}`);
    
    const accessTypes = ["none", "whitelist", "blacklist", "maintenance"];
    const accessTypeIndex = accessTypes.indexOf(option.accessType);
    const defaultAccessTypeIndex = accessTypeIndex !== -1 ? accessTypeIndex : 0;
    const accessDropdownItems = ["Everyone", "Whitelist", "Blacklist", "Maintenance"];
    const reorderedAccessItems = Array(4).fill(null).map((_, i) => accessDropdownItems[(i + defaultAccessTypeIndex) % 4]);
    console.log(`Access dropdown items reordered to: ${reorderedAccessItems}`);

    const availablePlayers = config.knownPlayers.filter(name => !option.accessList.includes(name));
    const availableForMaintenance = config.knownPlayers.filter(name => !option.maintenanceAccessList.includes(name));
    const currentList = option.accessList.length > 0 ? option.accessList : ["None"];
    const currentMaintList = option.maintenanceAccessList.length > 0 ? option.maintenanceAccessList : ["None"];
    const currentListDefaultIndex = 0;
    const currentMaintListDefaultIndex = 0;

    let dropdownItems = ["Hit", "Interact", "Both"];
    dropdownItems = Array(3).fill(null).map((_, i) => ["Hit", "Interact", "Both"][(i + defaultTriggerTypeIndex) % 3]);
    const adjustedDefaultIndex = 0;
    console.log(`Dropdown items reordered to: ${dropdownItems}, adjustedDefaultIndex: ${adjustedDefaultIndex}`);

    const form = new ModalFormData()
        .title(`Edit ${option.name}`)
        .textField("Destination Name", option.name, { defaultValue: option.name ?? "" })
        .textField("IP Address", option.ip, { defaultValue: option.ip ?? "" })
        .textField("Port", option.port.toString(), { defaultValue: option.port.toString() ?? "" })
        .textField("Entity Tag", option.entityTag || "", { defaultValue: option.entityTag || "" })
        .dropdown("Trigger Type", dropdownItems, { defaultValue: adjustedDefaultIndex })
        .toggle("Instant | Show Confirm Form", { defaultValue: option.requireConfirmation || false })
        .dropdown("Access", reorderedAccessItems, { defaultValue: 0 })
        .dropdown("Current White list", currentList, { defaultValue: currentListDefaultIndex })
        .dropdown("Add Known Player to White list", ["None", ...availablePlayers], { defaultValue: 0 })
        .dropdown("Remove Player from White list", ["None", ...option.accessList], { defaultValue: 0 })
        .dropdown("Current Maintenance White list", currentMaintList, { defaultValue: currentMaintListDefaultIndex }) // Added: Maintenance White list
        .dropdown("Add Player to Maintenance Access", ["None", ...availableForMaintenance], { defaultValue: 0 }) // Added: Add to maintenance list
        .dropdown("Remove Player from Maintenance Access", ["None", ...option.maintenanceAccessList], { defaultValue: 0 }) // Added: Remove from maintenance list
        .textField("Add Missing Player Name", "e.g., Mr Leefy", { defaultValue: undefined ?? "" });

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || !response.formValues) return;
        const [name, ip, port, entityTag, selectedTriggerTypeIndex, requireConfirmation, accessTypeIndex, currentListIndex, addPlayerIndex, removePlayerIndex, currentMaintListIndex, addMaintPlayerIndex, removeMaintPlayerIndex, customName] = response.formValues;

        if (config.transferOptions.some(opt => opt.name === name && opt !== option)) {
            player.sendMessage("§cA destination with this name already exists. Please choose a different name.");
            showEditDestinationForm(player, index);
            return;
        }

        const portNumber = parseInt(port);
        if (isNaN(portNumber) || portNumber < 0 || portNumber > 65535) {
            player.sendMessage("§cInvalid port number. Please enter a number between 0 and 65535.");
            showEditDestinationForm(player, index);
            return;
        }

        if (!isValidIp(ip)) {
            player.sendMessage("§cInvalid IP address. Please enter a valid IPv4 address (e.g., ***********) or 'test' for debugging.");
            showEditDestinationForm(player, index);
            return;
        }

        const triggerType = mapDropdownToTriggerType(dropdownItems[selectedTriggerTypeIndex]);
        console.log(`Selected triggerType: ${triggerType} (index: ${selectedTriggerTypeIndex})`);
        const accessType = accessTypes[accessDropdownItems.indexOf(reorderedAccessItems[accessTypeIndex])];
        let accessListArray = [...option.accessList];
        let maintenanceAccessListArray = [...option.maintenanceAccessList];

        if (addPlayerIndex > 0) {
            const playerToAdd = availablePlayers[addPlayerIndex - 1].toLowerCase();
            if (!accessListArray.includes(playerToAdd)) {
                accessListArray.push(playerToAdd);
            }
        }

        if (removePlayerIndex > 0) {
            accessListArray.splice(removePlayerIndex - 1, 1);
        }

        if (addMaintPlayerIndex > 0) {
            const playerToAdd = availableForMaintenance[addMaintPlayerIndex - 1].toLowerCase();
            if (!maintenanceAccessListArray.includes(playerToAdd)) {
                maintenanceAccessListArray.push(playerToAdd);
            }
        }

        if (removeMaintPlayerIndex > 0) {
            maintenanceAccessListArray.splice(removeMaintPlayerIndex - 1, 1);
        }

        if (customName) {
            const customNameLower = customName.toLowerCase();
            if (!accessListArray.includes(customNameLower)) {
                accessListArray.push(customNameLower);
            }
            if (!maintenanceAccessListArray.includes(customNameLower)) {
                maintenanceAccessListArray.push(customNameLower);
            }
            if (!config.knownPlayers.includes(customNameLower)) {
                config.knownPlayers.push(customNameLower);
            }
        }

        config.transferOptions[index] = {
            name,
            ip,
            port: portNumber,
            entityTag: entityTag || "",
            triggerType,
            requireConfirmation,
            accessType,
            accessList: accessListArray,
            maintenanceAccessList: maintenanceAccessListArray,
        };
        if (oldName !== name && config.autoTransferOnSpawn === oldName) {
            config.autoTransferOnSpawn = name;
        }
        saveConfig();
        player.sendMessage(`§aUpdated destination: ${name}, confirmation: ${requireConfirmation ? "Yes" : "No"}, access: ${accessType}${accessListArray.length > 0 ? " (" + accessListArray.join(", ") + ")" : ""}${maintenanceAccessListArray.length > 0 ? ", maint access: (" + maintenanceAccessListArray.join(", ") + ")" : ""}`);
        showDestinationConfig(player);
    });
}

function showGlobalSettings(player) {
    const destinations = ["None", ...config.transferOptions.map(opt => opt.name)];
    const currentIndex = config.autoTransferOnSpawn ? destinations.indexOf(config.autoTransferOnSpawn) : 0;
    const reorderedDestinations = Array(destinations.length).fill(null).map((_, i) => destinations[(i + currentIndex) % destinations.length]);
    const form = new ModalFormData()
        .title("Single Transfer")
        .dropdown("§2Auto Transfer on Spawn\n§6This means when the player spawns they go directly to the selected transfer", reorderedDestinations, { defaultValue: 0 });

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || !response.formValues) return;
        const [selectedIndex] = response.formValues;
        const selectedDestination = reorderedDestinations[selectedIndex];
        config.autoTransferOnSpawn = selectedDestination === "None" ? null : selectedDestination;
        saveConfig();
        player.sendMessage(`§aAuto Transfer on Spawn set to: ${config.autoTransferOnSpawn || "None"}`);
    });
}

function showStatistics(player) {
    const now = system.currentTick;
    const uptime = Math.floor((now - transferStats.startTime) / 1200);
    let bodyText = `§eTransfer System Statistics\n`;
    bodyText += `§7Uptime: ${uptime} minutes\n`;
    bodyText += `§7Total Transfers: ${transferStats.totalTransfers}\n\n`;
    bodyText += `§aDestinations:\n`;
    config.transferOptions.forEach((opt) => {
        bodyText += `§7- ${opt.name}: ${transferStats.byDestination.get(opt.name) || 0} transfers\n`;
    });
    bodyText += `\n§aRecent Transfers (Last ${config.transferHistoryLimit}):\n`;
    if (transferStats.transferHistory.length === 0) {
        bodyText += "§7No recent transfers.\n";
    } else {
        transferStats.transferHistory.slice(-10).forEach((entry) => {
            const time = new Date(entry.timestamp).toLocaleTimeString();
            bodyText += `§7- ${entry.playerName} to ${entry.destination} at ${time} (Trigger: ${entry.triggerType})\n`;
        });
    }

    const form = new ActionFormData()
        .title("System Statistics")
        .body(bodyText)
        .button("Close");

    tryShowForm(player, form, 200, 5, () => {});
}

function showAdminItemConfig(player) {
    const form = new ModalFormData()
        .title("Configure Admin Item")
        .textField("Item ID", "e.g., minecraft:stick", { defaultValue: String(config.adminMenuItem ?? "") });

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || !response.formValues) return;
        const [itemId] = response.formValues;
        config.adminMenuItem = (itemId && typeof itemId === "string") ? itemId : "minecraft:dirt";
        saveConfig();
        player.sendMessage(`§aAdmin menu item set to: ${config.adminMenuItem}`);
        showAdminMenu(player);
    });
}

function attemptTransfer(player, destinationName, entityTag, bypassCooldown = false, triggerType = "unknown") {
    const destination = config.transferOptions.find(opt => opt.name === destinationName);
    if (!destination) {
        player.sendMessage("§cDestination not found.");
        return;
    }

    const transferCheck = canPlayerTransfer(player, destination);
    if (!transferCheck.allowed) {
        player.sendMessage(transferCheck.reason);
        return;
    }

    const now = system.currentTick;
    if (!bypassCooldown) {
        const lastTransfer = transferCooldowns.get(player.id) || 0;
        if (now - lastTransfer < 100) {
            player.sendMessage(`§cWait ${Math.ceil((100 - (now - lastTransfer)) / 20)} seconds.`);
            return;
        }
    }

    let attempts = 0;
    function tryTransfer() {
        attempts++;
        player.sendMessage(`§eAttempt ${attempts}/5...`);
        try {
            transferPlayer(player, destination.ip, Number(destination.port));
            player.sendMessage("§aTransfer successful!");
            transferCooldowns.set(player.id, now);
            transferStats.totalTransfers++;
            transferStats.byDestination.set(destinationName, (transferStats.byDestination.get(destinationName) || 0) + 1);
            transferStats.transferHistory.push({
                playerName: player.name,
                destination: destinationName,
                timestamp: Date.now(),
                triggerType,
            });
            if (transferStats.transferHistory.length > config.transferHistoryLimit) {
                transferStats.transferHistory.shift();
            }
            saveStats();
        } catch (error) {
            const errorMessage = error.message || "Unknown error";
            console.error(`Transfer failed for ${player.name} to ${destinationName} (IP: ${destination.ip}, Port: ${destination.port}): ${errorMessage}`);
            if (attempts < 5) {
                player.sendMessage(`§cAttempt ${attempts} failed: ${errorMessage}. Retrying...`);
                system.runTimeout(tryTransfer, 20);
            } else {
                player.sendMessage(`§cTransfer failed after all attempts: ${errorMessage}. Please contact an admin.`);
                transferCooldowns.set(player.id, now);
            }
        }
    }
    tryTransfer();
}

function showConfirmationDialog(player, destinationName, onConfirm) {
    const form = new ActionFormData()
        .title("Confirm Transfer")
        .body(`Do you want to transfer to ${destinationName}?`)
        .button("Yes")
        .button("No");

    tryShowForm(player, form, 200, 5, (response) => {
        if (!response.canceled && response.selection === 0) {
            onConfirm();
        }
    });
}

world.afterEvents.playerSpawn.subscribe((event) => {
    const player = event.player;
    const playerName = player.name;
    const playerNameLower = playerName.toLowerCase();
    
    if (!config.knownPlayers.includes(playerNameLower)) {
        config.knownPlayers.push(playerNameLower);
        saveConfig();
    }

    console.log(`Player ${playerName} spawned. Is admin: ${isAdmin(player)}, autoTransferOnSpawn: ${config.autoTransferOnSpawn}`);

    if (isAdmin(player)) {
        player.sendMessage("§eAdmin Commands Available:");
        player.sendMessage("§a!t §7- Transfer Menu");
        player.sendMessage("§a!transfer §7- Transfer Menu");
        player.sendMessage(`§7Right-click with §2${config.adminMenuItem}§7 to open Transfer Menu`);
        // Modified: Show a message even if autoTransferOnSpawn is not set or invalid
        if (config.autoTransferOnSpawn) {
            const destination = config.transferOptions.find(opt => opt.name === config.autoTransferOnSpawn);
            if (destination) {
                player.sendMessage(`§4ADMIN: §6If you were a normal player, you would have transferred to §2${config.autoTransferOnSpawn}§6! Please sneak and use §2${config.adminMenuItem} §2to continue...`);
            } else {
                player.sendMessage(`§4ADMIN WARNING: §cAuto Transfer on Spawn is set to "${config.autoTransferOnSpawn}", but this destination does not exist. Please update it in the admin menu.`);
            }
        } else {
            player.sendMessage(`§4ADMIN INFO: §6Auto Transfer on Spawn is not set. Normal players will not be transferred on spawn.`);
        }
        return;
    }

    if (!config.autoTransferOnSpawn) {
        console.log(`Auto-transfer on spawn is not set for ${playerName}.`);
        return;
    }

    const destination = config.transferOptions.find(opt => opt.name === config.autoTransferOnSpawn);
    if (!destination) {
        console.log(`Auto transfer destination "${config.autoTransferOnSpawn}" not found for ${playerName}.`);
        player.sendMessage(`§cAuto-transfer failed: The destination "${config.autoTransferOnSpawn}" does not exist. Please contact an admin.`);
        return;
    }

    const transferCheck = canPlayerTransfer(player, destination);
    if (!transferCheck.allowed) {
        player.sendMessage(transferCheck.reason); // Added: Show the reason before kicking
        console.log(`Transfer denied for ${playerName} to ${destination.name}: ${transferCheck.reason}`);
        system.runTimeout(() => {
            try {
                player.runCommand(`kick "${playerName}" ${transferCheck.reason.replace(/§[0-9a-f]/g, "")}`);
            } catch (error) {
                console.error(`Failed to kick ${playerName}: ${error}`);
                player.sendMessage(`§cFailed to kick you after transfer denial. Please leave manually or contact an admin.`);
            }
        }, 20); // Delay the kick slightly to ensure the player sees the message
        return;
    }

    player.sendMessage(`§eTransferring to ${destination.name}...`);
    console.log(`Attempting auto-transfer for ${playerName} to ${destination.name} (IP: ${destination.ip}, Port: ${destination.port})`);
    attemptTransfer(player, destination.name, destination.entityTag, true, "auto-spawn");
});


world.afterEvents.playerLeave.subscribe((event) => {
    transferCooldowns.delete(event.playerId);
});

world.beforeEvents.playerInteractWithEntity.subscribe((event) => {
    const player = event.player;
    const entity = event.target;
    if (entity.typeId === "minecraft:player") return;

    const transferOption = config.transferOptions.find(opt => 
        entity.hasTag(opt.entityTag) && (opt.triggerType === "interact" || opt.triggerType === "both")
    );
    if (transferOption) {
        console.log(`Interact event triggered for destination: ${transferOption.name}, triggerType: ${transferOption.triggerType}`);
        if (transferOption.requireConfirmation) {
            showConfirmationDialog(player, transferOption.name, () => {
                player.sendMessage(`§eTransferring to ${transferOption.name}...`);
                attemptTransfer(player, transferOption.name, transferOption.entityTag, false, transferOption.triggerType);
            });
        } else {
            player.sendMessage(`§eTransferring to ${transferOption.name}...`);
            attemptTransfer(player, transferOption.name, transferOption.entityTag, false, transferOption.triggerType);
        }
    }
});

world.afterEvents.entityHitEntity.subscribe((event) => {
    if (!(event.damagingEntity instanceof Player)) return;
    const player = event.damagingEntity;
    const entity = event.hitEntity;
    const transferOption = config.transferOptions.find(opt => 
        entity.hasTag(opt.entityTag) && (opt.triggerType === "hit" || opt.triggerType === "both")
    );
    if (transferOption) {
        console.log(`Hit event triggered for destination: ${transferOption.name}, triggerType: ${transferOption.triggerType}`);
        if (transferOption.requireConfirmation) {
            showConfirmationDialog(player, transferOption.name, () => {
                player.sendMessage(`§eTransferring to ${transferOption.name}...`);
                attemptTransfer(player, transferOption.name, transferOption.entityTag, false, transferOption.triggerType);
            });
        } else {
            player.sendMessage(`§eTransferring to ${transferOption.name}...`);
            attemptTransfer(player, transferOption.name, transferOption.entityTag, false, transferOption.triggerType);
        }
    }
});

world.beforeEvents.chatSend.subscribe((event) => {
    const player = event.sender;
    const message = event.message.toLowerCase();

    if (isAdmin(player)) {
        switch (message) {
            case "!t":
                event.cancel = true;
                player.sendMessage("§ePlease close chat to open form!");
                showAdminMenu(player);
                break;
            case "!transfer":
                event.cancel = true;
                player.sendMessage("§ePlease close chat to open form!");
                showTransferMenu(player);
                break;
        }
    } else {
        if (message === "!t" || message === "!transfer") {
            event.cancel = true;
            player.sendMessage("§cSorry, you need the admin role!");
        }
    }
});

world.beforeEvents.itemUse.subscribe((event) => {
    const player = event.source;
    const item = event.itemStack;

    
    if (isAdmin(player) && item?.typeId === config.adminMenuItem) {
        if (player.isSneaking && config.autoTransferOnSpawn) {
            console.log(`Attempting admin-sneak transfer for ${player.name}. autoTransferOnSpawn: ${config.autoTransferOnSpawn}`);
            const destination = config.transferOptions.find(opt => opt.name === config.autoTransferOnSpawn);
            if (destination) {
                player.sendMessage(`§eTransferring to ${destination.name}...`);
                console.log(`Destination found: ${destination.name} (IP: ${destination.ip}, Port: ${destination.port})`);
                try {
                    attemptTransfer(player, destination.name, destination.entityTag, true, "admin-sneak");
                } catch (error) {
                    console.error(`Error in attemptTransfer for ${player.name}: ${error}`);
                    player.sendMessage(`§cFailed to transfer: ${error.message || "Unknown error"}. Please check the server logs or contact support.`);
                }
            } else {
                console.log(`Auto transfer destination "${config.autoTransferOnSpawn}" not found for ${player.name}.`);
                player.sendMessage(`§cAuto-transfer failed: The destination "${config.autoTransferOnSpawn}" does not exist. Please update it in the admin menu.`);
            }
        } else {
            console.log(`Opening admin menu for ${player.name}. isSneaking: ${player.isSneaking}, autoTransferOnSpawn: ${config.autoTransferOnSpawn}`);
            showAdminMenu(player);
        }
    }
});


function showTransferMenu(player) {
    const form = new ActionFormData()
        .title("Transfer Menu")
        .body("Select a destination to transfer to:")
        .button("Back");
    config.transferOptions.forEach((option) => {
        form.button(option.name);
    });

    tryShowForm(player, form, 200, 5, (response) => {
        if (response.canceled || response.selection === 0) {
            showAdminMenu(player);
            return;
        }
        const selectedOption = config.transferOptions[response.selection - 1];
        if (selectedOption.requireConfirmation) {
            showConfirmationDialog(player, selectedOption.name, () => {
                attemptTransfer(player, selectedOption.name, selectedOption.entityTag, false, "menu");
            });
        } else {
            attemptTransfer(player, selectedOption.name, selectedOption.entityTag, false, "menu");
        }
    });
}

system.run(() => {
    loadConfig();
    console.log("Transfer system initialized.");
});
