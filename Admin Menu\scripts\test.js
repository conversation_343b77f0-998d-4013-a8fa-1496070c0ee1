import { world, system } from "@minecraft/server";

// Global performance tracking
const tickTimes = [];
let lastTime = Date.now();
let globalWorstChunk = { x: 0, z: 0, count: 0 };

// Track entity density per chunk
const chunkEntityCount = new Map();

// Update TPS metrics every tick
system.runInterval(() => {
    const now = Date.now();
    const delta = now - lastTime;
    lastTime = now;
    
    tickTimes.push(delta);
    if (tickTimes.length > 100) tickTimes.shift();
}, 1);

// Update entity distribution every 5 seconds
system.runInterval(() => {
    chunkEntityCount.clear();
    globalWorstChunk.count = 0;

    for (const entity of world.getAllEntities()) {
        const { x, z } = entity.location;
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        const count = (chunkEntityCount.get(chunkKey) || 0) + 1;
        chunkEntityCount.set(chunkKey, count);
        
        // Track global worst chunk
        if (count > globalWorstChunk.count) {
            globalWorstChunk = { x: chunkX, z: chunkZ, count };
        }
    }
}, 100);

// Convert chunk to block coordinates
function chunkToBlockCoord(chunkCoord) {
    return chunkCoord * 16 + 8;
}

// Find worst chunk near a player
function findWorstNearPlayer(player) {
    const { x, z } = player.location;
    const playerChunkX = Math.floor(x / 16);
    const playerChunkZ = Math.floor(z / 16);
    
    let worstInArea = { count: 0 };
    const scanRadius = 4;  // 64 blocks radius (4 chunks)
    
    // Scan nearby chunks
    for (let dx = -scanRadius; dx <= scanRadius; dx++) {
        for (let dz = -scanRadius; dz <= scanRadius; dz++) {
            const chunkKey = `${playerChunkX + dx},${playerChunkZ + dz}`;
            const count = chunkEntityCount.get(chunkKey) || 0;
            
            if (count > worstInArea.count) {
                worstInArea = {
                    x: playerChunkX + dx,
                    z: playerChunkZ + dz,
                    count
                };
            }
        }
    }
    
    return worstInArea;
}

// Register chat command
world.beforeEvents.chatSend.subscribe(event => {
    const msg = event.message.toLowerCase();
    if (msg !== "-tps") return;
    
    event.cancel = true;
    const player = event.sender;
    
    // Calculate TPS
    const avgTickTime = tickTimes.reduce((a, b) => a + b, 0) / tickTimes.length;
    const currentTPS = Math.min(20, (1000 / avgTickTime)).toFixed(1);
    
    // Header message
    let report = [
        `§6Server TPS: §a${currentTPS}§r`,
        `§6Global Worst: §c${globalWorstChunk.count} entities §rat §aX=${chunkToBlockCoord(globalWorstChunk.x)} Z=${chunkToBlockCoord(globalWorstChunk.z)}`
    ];
    
    // Check all online players
    for (const onlinePlayer of world.getAllPlayers()) {
        const worstNearPlayer = findWorstNearPlayer(onlinePlayer);
        
        if (worstNearPlayer.count > 0) {
            report.push(
                `§e${onlinePlayer.name}: §c${worstNearPlayer.count} entities §r@ §aX=${chunkToBlockCoord(worstNearPlayer.x)} Z=${chunkToBlockCoord(worstNearPlayer.z)}`
            );
        }
    }
    
    // Send report to player who triggered command
    player.sendMessage(report.join('\n'));
});