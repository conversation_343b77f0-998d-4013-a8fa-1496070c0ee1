// testCommandBlocks.js — Finds command blocks in a 50 block radius

import { world } from "@minecraft/server";

const SCAN_RADIUS = 50;

world.beforeEvents.chatSend.subscribe((event) => {
  const player = event.sender;
  const message = event.message;

  if (message.trim().toLowerCase() !== "-tc") return;

  event.cancel = true; // prevent message from appearing in chat

  const origin = player.location;
  const dimension = player.dimension;

  let found = false;

  for (let x = -SCAN_RADIUS; x <= SCAN_RADIUS; x++) {
    for (let y = -SCAN_RADIUS; y <= SCAN_RADIUS; y++) {
      for (let z = -SCAN_RADIUS; z <= SCAN_RADIUS; z++) {
        const bx = Math.floor(origin.x + x);
        const by = Math.floor(origin.y + y);
        const bz = Math.floor(origin.z + z);

        try {
          const block = dimension.getBlock({ x: bx, y: by, z: bz });
          if (block?.typeId === "minecraft:command_block") {
            found = true;
            player.sendMessage(`§6Found command block at §a${bx} ${by} ${bz}`);
            // Uncomment the next line if you want to delete it:
            // dimension.runCommandAsync(`setblock ${bx} ${by} ${bz} air`);
          }
        } catch {}
      }
    }
  }

  if (!found) {
    player.sendMessage("§cNo command blocks found within 50 blocks.");
  }
});