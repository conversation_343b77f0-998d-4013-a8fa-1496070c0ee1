import { getScore, setScore } from './functions.js';

export class ShopItem {
    constructor(name, price, description, quantity, itemId, icon = '') {
        this.name = name;
        this.price = price;
        this.description = description;
        this.quantity = quantity;
        this.itemId = itemId;
        this.icon = icon;
    }
}

class Shops {
    constructor() {
        this.items = new Map();
    }

    addItem(item) {
        this.items.set(item.name, item);
    }

    getItem(name) {
        return this.items.get(name);
    }

    buyItem(itemName, quantity, player) {
        return new Promise(resolve => {
            const item = this.getItem(itemName);
            if (!item) {
                console.warn('Item not available.');
                resolve(false);
                return;
            }

            const totalCost = item.price * quantity;
            const playerBalance = getScore(player.name, getMoneyObjective());

            if (playerBalance >= totalCost) {
                setScore(player.name, getMoneyObjective(), playerBalance - totalCost);
                const db = new DB();
                const currentStorage = db.get("storedMoney") || 0;
                db.set("storedMoney", currentStorage + totalCost);
                
                player.runCommand(`give "${player.name}" ${item.itemId} ${quantity}`);
                resolve(true);
            } else {
                player.sendMessage("§cInsufficient funds!");
                resolve(false);
            }
        });
    }
}    

export const shop = new Shops();

export const shopList = [
    {
        shopType: "Armor Trims",
        items: [
            new ShopItem("Netherite Upgrade", 5000, "Upgrade gear to Netherite", 1, 
                "minecraft:netherite_upgrade_smithing_template", 
                "textures/items/netherite_upgrade_smithing_template"),

            new ShopItem("Sentry Trim", 2500, "Sentry Armor Trim Pattern", 1, 
                "minecraft:sentry_armor_trim_smithing_template", 
                "textures/items/sentry_armor_trim_smithing_template"),

            new ShopItem("Vex Trim", 2500, "Vex Armor Trim Pattern", 1, 
                "minecraft:vex_armor_trim_smithing_template", 
                "textures/items/vex_armor_trim_smithing_template"),

            new ShopItem("Wild Trim", 2500, "Wild Armor Trim Pattern", 1, 
                "minecraft:wild_armor_trim_smithing_template", 
                "textures/items/wild_armor_trim_smithing_template"),

            new ShopItem("Coast Trim", 2500, "Coast Armor Trim Pattern", 1, 
                "minecraft:coast_armor_trim_smithing_template", 
                "textures/items/coast_armor_trim_smithing_template"),

            new ShopItem("Dune Trim", 2500, "Dune Armor Trim Pattern", 1, 
                "minecraft:dune_armor_trim_smithing_template", 
                "textures/items/dune_armor_trim_smithing_template"),

            new ShopItem("Wayfinder Trim", 2500, "Wayfinder Armor Trim Pattern", 1, 
                "minecraft:wayfinder_armor_trim_smithing_template", 
                "textures/items/wayfinder_armor_trim_smithing_template"),

            new ShopItem("Raiser Trim", 2500, "Raiser Armor Trim Pattern", 1, 
                "minecraft:raiser_armor_trim_smithing_template", 
                "textures/items/raiser_armor_trim_smithing_template"),

            new ShopItem("Shaper Trim", 2500, "Shaper Armor Trim Pattern", 1, 
                "minecraft:shaper_armor_trim_smithing_template", 
                "textures/items/shaper_armor_trim_smithing_template"),

            new ShopItem("Host Trim", 2500, "Host Armor Trim Pattern", 1, 
                "minecraft:host_armor_trim_smithing_template", 
                "textures/items/host_armor_trim_smithing_template"),

            new ShopItem("Ward Trim", 2500, "Ward Armor Trim Pattern", 1, 
                "minecraft:ward_armor_trim_smithing_template", 
                "textures/items/ward_armor_trim_smithing_template"),

            new ShopItem("Silence Trim", 2500, "Silence Armor Trim Pattern", 1, 
                "minecraft:silence_armor_trim_smithing_template", 
                "textures/items/silence_armor_trim_smithing_template"),

            new ShopItem("Tide Trim", 2500, "Tide Armor Trim Pattern", 1, 
                "minecraft:tide_armor_trim_smithing_template", 
                "textures/items/tide_armor_trim_smithing_template"),

            new ShopItem("Snout Trim", 2500, "Snout Armor Trim Pattern", 1, 
                "minecraft:snout_armor_trim_smithing_template", 
                "textures/items/snout_armor_trim_smithing_template"),

            new ShopItem("Rib Trim", 2500, "Rib Armor Trim Pattern", 1, 
                "minecraft:rib_armor_trim_smithing_template", 
                "textures/items/rib_armor_trim_smithing_template"),

            new ShopItem("Eye Trim", 2500, "Eye Armor Trim Pattern", 1, 
                "minecraft:eye_armor_trim_smithing_template", 
                "textures/items/eye_armor_trim_smithing_template"),

            new ShopItem("Bolt Trim", 2500, "Bolt Armor Trim Pattern", 1, 
                "minecraft:bolt_armor_trim_smithing_template", 
                "textures/items/bolt_armor_trim_smithing_template"),

           new ShopItem("Spire Trim", 2500, "Spire Armor Trim Pattern", 1, 
                "minecraft:spire_armor_trim_smithing_template", 
                "textures/items/spire_armor_trim_smithing_template"),

            new ShopItem("Flow Trim", 2500, "Flow Armor Trim Pattern", 1, 
                "minecraft:Flow_armor_trim_smithing_template", 
                "textures/items/flow_armor_trim_smithing_template")
        ]
    }
];

// Initialize shop with items
shopList.forEach(shopData => {
    shopData.items.forEach(item => {
        shop.addItem(item);
    });
});