import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { shopList } from "./index.js";
import { DB } from "./DB.js";

// === SCOREBOARD HELPERS ===
function getScore(player, objectiveName) {
  const objective = world.scoreboard.getObjective(objectiveName);
  if (!objective) return 0;
  const identity = player.scoreboardIdentity;
  return identity ? objective.getScore(identity) : 0;
}

function setScore(player, objectiveName, value) {
  const objective = world.scoreboard.getObjective(objectiveName);
  if (!objective) return;
  const identity = player.scoreboardIdentity || player;
  objective.setScore(identity, value);
}



// === ECONOMY DATABASE ===
const db = new DB();

function getMoneyObjective() {
  return db.get("moneyObjective") || "Money";
}

// Initialize the scoreboard using the API
const moneyObjective = getMoneyObjective();
if (!world.scoreboard.getObjective(moneyObjective)) {
  world.scoreboard.addObjective(moneyObjective, moneyObjective);
}

// === COOLDOWN MANAGEMENT ===
const lastHit = new Map();

// === ENTITY INTERACTION ===
world.afterEvents.entityHitEntity.subscribe((evd) => {
  const { hitEntity, damagingEntity } = evd;

  if (!damagingEntity || damagingEntity.typeId !== "minecraft:player") return;

  if (
    hitEntity &&
    hitEntity.typeId === "npc:npc_custom4" &&
    hitEntity.hasTag("trims")
  ) {
    const player = damagingEntity;
    const now = Date.now();
    if (lastHit.has(player.id) && now - lastHit.get(player.id) < 5000) {
      player.sendMessage(
        `§r§8[§3Armor Trim Shop§8] §cPlease wait before interacting again!`
      );
      return;
    }
    lastHit.set(player.id, now);
    primaryMenu(player);
  }
});

// === MENUS ===
function primaryMenu(player) {
  const menu = new ActionFormData()
    .title("§l§3Armor Trim Shop")
    .body("Select an option:");

  if (player.hasTag("trimadmin")) {
    menu.button("Withdraw Stored Funds", "textures/items/coin");
  }

  menu.button("Armor Trims", "textures/items/netherite_upgrade_smithing_template");

  menu.show(player).then(response => {
    if (response.canceled) return;

    if (response.selection === 0 && player.hasTag("trimadmin")) {
      withdrawStoredMoney(player);
    } else {
      secondaryMenu(player);
    }
  }).catch(error => {
    console.error(`Error showing primary menu for ${player.name}: ${error.message}`);
    player.sendMessage(
      `§r§8[§3Armor Trim Shop§8] §cError displaying menu: ${error.message}`
    );
  });
}

function withdrawStoredMoney(player) {
  try {
    const stored = db.get("storedMoney") || 0;

    if (stored <= 0) {
      player.sendMessage("§cNo funds in storage!");
      return;
    }

    const moneyObj = getMoneyObjective();
    const current = getScore(player, moneyObj);
    setScore(player, moneyObj, current + stored);

    db.set("storedMoney", 0);
    player.sendMessage(`§aWithdrawn $${stored} from storage!`);
  } catch (error) {
    console.error(`Error withdrawing stored money for ${player.name}: ${error.message}`);
    player.sendMessage(
      `§r§8[§3Armor Trim Shop§8] §cError withdrawing funds: ${error.message}`
    );
  }
}

function secondaryMenu(player) {
  const menu = new ActionFormData()
    .title("§l§3Available Trims")
    .button("Back", "textures/ui/arrow_left");

  shopList[0].items.forEach(trim => {
    menu.button(`§r${trim.name}\n§7Price: $${trim.price}`, trim.icon);
  });

  menu.show(player).then(response => {
    if (response.canceled || response.selection === 0) {
      primaryMenu(player);
      return;
    }

    const selectedTrim = shopList[0].items[response.selection - 1];
    confirmPurchase(player, selectedTrim);
  }).catch(error => {
    console.error(`Error showing secondary menu for ${player.name}: ${error.message}`);
    player.sendMessage(
      `§r§8[§3Armor Trim Shop§8] §cError displaying menu: ${error.message}`
    );
  });
}

function confirmPurchase(player, trim) {
  const moneyObj = getMoneyObjective();
  const balance = getScore(player, moneyObj);

  const form = new ModalFormData()
    .title(`Purchase ${trim.name}`)
    .slider("Quantity", 1, 64, { valueStep: 1, defaultValue: 1 })
    .toggle("Confirm Purchase", { defaultValue: false });

  form.show(player).then(response => {
    if (response.canceled) return;

    const [quantity, confirmed] = response.formValues;
    const totalCost = quantity * trim.price;

    if (confirmed && balance >= totalCost) {
      try {
        setScore(player, moneyObj, balance - totalCost);
        const currentStorage = db.get("storedMoney") || 0;
        db.set("storedMoney", currentStorage + totalCost);

        player.dimension.runCommand(`give "${player.name}" ${trim.itemId} ${quantity}`);
        player.sendMessage(`§aPurchased ${quantity} ${trim.name}!`);
      } catch (error) {
        console.error(`Error processing purchase for ${player.name}: ${error.message}`);
        player.sendMessage(
          `§r§8[§3Armor Trim Shop§8] §cError processing purchase: ${error.message}`
        );
        try {
          setScore(player, moneyObj, balance);
        } catch (rollbackError) {
          console.error(`Error rolling back purchase for ${player.name}: ${rollbackError.message}`);
        }
      }
    } else if (confirmed) {
      player.sendMessage("§cInsufficient funds!");
    }
  }).catch(error => {
    console.error(`Error showing purchase form for ${player.name}: ${error.message}`);
    player.sendMessage(
      `§r§8[§3Armor Trim Shop§8] §cError displaying form: ${error.message}`
    );
  });
}