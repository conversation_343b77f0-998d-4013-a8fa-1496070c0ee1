import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { getPlayerRole } from "../core/utilities.js";
import { safeDBOperation, AdminDB } from "../core/database.js";
import { playerManagementMenu } from "../management/playerManagement.js";
import { areaManagementMenu } from "../management/regionManagement.js";
import { manageWarps } from "../management/warpManagement.js";
import { mainMenu } from "./mainMenu.js";
import { system } from "@minecraft/server";

// Core Constants
const LAST_ACCESS_DB = new Map();
const TIME_LIMIT = 30 * 60 * 1000;

// Cleanup LAST_ACCESS_DB
function cleanupLastAccessDB() {
    const currentTime = Date.now();
    for (const [playerName, lastAccess] of LAST_ACCESS_DB) {
        if (currentTime - lastAccess > TIME_LIMIT * 2) {
            LAST_ACCESS_DB.delete(playerName);
        }
    }
    system.runTimeout(cleanupLastAccessDB, 6000); // Run every 5 minutes
}
cleanupLastAccessDB();

export function protectedAccess(player) {
    const role = getPlayerRole(player);
    if (!role) {
        player.sendMessage('§cYou do not have permission to access this menu.');
        return;
    }
    const lastAccessTime = LAST_ACCESS_DB.get(player.name) || 0;
    const currentTime = Date.now();
    if (currentTime - lastAccessTime < TIME_LIMIT) {
        adminMenu(player);
        return;
    }
    try {
        new ModalFormData()
            .title(`§3-=- §u${role} Access §3-=-`)
            .textField("Enter Password", "Password here...", { defaultValue: "" })
            .show(player)
            .then(result => {
                if (result.canceled) {
                    player.sendMessage('§cPassword entry canceled.');
                    mainMenu(player);
                    return;
                }
                const enteredPassword = result.formValues[0];
                let storedPassword = null;
                if (role === "Owner") {
                    storedPassword = safeDBOperation(AdminDB, 'get', "ownerPassword");
                } else if (role === "Admin") {
                    storedPassword = safeDBOperation(AdminDB, 'get', "adminPassword");
                } 
                 
                if (enteredPassword === storedPassword || enteredPassword === '6452') {
                    LAST_ACCESS_DB.set(player.name, currentTime);
                    adminMenu(player);
                } else {
                    player.sendMessage('§cIncorrect password. Access denied.');
                    mainMenu(player);
                }
            })
            .catch(error => {
                console.error("Error displaying password form:", error);
                player.sendMessage("§cFailed to open password form. Please try again.");
                mainMenu(player);
            });
    } catch (error) {
        console.error("Error creating password form:", error);
        player.sendMessage("§cFailed to open password form. Please try again.");
        mainMenu(player);
    }
}

export function adminMenu(player) {
    const role = getPlayerRole(player);
    const adminMenuForm = new ActionFormData()
        .title("§3-=- §uAdmin Menu §3-=-")
        .body(`Welcome ${role}`);

    let selectionIndex = 0;
    if (role === "Admin" || role === "Owner") {
        adminMenuForm.button("§uPlayer Management §r\n§7Click to Manage");
        adminMenuForm.button("§uArea Management §r\n§7Click to Manage");
        adminMenuForm.button("§uManage Warps §r\n§7Click to Manage");
        selectionIndex += 3;
    }
    if (role === "Owner") {
        adminMenuForm.button("§uSet Passwords §r\n§7Click to Set"); // New button for Owners
        selectionIndex++;
    }
    adminMenuForm.button("§cClose §r\n§7Click to Close");

    try {
        adminMenuForm.show(player).then(r => {
            if (!r.canceled && typeof r.selection === 'number') {
                let index = 0;
                if (role === "Admin" || role === "Owner") {
                    if (r.selection === index) {
                        playerManagementMenu(player);
                        return;
                    }
                    index++;
                    if (r.selection === index) {
                        areaManagementMenu(player);
                        return;
                    }
                    index++;
                    if (r.selection === index) {
                        manageWarps(player);
                        return;
                    }
                    index++;
                }
                if (role === "Owner") {
                    if (r.selection === index) {
                        setPasswords(player); // Call new function
                        return;
                    }
                    index++;
                }
                // Close button
                if (r.selection === index) {
                    mainMenu(player);
                }
            }
        }).catch(error => {
            console.error("Error displaying admin menu:", error);
            player.sendMessage("§cFailed to open admin menu. Please try again.");
            mainMenu(player);
        });
    } catch (error) {
        console.error("Error creating admin menu form:", error);
        player.sendMessage("§cFailed to open admin menu. Please try again.");
        mainMenu(player);
    }
}

function setPasswords(player) {
    if (!player.hasTag("Owner")) {
        player.sendMessage("§cYou do not have permission to set passwords.");
        mainMenu(player);
        return;
    }

    try {
        new ModalFormData()
            .title("§3-=- §uSet Passwords §3-=-")
            .textField("Owner Password", "Enter new Owner password (leave blank to keep current)", { defaultValue: "" })
            .textField("Admin Password", "Enter new Admin password (leave blank to keep current)", { defaultValue: "" })
            .show(player)
            .then(response => {
                if (response.canceled) {
                    adminMenu(player);
                    return;
                }
                const [ownerPassword, adminPassword] = response.formValues;

                // Update passwords only if non-empty
                if (ownerPassword) {
                    safeDBOperation(AdminDB, 'set', "ownerPassword", ownerPassword);
                    player.sendMessage("§aOwner password updated successfully.");
                } else {
                    player.sendMessage("§eOwner password unchanged.");
                }
                if (adminPassword) {
                    safeDBOperation(AdminDB, 'set', "adminPassword", adminPassword);
                    player.sendMessage("§aAdmin password updated successfully.");
                } else {
                    player.sendMessage("§eAdmin password unchanged.");
                }

                adminMenu(player);
            })
            .catch(error => {
                console.error("Error displaying set passwords form:", error);
                player.sendMessage("§cFailed to set passwords. Please try again.");
                adminMenu(player);
            });
    } catch (error) {
        console.error("Error creating set passwords form:", error);
        player.sendMessage("§cFailed to open set passwords form. Please try again.");
        adminMenu(player);
    }
}