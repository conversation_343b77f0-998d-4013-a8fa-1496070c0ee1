import { ActionFormData } from "@minecraft/server-ui";
import { EffectTypes, Player, GameMode } from "@minecraft/server";
import { getPlayerRole } from "../core/utilities.js";
import { protectedAccess } from "./adminMenu.js";
import { warpsMenu } from "../management/warpManagement.js";
import { areaManagementMenu } from "../management/regionManagement.js";
import { regionRequestMenu, adminRegionRequestMenu } from "../management/regionRequests.js";

export function mainMenu(player) {
    if (!(player instanceof Player)) {
        console.error("mainMenu: Invalid player object:", player);
        return;
    }

    const role = getPlayerRole(player);
    const mainMenuForm = new ActionFormData()
        .title("§3-=- §uMain Menu §3-=-")
        .body("Select an option:");

    let selectionIndex = 0;
    if (role === "Owner" || role === "Admin" || role === "Mod" || role === "Ravensthorpe") {
        mainMenuForm.button("§uProtected Access §r\n§7Click to Access");
        selectionIndex++;
    }
    mainMenuForm.button("§uWarps §r\n§7Click to View");
    mainMenuForm.button("§uToggle Invisibility §r\n§7Click to Toggle");
    mainMenuForm.button("§uToggle Night Vision §r\n§7Click to Toggle");
    mainMenuForm.button("§uRequest Region §r\n§7Click to Request");
    if (player.hasTag("Admin") || player.hasTag("Owner")) {
        mainMenuForm.button("§uReview Region Requests §r\n§7Click to Review");
        mainMenuForm.button("§uCreative Mode\n§7Click to enable");
        mainMenuForm.button("§uSurvival Mode\n§7Click to enable");
        mainMenuForm.button("§uSpectator Mode\n§7Click to enable");
    }

    try {
        mainMenuForm.show(player).then(r => {
            if (!r.canceled && typeof r.selection === 'number') {
                let index = 0;
                if (role === "Owner" || role === "Admin" || role === "Mod" || role === "Ravensthorpe") {
                    if (r.selection === index) {
                        protectedAccess(player);
                        return;
                    }
                    index++;
                }
                if (r.selection === index) {
                    warpsMenu(player);
                    return;
                }
                index++;
                if (r.selection === index) {
                    toggleInvisibility(player);
                    return;
                }
                index++;
                if (r.selection === index) {
                    toggleNightVision(player);
                    return;
                }
                index++;
                if (r.selection === index) {
                    regionRequestMenu(player);
                    return;
                }
                index++;
                if (player.hasTag("Admin") || player.hasTag("Owner")) {
                    if (r.selection === index) {
                        adminRegionRequestMenu(player);
                        return;
                    }
                    index++;
                    if (r.selection === index) {
                        setCreativeMode(player);
                        return;
                    }
                    index++;
                    if (r.selection === index) {
                        setSurvivalMode(player);
                        return;
                    }
                    index++;
                    if (r.selection === index) {
                        setSpectatorMode(player);
                        return;
                    }
                }
            }
        }).catch(error => {
            console.error("Error displaying main menu:", error);
            player.sendMessage("§cFailed to open main menu. Please try again.");
        });
    } catch (error) {
        console.error("Error creating main menu form:", error);
        player.sendMessage("§cFailed to open main menu. Please try again.");
    }
}

function toggleInvisibility(player) {
    if (!(player instanceof Player)) {
        console.error("toggleInvisibility: Invalid player object:", player);
        return;
    }
    try {
        const invisibilityEffect = player.getEffect(EffectTypes.get("invisibility"));
        if (invisibilityEffect) {
            player.removeEffect(EffectTypes.get("invisibility"));
            player.sendMessage("§aInvisibility disabled.");
        } else {
            player.addEffect(EffectTypes.get("invisibility"), 999999, { amplifier: 1, showParticles: false });
            player.sendMessage("§aInvisibility enabled.");
        }
        mainMenu(player);
    } catch (error) {
        console.error("Error toggling invisibility:", error);
        player.sendMessage("§cFailed to toggle invisibility. Please try again.");
        mainMenu(player);
    }
}

function toggleNightVision(player) {
    if (!(player instanceof Player)) {
        console.error("toggleNightVision: Invalid player object:", player);
        return;
    }
    try {
        const nightVisionEffect = player.getEffect(EffectTypes.get("night_vision"));
        if (nightVisionEffect) {
            player.removeEffect(EffectTypes.get("night_vision"));
            player.sendMessage("§aNight vision disabled.");
        } else {
            player.addEffect(EffectTypes.get("night_vision"), 999999, { amplifier: 1, showParticles: false });
            player.sendMessage("§aNight vision enabled.");
        }
        mainMenu(player);
    } catch (error) {
        console.error("Error toggling night vision:", error);
        player.sendMessage("§cFailed to toggle night vision. Please try again.");
        mainMenu(player);
    }
}

function setCreativeMode(player) {
    if (!(player instanceof Player)) {
        console.error("setCreativeMode: Invalid player object:", player);
        return;
    }
    try {
        player.setGameMode(GameMode.Creative);
        player.sendMessage("§aCreative mode enabled.");
        mainMenu(player);
    } catch (error) {
        console.error("Error setting creative mode:", error);
        player.sendMessage("§cFailed to set creative mode. Please try again.");
        mainMenu(player);
    }
}

function setSurvivalMode(player) {
    if (!(player instanceof Player)) {
        console.error("setSurvivalMode: Invalid player object:", player);
        return;
    }
    try {
        player.setGameMode(GameMode.Survival);
        player.sendMessage("§aSurvival mode enabled.");
        mainMenu(player);
    } catch (error) {
        console.error("Error setting survival mode:", error);
        player.sendMessage("§cFailed to set survival mode. Please try again.");
        mainMenu(player);
    }
}

function setSpectatorMode(player) {
    if (!(player instanceof Player)) {
        console.error("setSpectatorMode: Invalid player object:", player);
        return;
    }
    try {
        player.setGameMode(GameMode.Spectator);
        player.sendMessage("§aSpectator mode enabled.");
        mainMenu(player);
    } catch (error) {
        console.error("Error setting spectator mode:", error);
        player.sendMessage("§cFailed to set spectator mode. Please try again.");
        mainMenu(player);
    }
}
