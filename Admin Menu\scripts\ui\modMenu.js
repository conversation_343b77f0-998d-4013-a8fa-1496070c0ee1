import { world, system, Player, GameMode } from "@minecraft/server";
import { ModalFormData, ActionFormData } from "@minecraft/server-ui";

// Debug: Verify imports
console.log("ActionFormData:", typeof ActionFormData);
console.log("ModalFormData:", typeof ModalFormData);

// Listen for chat messages
world.beforeEvents.chatSend.subscribe((event) => {
  const player = event.sender;
  const message = event.message.trim();

  // Check if the player has the Mod tag and typed -mod
  if (message === "-mod" && player.hasTag("Mod")) {
    event.cancel = true; // Prevent the message from appearing in chat

    // Add a 3-second delay before showing the form
    system.runTimeout(() => {
      showModMenu(player);
    }, 60); // 20 ticks = 1 second, so 60 ticks = 3 seconds
  }
});

// Function to show the main mod menu
function showModMenu(player) {
  if (!ActionFormData) {
    console.error("ActionFormData is not defined. Check @minecraft/server-ui import.");
    player.sendMessage("Error: Mod menu unavailable due to a script issue.");
    return;
  }

  try {
    const form = new ActionFormData()
      .title("Mod Menu")
      .body("Select an option:")
      .button("Commands")
      .button("Spectator")
      .button("Survival");

    if (typeof form.show !== "function") {
      console.error("form.show is not a function. Check @minecraft/server-ui version.");
      player.sendMessage("Error: Mod menu unavailable due to a script issue.");
      return;
    }

    form.show(player).then((response) => {
      if (response.canceled) {
        console.log("Mod menu canceled by:", player.name);
        return;
      }

      const selection = response.selection;
      if (selection === 0) {
        showCommandsMenu(player);
      } else if (selection === 1) {
        try {
          player.setGameMode(GameMode.Spectator); // Updated to use setGameMode
          console.log("Set", player.name, "to spectator mode");
        } catch (error) {
          console.error("Spectator mode error:", error);
          player.sendMessage("Failed to switch to spectator mode.");
        }
      } else if (selection === 2) {
        try {
          player.setGameMode(GameMode.Survival); // Updated to use setGameMode
          console.log("Set", player.name, "to survival mode");
        } catch (error) {
          console.error("Survival mode error:", error);
          player.sendMessage("Failed to switch to survival mode.");
        }
      }
    }).catch((error) => {
      console.error("Error in mod menu form.show:", error);
      player.sendMessage("Error: Failed to display mod menu.");
    });
  } catch (error) {
    console.error("Error creating ActionFormData:", error);
    player.sendMessage("Error: Mod menu unavailable due to a script issue.");
  }
}

// Function to show the commands sub-menu
function showCommandsMenu(player) {
  if (!ActionFormData) {
    console.error("ActionFormData is not defined. Check @minecraft/server-ui import.");
    player.sendMessage("Error: Commands menu unavailable due to a script issue.");
    return;
  }

  try {
    const form = new ActionFormData()
      .title("Commands Menu")
      .body("Select a command:")
      .button("Teleport To")
      .button("Teleport From");

    form.show(player).then((response) => {
      if (response.canceled) {
        console.log("Commands menu canceled by:", player.name);
        return;
      }

      const selection = response.selection;
      if (selection === 0) {
        showTeleportToMenu(player);
      } else if (selection === 1) {
        showTeleportFromMenu(player);
      }
    }).catch((error) => {
      console.error("Error in commands form.show:", error);
      player.sendMessage("Error: Failed to display commands menu.");
    });
  } catch (error) {
    console.error("Error creating commands ActionFormData:", error);
    player.sendMessage("Error: Commands menu unavailable due to a script issue.");
  }
}

// Function to show the teleport-to menu
function showTeleportToMenu(player) {
  const players = [...world.getPlayers()].filter((p) => !p.hasTag("Owner") && p !== player);
  const playerNames = players.map((p) => p.name);

  if (playerNames.length === 0) {
    console.log("No eligible players for teleport-to:", player.name);
    player.sendMessage("No eligible players to teleport to.");
    return;
  }

  if (!ModalFormData) {
    console.error("ModalFormData is not defined. Check @minecraft/server-ui import.");
    player.sendMessage("Error: Teleport menu unavailable due to a script issue.");
    return;
  }

  try {
    const form = new ModalFormData()
      .title("Teleport To")
      .dropdown("Select a player:", playerNames);

    console.log("Showing teleport-to form for player:", player.name, "Players:", playerNames);

    form.show(player).then((response) => {
      if (response.canceled) {
        console.log("Teleport-to form canceled by:", player.name);
        return;
      }

      const selectedIndex = response.formValues[0];
      console.log("Selected index:", selectedIndex, "Player names:", playerNames);

      if (typeof selectedIndex !== "number" || selectedIndex < 0 || selectedIndex >= players.length) {
        console.error("Invalid selected index:", selectedIndex);
        player.sendMessage("Error: Invalid player selection.");
        return;
      }

      const targetPlayer = players[selectedIndex];
      if (!targetPlayer) {
        console.error("Target player not found for index:", selectedIndex);
        player.sendMessage("Error: Selected player not found.");
        return;
      }

      console.log("Preparing to teleport", player.name, "to", targetPlayer.name);

      // Always set to spectator mode
      try {
        player.setGameMode(GameMode.Spectator); // Updated to use setGameMode
        console.log("Set", player.name, "to spectator mode");
      } catch (error) {
        console.error("Error setting spectator mode:", error);
        player.sendMessage("Error: Failed to switch to spectator mode.");
        return;
      }

      // Teleport to the target player
      try {
        player.teleport(targetPlayer.location); // Updated to use teleport method
        console.log("Successfully teleported", player.name, "to", targetPlayer.name);
        player.sendMessage(`Teleported to ${targetPlayer.name}`);
      } catch (error) {
        console.error("Teleportation error:", error);
        player.sendMessage("Error: Failed to teleport to player.");
      }
    }).catch((error) => {
      console.error("Error in teleport-to form.show:", error);
      player.sendMessage("Error: Failed to process teleport request.");
    });
  } catch (error) {
    console.error("Error creating teleport-to ModalFormData:", error);
    player.sendMessage("Error: Teleport menu unavailable due to a script issue.");
  }
}

// Function to show the teleport-from menu
function showTeleportFromMenu(player) {
  const players = [...world.getPlayers()].filter((p) => !p.hasTag("Owner") && p !== player);
  const playerNames = players.map((p) => p.name);

  if (playerNames.length === 0) {
    console.log("No eligible players for teleport-from:", player.name);
    player.sendMessage("No eligible players to teleport to you.");
    return;
  }

  if (!ModalFormData) {
    console.error("ModalFormData is not defined. Check @minecraft/server-ui import.");
    player.sendMessage("Error: Teleport menu unavailable due to a script issue.");
    return;
  }

  try {
    const form = new ModalFormData()
      .title("Teleport From")
      .dropdown("Select a player:", playerNames);

    console.log("Showing teleport-from form for player:", player.name, "Players:", playerNames);

    form.show(player).then((response) => {
      if (response.canceled) {
        console.log("Teleport-from form canceled by:", player.name);
        return;
      }

      const selectedIndex = response.formValues[0];
      console.log("Selected index:", selectedIndex, "Player names:", playerNames);

      if (typeof selectedIndex !== "number" || selectedIndex < 0 || selectedIndex >= players.length) {
        console.error("Invalid selected index:", selectedIndex);
        player.sendMessage("Error: Invalid player selection.");
        return;
      }

      const targetPlayer = players[selectedIndex];
      if (!targetPlayer) {
        console.error("Target player not found for index:", selectedIndex);
        player.sendMessage("Error: Selected player not found.");
        return;
      }

      console.log("Teleporting", targetPlayer.name, "to", player.name);

      // Teleport the target player to the mod
      try {
        targetPlayer.teleport(player.location); // Updated to use teleport method
        console.log("Successfully teleported", targetPlayer.name, "to", player.name);
        player.sendMessage(`Teleported ${targetPlayer.name} to you`);
      } catch (error) {
        console.error("Teleportation error:", error);
        player.sendMessage("Error: Failed to teleport player.");
      }
    }).catch((error) => {
      console.error("Error in teleport-from form.show:", error);
      player.sendMessage("Error: Failed to process teleport request.");
    });
  } catch (error) {
    console.error("Error creating teleport-from ModalFormData:", error);
    player.sendMessage("Error: Teleport menu unavailable due to a script issue.");
  }
}