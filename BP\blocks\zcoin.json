{"format_version": "1.20.40", "minecraft:block": {"description": {"identifier": "zombie:zcoin_block", "menu_category": {"category": "none"}, "traits": {"minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}, "minecraft:placement_position": {"enabled_states": ["minecraft:block_face"]}}}, "components": {"tag:custom": {}, "minecraft:loot": "loot_tables/zcoin.json", "minecraft:light_dampening": 0, "minecraft:material_instances": {"*": {"texture": "coins", "render_method": "alpha_test"}}, "minecraft:map_color": "#FBBF0A", "minecraft:destructible_by_mining": {"seconds_to_destroy": 0.5}, "minecraft:display_name": "Coin Block", "minecraft:destructible_by_explosion": {"explosion_resistance": 13}, "minecraft:geometry": "geometry.zcoin"}, "permutations": [{"condition": "query.block_state('minecraft:block_face') == 'north'", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}, "minecraft:geometry": "geometry.zcoin_wall", "minecraft:selection_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}, "minecraft:collision_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}}}, {"condition": "query.block_state('minecraft:block_face') == 'south'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}, "minecraft:geometry": "geometry.zcoin_wall", "minecraft:selection_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}, "minecraft:collision_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}}}, {"condition": "query.block_state('minecraft:block_face') == 'east'", "components": {"minecraft:transformation": {"rotation": [0, -90, 0]}, "minecraft:geometry": "geometry.zcoin_wall", "minecraft:selection_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}, "minecraft:collision_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}}}, {"condition": "query.block_state('minecraft:block_face') == 'west'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}, "minecraft:geometry": "geometry.zcoin_wall", "minecraft:selection_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}, "minecraft:collision_box": {"origin": [-8, 0, 6], "size": [16, 16, 2]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'north' && query.block_state('minecraft:block_face') == 'down'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}, "minecraft:collision_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}, "minecraft:geometry": "geometry.zcoin_roof"}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'south' && query.block_state('minecraft:block_face') == 'down'", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}, "minecraft:geometry": "geometry.zcoin_roof", "minecraft:collision_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'east' && query.block_state('minecraft:block_face') == 'down'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}, "minecraft:geometry": "geometry.zcoin_roof", "minecraft:collision_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'west' && query.block_state('minecraft:block_face') == 'down'", "components": {"minecraft:transformation": {"rotation": [0, 270, 0]}, "minecraft:geometry": "geometry.zcoin_roof", "minecraft:collision_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 14, -8], "size": [16, 2, 16]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'north' && query.block_state('minecraft:block_face') == 'up'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}, "minecraft:geometry": "geometry.zcoin", "minecraft:collision_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'south' && query.block_state('minecraft:block_face') == 'up'", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}, "minecraft:geometry": "geometry.zcoin", "minecraft:collision_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'east' && query.block_state('minecraft:block_face') == 'up'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}, "minecraft:geometry": "geometry.zcoin", "minecraft:collision_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'west' && query.block_state('minecraft:block_face') == 'up'", "components": {"minecraft:transformation": {"rotation": [0, 270, 0]}, "minecraft:geometry": "geometry.zcoin", "minecraft:collision_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}, "minecraft:selection_box": {"origin": [-8, 0, -8], "size": [16, 2, 16]}}}]}}