import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { adminSettingsMenu } from "../admin_menu";
import { manageCommandPrompt } from "./commandprompt_settings";
import { tp_settings, player_settings, stores_settings, gamemode_settings } from "./rank_settings";

export function adminPlayerManagement(player) {
    const scoreboard = world.scoreboard.getObjective("admin");
    if (!scoreboard) {
        player.sendMessage("§cNo admin scoreboard found.");
        return;
    }

    const ownerName = `${player.name}`;

    const fakeAdmins = scoreboard.getParticipants()
        .filter(participant => participant.displayName.startsWith("admin_"))
        .map(participant => translateDisplayName(participant.displayName.replace("admin_", "")));

    const adminPlayersForm = new ActionFormData()
        .title("§4Admin Player Management")
        .body("Manage players with admin privileges.")
        .button("§0Add Admin", "textures/ui/dressing_room_customization")
        .button("§0Add/Edit Ranks", "textures/ui/anvil_icon")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover")
        .button("§l§cExit", "textures/ui/crossout");

    const displayedAdmins = [];
    fakeAdmins.forEach(admin => {
        if (admin !== `${ownerName}_owner`) {
            adminPlayersForm.button(admin);
            displayedAdmins.push(admin);
        }
    });

    adminPlayersForm.show(player).then(response => {
        if (response.canceled) return;

        if (response.selection === 0) {
            addAdmin(player, ownerName);
            return;
        }

        if (response.selection === 1) {
            manageRanks(player);
            return;
        }

        if (response.selection === 2) {
            adminSettingsMenu(player);
            return;
        }

        if (response.selection === 3) {
            player.sendMessage("§aExiting menu...");
            return;
        }

        const selectedAdminIndex = response.selection - 4;
        if (selectedAdminIndex >= 0 && selectedAdminIndex < displayedAdmins.length) {
            const selectedAdmin = displayedAdmins[selectedAdminIndex];
            manageAdminPlayer(player, selectedAdmin, ownerName);
        }
    }).catch(err => {
        console.error("Error in adminPlayerManagement:", err);
        player.sendMessage("§cAn error occurred while opening the admin menu.");
    });
}

function manageRanks(player) {
    const scoreboard = world.scoreboard.getObjective("admin");
    if (!scoreboard) {
        player.sendMessage("§cNo admin scoreboard found.");
        return;
    }

    const ranks = scoreboard.getParticipants()
        .filter(participant => participant.displayName.startsWith("rank_"))
        .map(participant => ({
            name: participant.displayName.replace("rank_", ""),
            score: 0 // Default rank score
        }));

    const manageRanksForm = new ActionFormData()
        .title("§4Manage Ranks")
        .body("Add or edit ranks.")
        .button("§0Add Rank", "textures/ui/anvil_icon");

    ranks.forEach(rank => {
        manageRanksForm.button(`§0${translateDisplayName(rank.name)}`, "textures/ui/bubble");
    });

    manageRanksForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    manageRanksForm.show(player).then(response => {
        if (response.canceled) {
            adminPlayerManagement(player);
            return;
        }

        if (response.selection === 0) {
            addRank(player);
            return;
        }

        const selectedRankIndex = response.selection - 1;
        if (selectedRankIndex >= 0 && selectedRankIndex < ranks.length) {
            const selectedRank = ranks[selectedRankIndex];
            addSettingsMenu(player, selectedRank);
        } else {
            adminPlayerManagement(player);
        }
    }).catch(err => {
        console.error("Error in manageRanks:", err);
        player.sendMessage("§cAn error occurred while managing ranks.");
    });
}

export function addSettingsMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const settings = [
        "commandprompt",
        "tp_settings",
        "player_settings",
        "stores_settings",
        "gamemode_settings"
    ];

    const rankScoreboard = world.scoreboard.getObjective(rankScoreboardName);
    if (!rankScoreboard) {
        player.sendMessage("§cNo scoreboard found for this rank.");
        return;
    }

    // Filter to only show enabled settings
    const enabledSettings = settings.filter(setting => {
        const participant = rankScoreboard.getParticipants().find(p => p.displayName === setting);
        return participant && rankScoreboard.getScore(participant) === 1;
    });

    const addSettingsForm = new ActionFormData()
        .title(`§4Manage Rank: ${translateDisplayName(rank.name)}`)
        .body("Manage the enabled settings for this rank.")
        .button("§0Add Settings", "textures/ui/plus");

    // Add buttons for enabled settings
    enabledSettings.forEach(setting => {
        addSettingsForm.button(setting.replace(/_/g, " "), "textures/ui/bubble");
    });

    addSettingsForm
        .button("§cRemove Rank", "textures/ui/cancel")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    addSettingsForm.show(player).then(response => {
        // If canceled or "Back" (the last button)
        if (response.canceled || response.selection === enabledSettings.length + 2) {
            manageRanks(player);
            return;
        }

        if (response.selection === 0) {
            rankSettings(player, rank);
            return;
        }

        // If the second-to-last button was chosen => "Remove Rank"
        if (response.selection === enabledSettings.length + 1) {
            confirmRemoveRank(player, rank);
            return;
        }

        // Otherwise, one of the currently enabled settings was chosen
        const selectedIndex = response.selection - 1;
        if (selectedIndex >= 0 && selectedIndex < enabledSettings.length) {
            const selectedSetting = enabledSettings[selectedIndex];
            runSettingFunction(selectedSetting, player, rank);
        }
    }).catch(err => {
        console.error("Error in addSettingsMenu:", err);
        player.sendMessage("§cAn error occurred while managing rank settings.");
    });
}

function runSettingFunction(setting, player, rank) {
    const settingFunctions = {
        commandprompt: manageCommandPrompt,
        tp_settings: tp_settings,
        player_settings: player_settings,
        stores_settings: stores_settings,
        gamemode_settings: gamemode_settings
    };

    const func = settingFunctions[setting];
    if (func) {
        func(player, rank);
    } else {
        player.sendMessage(`§cNo function implemented for setting: ${setting}`);
    }
}

function confirmRemoveRank(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;

    const confirmForm = new ActionFormData()
        .title(`§4Confirm Rank Removal`)
        .body(`Are you sure you want to remove the rank '${translateDisplayName(rank.name)}'?\nThis will delete all settings and the rank itself.`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            addSettingsMenu(player, rank);
            return;
        }

        // Remove the fake player from the admin scoreboard
        // CHANGED: wrap rank name in quotes
        player.runCommandAsync(`scoreboard players reset "rank_${rank.name}" admin`).catch(err => {
            console.error(`Error removing rank from admin scoreboard: ${err}`);
        });

        // Remove the rank-specific scoreboard
        // CHANGED: wrap scoreboard name in quotes
        player.runCommandAsync(`scoreboard objectives remove "rank_${rank.name}"`).then(() => {
            player.sendMessage(`§aRank '${translateDisplayName(rank.name)}' has been successfully removed.`);
            manageRanks(player);
        }).catch(err => {
            console.error(`Error removing rank scoreboard: ${err}`);
            player.sendMessage(`§cFailed to remove rank '${translateDisplayName(rank.name)}': ${err}`);
        });
    }).catch(err => {
        console.error("Error in confirmRemoveRank:", err);
        player.sendMessage("§cAn error occurred while confirming rank removal.");
    });
}

function rankSettings(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const settings = [
        "commandprompt",
        "tp_settings",
        "player_settings",
        "stores_settings",
        "gamemode_settings"
    ];

    // Ensure scoreboard objective exists for rank
    const rankScoreboard =
        world.scoreboard.getObjective(rankScoreboardName) ||
        world.scoreboard.addObjective(rankScoreboardName, "Rank Settings");

    const rankSettingsForm = new ActionFormData()
        .title(`§4Rank Settings: ${translateDisplayName(rank.name)}`)
        .body("Toggle settings for this rank.");

    settings.forEach(setting => {
        const participant = rankScoreboard.getParticipants().find(p => p.displayName === setting);
        const currentScore = participant ? rankScoreboard.getScore(participant) : 0;
        const status = currentScore === 1 ? "§a(On)" : "§c(Off)";
        rankSettingsForm.button(`${setting.replace(/_/g, " ")} ${status}`);
    });

    rankSettingsForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    rankSettingsForm.show(player).then((response) => {
        if (response.canceled) return;

        if (response.selection < settings.length) {
            const selectedSetting = settings[response.selection];
            const participant = rankScoreboard.getParticipants().find(p => p.displayName === selectedSetting);
            const currentScore = participant ? rankScoreboard.getScore(participant) : 0;
            const newScore = currentScore === 1 ? 0 : 1;

            rankScoreboard.setScore(selectedSetting, newScore);

            player.sendMessage(`Setting '${selectedSetting.replace(/_/g, " ")}' is now ${newScore === 1 ? "enabled" : "disabled"}.`);

            // Refresh the menu
            rankSettings(player, rank);
        } else {
            addSettingsMenu(player, rank);
        }
    }).catch(err => {
        console.error("Error in rankSettings:", err);
        player.sendMessage("§cAn error occurred while managing rank settings.");
    });
}

function addRank(player) {
    const addRankForm = new ModalFormData()
        .title("§4Add Rank")
        .textField("Enter rank name:", "Rank name")
        .textField("Set rank level (cannot be 0):", "Rank level");

    addRankForm.show(player).then(response => {
        if (response.canceled) {
            manageRanks(player);
            return;
        }

        const rawRankName = response.formValues[0];
        const rankName = sanitizeInput(rawRankName);
        const rankLevel = parseInt(response.formValues[1], 10);

        if (!rankName || isNaN(rankLevel) || rankLevel === 0) {
            player.sendMessage("§cInvalid rank name or level. Rank level cannot be 0.");
            manageRanks(player);
            return;
        }

        const fakePlayerName = `rank_${rankName}`;
        const rankScoreboardName = `rank_${rankName}`;
        const scoreboard = world.scoreboard.getObjective("admin");

        if (scoreboard.getParticipants().some(p => p.displayName === fakePlayerName)) {
            player.sendMessage("§cA rank with that name already exists.");
            manageRanks(player);
            return;
        }

        // CHANGED: wrap "rank_{rankName}"
        player.runCommandAsync(`scoreboard players set "rank_${rankName}" admin ${rankLevel}`).then(() => {
            // CHANGED: wrap scoreboard name in quotes
            player.runCommandAsync(`scoreboard objectives add "rank_${rankName}" dummy`).then(() => {
                player.sendMessage(`§aRank '${translateDisplayName(rankName)}' added with level ${rankLevel}.`);
                manageRanks(player);
            }).catch(err => {
                player.sendMessage(`§cFailed to create scoreboard for rank '${translateDisplayName(rankName)}': ${err}`);
            });
        }).catch(err => {
            player.sendMessage(`§cFailed to add rank: ${err}`);
        });
    }).catch(err => {
        console.error("Error in addRank:", err);
        player.sendMessage("§cAn error occurred while adding a rank.");
    });
}

function addAdmin(player) {
    const scoreboard = world.scoreboard.getObjective("admin");

    // Gather all online players who are not already admins or the owner
    const onlinePlayers = world.getPlayers().filter(p => {
        const adminEntry = `admin_${p.name}`;
        const ownerEntry = `admin_${p.name}_owner`;

        const isAdmin = scoreboard.getParticipants().some(participant => participant.displayName === adminEntry);
        const isOwner = scoreboard.getParticipants().some(participant => participant.displayName === ownerEntry);

        return !isAdmin && !isOwner;
    });

    if (onlinePlayers.length === 0) {
        player.sendMessage("§cNo eligible players to add as admin.");
        adminPlayerManagement(player);
        return;
    }

    const addAdminForm = new ActionFormData()
        .title("§4Add Admin")
        .body("Select a player to grant admin privileges:");

    onlinePlayers.forEach(p => addAdminForm.button(p.name));

    addAdminForm.show(player).then(response => {
        if (response.canceled) {
            adminPlayerManagement(player);
            return;
        }

        const selectedPlayer = onlinePlayers[response.selection];

        const confirmForm = new ActionFormData()
            .title("§4Confirm Admin Addition")
            .body(`Are you sure you want to make ${selectedPlayer.name} an admin?`)
            .button("§aYes", "textures/ui/check")
            .button("§cNo", "textures/ui/crossout");

        confirmForm.show(player).then(confirmResponse => {
            if (confirmResponse.canceled || confirmResponse.selection === 1) {
                adminPlayerManagement(player);
                return;
            }

            // CHANGED: wrap "admin_{selectedPlayer.name}"
            player.runCommandAsync(`scoreboard players set "admin_${selectedPlayer.name}" admin 1`).then(() => {
                player.sendMessage(`§a${selectedPlayer.name} has been added as an admin.`);
                selectRankForAdmin(player, selectedPlayer);
            }).catch(err => {
                player.sendMessage(`§cFailed to add ${selectedPlayer.name} as admin: ${err}`);
            });
        });
    }).catch(err => {
        console.error("Error in addAdmin:", err);
        player.sendMessage("§cAn error occurred while adding an admin.");
    });
}

function selectRankForAdmin(player, selectedPlayer) {
    const scoreboard = world.scoreboard.getObjective("admin");
    const ranks = scoreboard.getParticipants()
        .filter(participant => participant.displayName.startsWith("rank_"))
        .map(participant => participant.displayName.replace("rank_", ""));

    const rankForm = new ActionFormData()
        .title("§4Assign Rank")
        .body("Select a rank for the new admin:")
        .button("Trusted Admin (Full Permissions)");

    ranks.forEach(rank => rankForm.button(rank));

    rankForm.show(player).then(response => {
        if (response.canceled) {
            adminPlayerManagement(player);
            return;
        }

        if (response.selection === 0) {
            confirmTrustedAdmin(player, selectedPlayer);
            return;
        }

        const selectedRank = ranks[response.selection - 1];
        confirmRankAssignment(player, selectedPlayer, selectedRank);
    }).catch(err => {
        console.error("Error in selectRankForAdmin:", err);
        player.sendMessage("§cAn error occurred while selecting a rank.");
    });
}

function confirmTrustedAdmin(player, selectedPlayer) {
    const confirmForm = new ActionFormData()
        .title("§4Trusted Admin Warning")
        .body("Giving this rank allows the admin full access to all settings, equivalent to the owner. Do you wish to proceed?")
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            selectRankForAdmin(player, selectedPlayer);
            return;
        }

        // CHANGED: wrap "admin_{selectedPlayer.name}"
        player.runCommandAsync(`scoreboard players set "admin_${selectedPlayer.name}" admin 0`).then(() => {
            player.sendMessage(`§a${selectedPlayer.name} is now a Trusted Admin.`);
            adminPlayerManagement(player);
        }).catch(err => {
            player.sendMessage("§cFailed to assign Trusted Admin rank.");
        });
    }).catch(err => {
        console.error("Error in confirmTrustedAdmin:", err);
        player.sendMessage("§cAn error occurred while assigning Trusted Admin rank.");
    });
}

function confirmRankAssignment(player, selectedPlayer, selectedRank) {
    const participants = world.scoreboard.getObjective("admin").getParticipants();
    const rankParticipant = participants.find(p => p.displayName === `rank_${selectedRank}`);
    const rankScore = rankParticipant ? rankParticipant.score : 1; // default if not found

    const confirmForm = new ActionFormData()
        .title(`§4Assign Rank: ${selectedRank}`)
        .body(`Are you sure you want to assign the rank '${selectedRank}' to ${selectedPlayer.name}?`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            selectRankForAdmin(player, selectedPlayer);
            return;
        }

        // CHANGED: wrap "admin_{selectedPlayer.name}"
        player.runCommandAsync(`scoreboard players set "admin_${selectedPlayer.name}" admin ${rankScore}`).then(() => {
            player.sendMessage(`§a${selectedPlayer.name} has been assigned the rank '${selectedRank}'.`);
            adminPlayerManagement(player);
        }).catch(err => {
            player.sendMessage(`§cFailed to assign rank '${selectedRank}'.`);
        });
    }).catch(err => {
        console.error("Error in confirmRankAssignment:", err);
        player.sendMessage("§cAn error occurred while assigning the rank.");
    });
}

function manageAdminPlayer(player, selectedAdmin, ownerName) {
    const form = new ActionFormData()
        .title(`§4Manage Admin: ${selectedAdmin}`)
        .body("What would you like to do?\n§4Warning to change ranks they must be on")
        .button("§aChange Rank", "textures/ui/change")
        .button("§cRemove Admin", "textures/ui/trash_icon")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then(response => {
        if (response.canceled) {
            adminPlayerManagement(player);
            return;
        }

        if (response.selection === 0) {
            selectRankForAdmin(player, { name: selectedAdmin });
            return;
        }

        if (response.selection === 1) {
            confirmRemoveAdmin(player, selectedAdmin);
            return;
        }

        adminPlayerManagement(player);
    }).catch(err => {
        console.error("Error in manageAdminPlayer:", err);
        player.sendMessage("§cAn error occurred while managing the admin.");
    });
}

function confirmRemoveAdmin(player, selectedAdmin) {
    const confirmForm = new ActionFormData()
        .title(`§4Remove Admin: ${selectedAdmin}`)
        .body(`Are you sure you want to remove ${selectedAdmin} as an admin? This action cannot be undone.`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            manageAdminPlayer(player, selectedAdmin, null);
            return;
        }

        // CHANGED: wrap "admin_{selectedAdmin}"
        player.runCommandAsync(`scoreboard players reset "admin_${selectedAdmin}" admin`).then(() => {
            player.sendMessage(`§a${selectedAdmin} has been removed as an admin.`);
            adminPlayerManagement(player);
        }).catch(err => {
            player.sendMessage(`§cFailed to remove ${selectedAdmin} as an admin.`);
        });
    }).catch(err => {
        console.error("Error in confirmRemoveAdmin:", err);
        player.sendMessage("§cAn error occurred while removing the admin.");
    });
}

function sanitizeInput(input) {
    return input.replace(/§/g, "¤").replace(/&/g, "¦").replace(/ /g, "_");
}

function translateDisplayName(displayName) {
    return displayName
        .replace(/¤/g, "§")
        .replace(/¦/g, "&")
        .replace(/_/g, " ");
}
