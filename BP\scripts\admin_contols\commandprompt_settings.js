import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { addSettingsMenu } from "./admin_player_managment";

export function manageCommandPrompt(player, rank) {
    const commandPromptForm = new ActionFormData()
        .title(`§4Manage Command Prompt: ${translateDisplayName(rank.name)}`)
        .body("Manage exact commands and keywords for this rank to ban.")
        .button("§0Add Exact Command", "textures/ui/plus")
        .button("§0Add Keyword", "textures/ui/plus")
        .button("§cRemove Command/Keyword", "textures/ui/minus")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    commandPromptForm.show(player).then(response => {
        if (response.canceled) {
            addSettingsMenu(player, rank);
            return;
        }

        if (response.selection === 0) {
            addExactCommand(player, rank);
            return;
        }

        if (response.selection === 1) {
            addKeyword(player, rank);
            return;
        }

        if (response.selection === 2) {
            removeCommandOrKeyword(player, rank);
            return;
        }

        if (response.selection === 3) {
            addSettingsMenu(player, rank);
            return;
        }
    }).catch(err => {
        console.error("Error in manageCommandPrompt:", err);
        player.sendMessage("§cAn error occurred while managing commands.");
    });
}

function removeCommandOrKeyword(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const rankScoreboard = world.scoreboard.getObjective(rankScoreboardName);

    if (!rankScoreboard) {
        player.sendMessage("§cNo scoreboard found for this rank.");
        return;
    }

    // Fetch all commands and keywords
    const participants = rankScoreboard.getParticipants()
        .map(participant => participant.displayName)
        .filter(name => name.startsWith("commandprompt_") || name.startsWith("commandkey_"));

    if (participants.length === 0) {
        player.sendMessage("§cNo commands or keywords to remove.");
        manageCommandPrompt(player, rank);
        return;
    }

    const removeForm = new ActionFormData()
        .title(`§4Remove Command/Keyword`)
        .body("Select a command or keyword to remove:");

    participants.forEach(name => {
        const displayName = decodeCommandString(name.replace(/^command(prompt|key)_/, ""));
        removeForm.button(displayName, "textures/ui/minus");
    });

    removeForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    removeForm.show(player).then(response => {
        if (response.canceled || response.selection === participants.length) {
            manageCommandPrompt(player, rank);
            return;
        }

        const selectedParticipant = participants[response.selection];
        confirmRemoveCommandOrKeyword(player, rank, selectedParticipant);
    }).catch(err => {
        console.error("Error in removeCommandOrKeyword:", err);
        player.sendMessage("§cAn error occurred while displaying commands and keywords.");
    });
}

function confirmRemoveCommandOrKeyword(player, rank, fakePlayerName) {
    const rankScoreboardName = `rank_${rank.name}`;
    const displayName = decodeCommandString(fakePlayerName.replace(/^command(prompt|key)_/, ""));

    const confirmForm = new ActionFormData()
        .title(`§4Confirm Removal`)
        .body(`Are you sure you want to remove '${displayName}'?\nThis action cannot be undone.`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            removeCommandOrKeyword(player, rank);
            return;
        }

        // Remove the fake player from the scoreboard
        player.runCommandAsync(`scoreboard players reset ${fakePlayerName} ${rankScoreboardName}`).then(() => {
            player.sendMessage(`§aRemoved '${displayName}' successfully.`);
            removeCommandOrKeyword(player, rank);
        }).catch(err => {
            console.error("Error removing command/keyword:", err);
            player.sendMessage(`§cFailed to remove '${displayName}'.`);
        });
    }).catch(err => {
        console.error("Error in confirmRemoveCommandOrKeyword:", err);
        player.sendMessage("§cAn error occurred while confirming removal.");
    });
}

function addExactCommand(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const prefix = "commandprompt_";

    const addCommandForm = new ModalFormData()
        .title("§4Add Exact Command")
        .textField("Enter the exact command to block:", "e.g., kill @e");

    addCommandForm.show(player).then(response => {
        if (response.canceled) {
            manageCommandPrompt(player, rank);
            return;
        }

        const commandToBlock = response.formValues[0].trim();
        if (!commandToBlock) {
            player.sendMessage("§cInvalid command. Please try again.");
            manageCommandPrompt(player, rank);
            return;
        }

        // Encode the command to make it safe for the scoreboard
        const encodedCommand = encodeCommandString(commandToBlock);
        const fakePlayerName = `${prefix}${encodedCommand}`;

        player.runCommandAsync(`scoreboard players set ${fakePlayerName} ${rankScoreboardName} 1`).then(() => {
            player.sendMessage(`§aBlocked exact command: ${commandToBlock}`);
            manageCommandPrompt(player, rank);
        }).catch(err => {
            console.error("Error adding exact command:", err);
            player.sendMessage("§cFailed to block the exact command.");
        });
    }).catch(err => {
        console.error("Error in addExactCommand:", err);
        player.sendMessage("§cAn error occurred while adding an exact command.");
    });
}

function addKeyword(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const prefix = "commandkey_";

    const addKeywordForm = new ModalFormData()
        .title("§4Add Keyword")
        .textField("Enter the keyword to block:", "e.g., gamemode");

    addKeywordForm.show(player).then(response => {
        if (response.canceled) {
            manageCommandPrompt(player, rank);
            return;
        }

        const keywordToBlock = response.formValues[0].trim();
        if (!keywordToBlock) {
            player.sendMessage("§cInvalid keyword. Please try again.");
            manageCommandPrompt(player, rank);
            return;
        }

        // Encode the keyword to make it safe for the scoreboard
        const encodedKeyword = encodeCommandString(keywordToBlock);
        const fakePlayerName = `${prefix}${encodedKeyword}`;

        player.runCommandAsync(`scoreboard players set ${fakePlayerName} ${rankScoreboardName} 1`).then(() => {
            player.sendMessage(`§aBlocked keyword: ${keywordToBlock}`);
            manageCommandPrompt(player, rank);
        }).catch(err => {
            console.error("Error adding keyword:", err);
            player.sendMessage("§cFailed to block the keyword.");
        });
    }).catch(err => {
        console.error("Error in addKeyword:", err);
        player.sendMessage("§cAn error occurred while adding a keyword.");
    });
}

function encodeCommandString(command) {
    return command
        .replace(/@/g, "_at_")
        .replace(/ /g, "_space_")
        .replace(/\//g, "_slash_")
        .replace(/:/g, "_colon_")
        .replace(/\./g, "_dot_");
}

function decodeCommandString(encodedCommand) {
    return encodedCommand
        .replace(/_at_/g, "@")
        .replace(/_space_/g, " ")
        .replace(/_slash_/g, "/")
        .replace(/_colon_/g, ":")
        .replace(/_dot_/g, ".");
}

function translateDisplayName(displayName) {
    return displayName.replace(/¤/g, "§").replace(/¦/g, "&").replace(/_/g, " ");
}
