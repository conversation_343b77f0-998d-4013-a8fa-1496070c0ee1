import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { world } from "@minecraft/server";

export function onePlayerSleep(player, economyName, formattedName) {
    const listForm = new ActionFormData()
        .title("List Display Options")
        .body(`How do you want "${economyName}" to be sorted in the list?`)
        .button("Ascending", "textures/ui/up_arrow")
        .button("Descending", "textures/ui/down_arrow");

    listForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        if (response.canceled) {
            overworld.runCommandAsync("scoreboard players set initialized admin 0");
            mainMenu(player);
            return;
        }

        if (response.selection === 0) {
            overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay ascending");
            overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 2`);
        } else if (response.selection === 1) {
            overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay descending");
            overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 3`);
        }

        overworld.runCommandAsync("scoreboard players set initialized admin 0");
        mainMenu(player);
    });
}