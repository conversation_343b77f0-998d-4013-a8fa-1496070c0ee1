import { world, EntityComponentTypes, EquipmentSlot, ItemComponentTypes, Player, system, ItemEnchantableComponent, EntityEquippableComponent, ItemStack, EnchantmentType } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { mainMenu } from "../mainmenu";

/**
 *
 * @param {ItemStack} item
 * @returns {{type: string, level: number}[]}
 */
function GetEnchants(item) {
    if (!item)
        return [];
    /**
     * @type {ItemEnchantableComponent | undefined}
     */
    const enchantComponent = item.getComponent(ItemComponentTypes.Enchantable);
    const enchantments = enchantComponent?.getEnchantments?.();
    if (!enchantComponent || !enchantments) {
        console.warn("No enchantments found.");
        return [];
    }
    return enchantments.map((enchantment) => ({
        type: enchantment.type.id,
        level: enchantment.level,
    }));
}
system.afterEvents.scriptEventReceive.subscribe((event) => {
    const { sourceEntity } = event;
    if (!(sourceEntity instanceof Player))
        return;
    /**
     * @type {EntityEquippableComponent}
     */
    const equipment = sourceEntity.getComponent(EntityComponentTypes.Equippable);
    const mainhand = equipment.getEquipment(EquipmentSlot.Mainhand);
    if (!mainhand)
        return sourceEntity.sendMessage("no item in hand");
    sourceEntity.sendMessage(JSON.stringify(GetEnchants(mainhand)));
});



export function playerShop(player) {
    const storeOwnerObjective = world.scoreboard.getObjective("storeowner") || world.scoreboard.addObjective("storeowner", "Store Owner");

    // Check if the player is listed as a store owner
    const playerStoreName = `${player.name}_store`;
    const isStoreOwner = storeOwnerObjective.getParticipants().some((participant) => participant.displayName === playerStoreName);

    const playershopform = new ActionFormData()
        .title("Player Stores")
        .body("Welcome to PlayerShops! Choose an option below:");

    // Show "Start a Store" only if the player is not listed as a store owner
    if (!isStoreOwner) {
        playershopform.button("Start a Store");
    }

    playershopform.button("View Stores", "textures/ui/night_vision_effect");
    playershopform.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    playershopform.show(player).then((response) => {
        if (response.canceled || response.selection === undefined) return; // Exit without going back

        switch (response.selection) {
            case 0:
                if (!isStoreOwner) {
                    startPlayerStore(player);
                } else {
                    viewPlayerStores(player);
                }
                break;
            case 1:
                if (!isStoreOwner) {
                    viewPlayerStores(player);
                } else {
                    mainMenu(player);
                }
                break;
            case 2:
                mainMenu(player);
                break;
            default:
                break;
        }
    });
}



function startPlayerStore(player) {
    // Get or create necessary scoreboards.
    const storeOwnerObjective = world.scoreboard.getObjective("storeowner") ||
        world.scoreboard.addObjective("storeowner", "Store Owner");
    const moneyObjective = world.scoreboard.getObjective("Money");
    const adminObjective = world.scoreboard.getObjective("admin");

    if (!moneyObjective || !adminObjective) {
        player.sendMessage("Economy or Admin scoreboard not found. Please contact an administrator.");
        return;
    }

    // Build the player's key (lowercase, no spaces)
    const playerKey = player.name.toLowerCase().replace(/\s+/g, "");
    // Fake key for each store will be: playerKey_storeName
    // Fake key for total count will be: playerKey_count

    // Retrieve the current store count from the storeowner scoreboard via the count key.
    const countKey = `${playerKey}_count`;
    let currentStoreCount = storeOwnerObjective.getParticipants().find(p => p.displayName === countKey)
        ? storeOwnerObjective.getScore(storeOwnerObjective.getParticipants().find(p => p.displayName === countKey))
        : 0;

    // Retrieve max allowed stores and cost from the admin scoreboard.
    const p2pCountParticipant = adminObjective.getParticipants().find(p => p.displayName === "P2Pcount");
    const maxStores = p2pCountParticipant ? adminObjective.getScore(p2pCountParticipant) : 1;

    const p2pCostParticipant = adminObjective.getParticipants().find(p => p.displayName === "P2Pcost");
    const storeCost = p2pCostParticipant ? adminObjective.getScore(p2pCostParticipant) : 0;

    const playerMoney = moneyObjective.getScore(player) || 0;

    // Check if the player already owns the maximum number of stores.
    if (currentStoreCount >= maxStores) {
        player.sendMessage(`You already own the maximum number of stores (${maxStores}).`);
        return playerShop(player);
    }

    // Check if the player has enough money to start a store.
    if (playerMoney < storeCost) {
        player.sendMessage(`You do not have enough money to start a store. Store cost: ${storeCost} §4Z§2Coins.`);
        return playerShop(player);
    }

    // Prompt the player to name their store.
    const nameForm = new ModalFormData()
        .title("Name Your Store")
        .textField("Enter a name for your store:", "Store Name");

    nameForm.show(player).then((response) => {
        if (response.canceled || !response.formValues || response.formValues.length === 0) {
            return; // Exit without returning to the previous menu
        }

        let storeName = response.formValues[0].trim();
        if (!storeName) {
            player.sendMessage("Store name cannot be empty.");
            return startPlayerStore(player);
        }

        // Replace special characters for scoreboard compatibility and normalize to lowercase.
        storeName = storeName
            .replace(/\s+/g, "_")
            .replace(/§/g, "¤")
            .replace(/&/g, "¦")
            .toLowerCase();

        // Build a unique scoreboard ID for the store.
        const storeScoreboardId = `${player.name.toLowerCase()}_store_${storeName}`;

        // Check if a store with the same name already exists.
        const existingStores = world.scoreboard.getObjectives().filter((objective) => objective.id.includes("_store_"));
        const storeNameExists = existingStores.some((objective) => {
            const existingName = objective.id.split("_store_")[1];
            return existingName === storeName;
        });

        if (storeNameExists) {
            player.sendMessage(`A store with the name '${storeName.replace(/_/g, " ").replace(/¤/g, "§").replace(/¦/g, "&")}' already exists. Please choose a different name.`);
            return startPlayerStore(player);
        }

        try {
            const overworld = world.getDimension("overworld");

            // Create the new scoreboard for the store.
            const storeScoreboard = world.scoreboard.addObjective(storeScoreboardId, storeName.replace(/_/g, " ").replace(/¤/g, "§").replace(/¦/g, "&"));

            // Initialize the "zcoins" participant in the store scoreboard.
            storeScoreboard.setScore("zcoins", 0);

            // Build the fake key for this new store: playerKey_storeName
            const fakeStoreKey = `${playerKey}_${storeName}`;

            // Save the new store entry under the player's fake store key.
            storeOwnerObjective.setScore(fakeStoreKey, 1);

            // Also update the total count.
            // If the count key doesn't exist, create it.
            let countParticipant = storeOwnerObjective.getParticipants().find(p => p.displayName === countKey);
            if (!countParticipant) {
                // Create with initial count of 1
                storeOwnerObjective.setScore(countKey, 1);
            } else {
                const newCount = storeOwnerObjective.getScore(countParticipant) + 1;
                storeOwnerObjective.setScore(countKey, newCount);
            }

            // Deduct the store cost from the player's Money scoreboard.
            moneyObjective.setScore(player, playerMoney - storeCost);

            player.sendMessage(`Your store '${storeName.replace(/_/g, " ").replace(/¤/g, "§").replace(/¦/g, "&")}' has been created successfully!`);
        } catch (error) {
            player.sendMessage("An error occurred while creating your store. Please try again.");
            console.error("Store creation error:", error);
        }

        playerShop(player);
    }).catch((error) => {
        player.sendMessage("An unexpected error occurred. Please try again.");
        console.error("Form submission error:", error);
    });
}



function viewPlayerStores(player) {
    const storeList = [];

    // Gather all player-created store scoreboards
    world.scoreboard.getObjectives().forEach((objective) => {
        // Only include objectives that match the player store naming pattern
        if (objective.id.includes("_store_")) {
            const storeName = objective.id
                .split("_store_")[1]
                .replace(/_/g, " ")
                .replace(/¤/g, "§")
                .replace(/¦/g, "&"); // Reverse replacements
            storeList.push({ id: objective.id, name: storeName });
        }
    });

    if (storeList.length === 0) {
        player.sendMessage("There are no stores available to view.");
        return; // Exit if no stores are available
    }

    const storeForm = new ActionFormData()
        .title("Player Stores")
        .body("Choose a store to view:");

    storeList.forEach((store) => storeForm.button(store.name, "textures/ui/MCoin")); // Display store names
    storeForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    storeForm.show(player).then((response) => {
        if (response.canceled) {
            return; // Simply close the menu
        }

        if (response.selection === storeList.length) {
            // Handle "Back" button
            return playerShop(player); // Replace with the function that navigates back
        }

        const selectedStore = storeList[response.selection];
        handleSelectedStore(player, selectedStore, () => viewPlayerStores(player)); // Pass viewPlayerStores as callback
    }).catch((error) => {
        player.sendMessage("An error occurred while showing the store list.");
        console.error("Store view error:", error);
    });
}



function addToStore(player, selectedStore) {
    const inventory = player.getComponent("minecraft:inventory")?.container;

    if (!inventory) {
        player.sendMessage("Unable to fetch your inventory.");
        handleSelectedStore(player, selectedStore);
        return;
    }

    const items = [];
    for (let slot = 0; slot < inventory.size; slot++) {
        const item = inventory.getItem(slot);
        if (item) items.push({ slot, item });
    }

    if (items.length === 0) {
        player.sendMessage("Your inventory is empty.");
        handleSelectedStore(player, selectedStore);
        return;
    }

    const inventoryForm = new ActionFormData()
        .title("Select an Item to Add to Your Store")
        .body("Select an item from your inventory.");

    items.forEach((entry) => {
        const itemName = entry.item.typeId.replace(/^(minecraft:|zombie:)/, "");
        inventoryForm.button(`${entry.item.amount}x ${itemName}`);
    });

    inventoryForm.show(player).then((response) => {
        if (response.canceled) return;

        const selectedEntry = items[response.selection];
        if (!selectedEntry || !selectedEntry.item) {
            player.sendMessage("Invalid item selected.");
            handleSelectedStore(player, selectedStore);
            return;
        }

        const selectedItem = selectedEntry.item;
        const selectedSlot = selectedEntry.slot;

        // Extract enchantments using GetEnchants
        const enchantments = GetEnchants(selectedItem);
        const enchantmentString = enchantments
            .map((e) => `${e.type}:${e.level}`)
            .join(";");

        player.sendMessage(`Enchantments detected: ${enchantmentString || "None"}`);

        const quantityForm = new ModalFormData()
            .title(`Set Quantity for ${selectedItem.typeId}`)
            .textField("Enter the quantity to sell:", "Quantity", "1");

        quantityForm.show(player).then((quantityResponse) => {
            if (quantityResponse.canceled) return;

            const quantity = parseInt(quantityResponse.formValues[0]);
            if (isNaN(quantity) || quantity <= 0 || quantity > selectedItem.amount) {
                player.sendMessage("Invalid quantity.");
                return handleSelectedStore(player, selectedStore);
            }

            const priceForm = new ModalFormData()
                .title(`Set Price for ${selectedItem.typeId}`)
                .textField("Enter the price per item:", "Price", "1");

            priceForm.show(player).then((priceResponse) => {
                if (priceResponse.canceled) return;

                const price = parseInt(priceResponse.formValues[0]);
                if (isNaN(price) || price <= 0) {
                    player.sendMessage("Invalid price.");
                    return handleSelectedStore(player, selectedStore);
                }

                const scoreboard = world.scoreboard.getObjective(selectedStore.id);
                if (!scoreboard) {
                    player.sendMessage("Store scoreboard not found.");
                    return handleSelectedStore(player, selectedStore);
                }

                // Save the item with enchantments
                const itemKey = `${selectedItem.typeId}|${quantity}|${price}|${enchantmentString}`;
                scoreboard.setScore(itemKey, 0);

                // Debug message: Display the saved fake player name in chat
                player.sendMessage(`Saved to scoreboard: ${itemKey}`);

                // Update the player's inventory
                const itemToUpdate = inventory.getItem(selectedSlot);
                if (itemToUpdate) {
                    if (itemToUpdate.amount > quantity) {
                        itemToUpdate.amount -= quantity;
                        inventory.setItem(selectedSlot, itemToUpdate);
                    } else {
                        inventory.setItem(selectedSlot, null);
                    }
                }

                player.sendMessage(
                    `Added ${quantity} ${selectedItem.typeId.replace(/^(minecraft:|zombie:)/, "")}(s) to your store for ${price} each.`
                );
                handleSelectedStore(player, selectedStore);
            });
        });
    });
}





function handleSelectedStore(player, selectedStore, backCallback) {
    // Default to the main menu if backCallback is not provided
    backCallback = backCallback || (() => mainMenu(player));

    const storeOwnerName = selectedStore.id.split("_store_")[0];

    const storeMenu = new ActionFormData()
        .title(`Store: ${selectedStore.name}`)
        .body("Items available in this store:");

    if (storeOwnerName === player.name.toLowerCase()) {
        storeMenu.button("Add to Store");
        storeMenu.button("Claim Earnings");
    }

    storeMenu.button("Back");

    const scoreboard = world.scoreboard.getObjective(selectedStore.id);
    if (!scoreboard) {
        player.sendMessage("Store scoreboard not found.");
        return backCallback(); // Fallback to the defined back action
    }

    const items = [];
    scoreboard.getParticipants().forEach((participant) => {
        if (participant.displayName === "zcoins") return;

        const parts = participant.displayName.split("|");
        if (parts.length < 3) return;

        const [identifier, quantityStr, priceStr, enchantmentsStr] = parts;
        const quantity = parseInt(quantityStr);
        const price = parseInt(priceStr);

        if (!identifier || isNaN(quantity) || isNaN(price)) return;

        const enchantments = enchantmentsStr
            ? enchantmentsStr.split(";").map((e) => e.replace(":", " "))
            : [];

        items.push({
            id: participant.displayName,
            name: identifier.replace(/^(minecraft:|zombie:)/, ""),
            quantity,
            price,
            enchantments,
        });
    });

    items.forEach((item) => {
        const isEnchanted = item.enchantments.length > 0; // Check if the item is enchanted
        const enchantLabel = isEnchanted ? "§bEnchanted " : ""; // Add "Enchanted" label if applicable
        storeMenu.button(
            `${item.quantity}x ${item.name}\n${enchantLabel}§2Cost: ${item.price}`,
            "textures/ui/MCoin"
        );
    });

    storeMenu.show(player).then((response) => {
        if (response.canceled) {
            player.sendMessage("Store menu closed."); // Optional notification
            return; // Exit without navigating back
        }

        const offset = storeOwnerName === player.name.toLowerCase() ? 2 : 0;

        if (response.selection === 0 && offset === 2) {
            // "Add to Store" selected
            addToStore(player, selectedStore, () =>
                handleSelectedStore(player, selectedStore, backCallback)
            );
        } else if (response.selection === 1 && offset === 2) {
            // "Claim Earnings" selected
            claimZCoins(player, selectedStore);
        } else if (response.selection === offset) {
            // "Back" button selected
            backCallback(); // Return to the previous menu
        } else {
            // Handle item selection
            const selectedItem = items[response.selection - offset - 1];
            if (selectedItem) {
                if (storeOwnerName === player.name.toLowerCase()) {
                    manageStoreItem(player, selectedStore, selectedItem);
                } else {
                    purchaseStoreItem(player, selectedStore, selectedItem, backCallback);
                }
            }
        }
    }).catch((error) => {
        console.error("Error in store menu:", error);
        backCallback(); // Graceful fallback
    });
}




function manageStoreItem(player, selectedStore, selectedItem) {
    const itemMenu = new ActionFormData()
        .title(`Manage Item: ${selectedItem.name}`)
        .body(
            `Quantity: ${selectedItem.quantity}\n` +
            `Price: ${selectedItem.price}\n` +
            `Enchantments: ${selectedItem.enchantments.length ? selectedItem.enchantments.join(", ") : "None"}`
        )
        .button("Edit Price")
        .button("Remove Item")
        .button("Back");

    itemMenu.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0:
                // Edit Price
                editStoreItemPrice(player, selectedStore, selectedItem);
                break;
            case 1:
                // Remove Item
                removeStoreItem(player, selectedStore, selectedItem);
                break;
            case 2:
                // Back
                handleSelectedStore(player, selectedStore, () => viewPlayerStores(player));
                break;
            default:
                player.sendMessage("Invalid selection.");
                break;
        }
    });
}



function editStoreItemPrice(player, selectedStore, selectedItem) {
    const priceForm = new ModalFormData()
        .title(`Edit Price for ${selectedItem.name}`)
        .textField("Enter new price:", "Price", `${selectedItem.price}`);

    priceForm.show(player).then((response) => {
        if (response.canceled) return manageStoreItem(player, selectedStore, selectedItem);

        const newPrice = parseInt(response.formValues[0]);
        if (isNaN(newPrice) || newPrice <= 0) {
            player.sendMessage("Invalid price.");
            return editStoreItemPrice(player, selectedStore, selectedItem);
        }

        const scoreboard = world.scoreboard.getObjective(selectedStore.id);
        if (!scoreboard) {
            player.sendMessage("Store scoreboard not found.");
            return;
        }

        // Remove the old entry
        scoreboard.removeParticipant(selectedItem.id);

        // Add the updated entry with the correct format
        const newItemKey = `${selectedItem.identifier}|${selectedItem.quantity}|${newPrice}`;
        scoreboard.setScore(newItemKey, 0);

        player.sendMessage(`Price for ${selectedItem.name} updated to ${newPrice} §4Z§2Coins.`);
        handleSelectedStore(player, selectedStore, () => viewPlayerStores(player));
    });
}


function removeStoreItem(player, selectedStore, selectedItem) {
    const scoreboard = world.scoreboard.getObjective(selectedStore.id);
    if (!scoreboard) {
        player.sendMessage("Store scoreboard not found.");
        return;
    }

    // Remove the item from the scoreboard
    scoreboard.removeParticipant(selectedItem.id);

    // Return the item to the player's inventory
    const inventory = player.getComponent("minecraft:inventory")?.container;
    if (!inventory) {
        player.sendMessage("Unable to access inventory.");
        return;
    }

    try {
        // Parse item details from the key
        const parts = selectedItem.id.split("|");
        if (parts.length < 3) {
            player.sendMessage("Invalid item key format.");
            return;
        }

        const [identifier, quantityStr, priceStr, enchantmentsStr] = parts;
        const quantity = parseInt(quantityStr);

        if (!identifier || isNaN(quantity)) {
            player.sendMessage("Invalid item data.");
            return;
        }

        // Create the ItemStack to return
        const itemToReturn = new ItemStack(identifier, quantity);

        // Apply enchantments if they exist
        if (enchantmentsStr) {
            const enchantments = enchantmentsStr.split(";").map((e) => {
                const [type, level] = e.split(":");
                return { type: new EnchantmentType(type), level: parseInt(level) };
            });

            const enchantableComponent = itemToReturn.getComponent("minecraft:enchantable");
            if (enchantableComponent) {
                enchantments.forEach((enchant) => {
                    enchantableComponent.addEnchantment(enchant);
                });
                player.sendMessage(
                    `Enchantments applied: ${JSON.stringify(enchantments)}`
                );
            } else {
                player.sendMessage("This item cannot have enchantments applied.");
            }
        } else {
            player.sendMessage("No enchantments found for this item.");
        }

        // Add the item back to the player's inventory
        const slot = inventory.addItem(itemToReturn);
        if (slot !== -1) {
            player.sendMessage(
                `Removed ${quantity}x ${identifier.replace(
                    /minecraft:/,
                    ""
                )} from the store and returned it to your inventory with enchantments.`
            );
        } else {
            // Drop the item at the player's location if inventory is full
            player.sendMessage(
                `Removed ${quantity}x ${identifier.replace(
                    /minecraft:/,
                    ""
                )} from the store, but your inventory is full. Dropping the item at your location with enchantments.`
            );
            system.run(() =>
                player.dimension.spawnItem(itemToReturn, player.location)
            );
        }
    } catch (error) {
        player.sendMessage(
            `An error occurred while returning the item to your inventory: ${error.message}`
        );
        console.error(error);
    }

    // Go back to the store menu
    handleSelectedStore(player, selectedStore, () => viewPlayerStores(player));
}



// Helper function to parse item data
function parseItemData(itemId) {
    if (!itemId) throw new Error("Missing item data.");

    const parts = itemId.split("|");
    if (parts.length < 3) throw new Error("Invalid item format.");

    const [identifier, quantityStr, priceStr, enchantmentsStr] = parts;
    if (!identifier || isNaN(parseInt(quantityStr)) || isNaN(parseInt(priceStr))) {
        throw new Error("Invalid item data.");
    }

    return {
        identifier,
        quantity: parseInt(quantityStr),
        price: parseInt(priceStr),
        enchantments: enchantmentsStr
            ? enchantmentsStr.split(";").map((e) => {
                const [type, level] = e.split(":");
                return { type, level: parseInt(level) };
            })
            : [],
    };
}

// Main purchaseStoreItem function
function purchaseStoreItem(player, selectedStore, selectedItem, backCallback) {
    const scoreboard = world.scoreboard.getObjective(selectedStore.id);
    if (!scoreboard) {
        player.sendMessage("Error: Store scoreboard not found.");
        return;
    }

    const purchaseForm = new ModalFormData()
        .title(`Buy ${selectedItem.name}`)
        .textField(
            `How many would you like to buy? (${selectedItem.quantity} in stock at ${selectedItem.price} §4Z§2Coins each)`,
            "Quantity",
            "1"
        );

    purchaseForm.show(player).then((response) => {
        if (response.canceled) return handleSelectedStore(player, selectedStore, backCallback);

        const quantityToBuy = parseInt(response.formValues[0]);
        if (isNaN(quantityToBuy) || quantityToBuy <= 0 || quantityToBuy > selectedItem.quantity) {
            player.sendMessage("Invalid quantity.");
            return handleSelectedStore(player, selectedStore, backCallback);
        }

        const totalCost = quantityToBuy * selectedItem.price;
        const playerMoneyObjective = world.scoreboard.getObjective("Money");
        const playerMoney = playerMoneyObjective.getScore(player) || 0;

        if (playerMoney < totalCost) {
            player.sendMessage("You do not have enough §4Z§2Coins to complete this purchase.");
            return handleSelectedStore(player, selectedStore, backCallback);
        }

        try {
            // Parse item data
            try {
                const itemData = parseItemData(selectedItem.id);
                selectedItem.identifier = itemData.identifier;
                selectedItem.quantity = itemData.quantity;
                selectedItem.price = itemData.price;
                selectedItem.enchantments = itemData.enchantments;
            } catch (error) {
                player.sendMessage(`Error: ${error.message}`);
                console.error("Item parsing error:", error);
                return handleSelectedStore(player, selectedStore, backCallback);
            }

            // Deduct money from the buyer's Money scoreboard
            playerMoneyObjective.setScore(player, playerMoney - totalCost);

            // Add ZCoins to the store's bank
            const zcoinsScore = scoreboard.getScore("zcoins") || 0;
            scoreboard.setScore("zcoins", zcoinsScore + totalCost);

            // Update stock in the scoreboard
            const newStock = selectedItem.quantity - quantityToBuy;

            scoreboard.removeParticipant(selectedItem.id); // Remove the old participant entry

            if (newStock > 0) {
                // Add the updated entry back to the scoreboard
                const newKey = `${selectedItem.identifier}|${newStock}|${selectedItem.price}|${selectedItem.enchantments.join(";")}`;
                scoreboard.setScore(newKey, 0);
            }

            // Create and give the purchased item
            let purchasedItem;
            try {
                purchasedItem = new ItemStack(selectedItem.identifier, quantityToBuy);

                // Apply enchantments, if any
                const enchantableComponent = purchasedItem.getComponent("minecraft:enchantable");
                if (enchantableComponent && selectedItem.enchantments.length > 0) {
                    selectedItem.enchantments.forEach((enchant) => {
                        try {
                            const { type, level } = enchant;
                            enchantableComponent.addEnchantment({
                                type: new EnchantmentType(type),
                                level,
                            });
                        } catch (error) {
                            console.warn(`Failed to apply enchantment: ${type}:${level}`);
                        }
                    });
                }

                // Add the purchased item to the player's inventory
                const inventory = player.getComponent("minecraft:inventory")?.container;
                const slot = inventory.addItem(purchasedItem);

                if (slot === -1) {
                    // If inventory is full, drop the item
                    player.sendMessage("Your inventory is full. Items dropped at your location.");
                    system.run(() => player.dimension.spawnItem(purchasedItem, player.location));
                } else {
                    player.sendMessage(
                        `You bought ${quantityToBuy}x ${selectedItem.name} for ${totalCost} §4Z§2Coins.`
                    );
                }
            } catch (error) {
                player.sendMessage(`Error: Failed to create or give item '${selectedItem.identifier}'.`);
                console.error("ItemStack creation error:", error);
                return handleSelectedStore(player, selectedStore, backCallback);
            }
        } catch (error) {
            player.sendMessage("An error occurred while processing the purchase.");
            console.error("Purchase processing error:", error);
            return handleSelectedStore(player, selectedStore, backCallback);
        }

        handleSelectedStore(player, selectedStore, backCallback);
    }).catch((error) => {
        player.sendMessage("An unexpected error occurred during the purchase.");
        console.error("Unexpected purchase error:", error);
        handleSelectedStore(player, selectedStore, backCallback);
    });
}



function claimZCoins(player, selectedStore) {
    const scoreboard = world.scoreboard.getObjective(selectedStore.id);
    if (!scoreboard) {
        player.sendMessage("Store scoreboard not found.");
        return;
    }

    // Ensure "zcoins" participant exists
    let zcoinsScore = scoreboard.getScore("zcoins");
    if (zcoinsScore === undefined) {
        // Initialize "zcoins" if it doesn't exist
        scoreboard.setScore("zcoins", 0);
        zcoinsScore = 0;
    }

    if (zcoinsScore === 0) {
        player.sendMessage("No Economy available in the store bank.");
        return;
    }

    const playerMoneyObjective = world.scoreboard.getObjective("Money");
    const playerMoney = playerMoneyObjective.getScore(player) || 0;

    // Transfer ZCoins to the player's Money account
    playerMoneyObjective.setScore(player, playerMoney + zcoinsScore);

    // Reset ZCoins to 0
    scoreboard.setScore("zcoins", 0);

    player.sendMessage(`You have successfully claimed ${zcoinsScore} Economy from your store bank.`);
}
