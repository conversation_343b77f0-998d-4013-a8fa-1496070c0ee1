import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { addSettingsMenu } from "./admin_player_managment";

export function tp_settings(player, rank) {
    const scoreboardName = "admin"; // Assuming the fake players are under the "admin" scoreboard
    const tpSettings = [
        { name: "Tplayer", description: "Teleport to other players", texture: "textures/ui/check" },
        { name: "TPPlayer", description: "Teleport player to player", texture: "textures/ui/arrow" }
    ];

    const rankScoreboardName = `rank_${rank.name}`; // Assuming settings are stored per rank

    const form = new ActionFormData()
        .title("§4TP Settings")
        .body("Manage teleportation permissions.\n\n" +
            "§aTeleport to Other Players§r: Allows admins to teleport to other players.\n" +
            "§aTeleport Player to Player§r: Allows admins to teleport one player to another.");

    const scores = {}; // Cache scores to determine on/off state

    // Fetch scores for each setting
    tpSettings.forEach(setting => {
        try {
            const participant = world.scoreboard.getObjective(rankScoreboardName).getParticipants()
                .find(p => p.displayName === setting.name);
            scores[setting.name] = participant ? world.scoreboard.getObjective(rankScoreboardName).getScore(participant) : 0;
        } catch {
            scores[setting.name] = 0; // Default to 0 if not found
        }

        const status = scores[setting.name] === 1 ? "§a(On)" : "§c(Off)";
        form.button(`${setting.description} ${status}`, setting.texture);
    });

    form.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then(response => {
        if (response.canceled) {
            return; // Exit the menu
        }

        if (response.selection === tpSettings.length) {
            addSettingsMenu(player, rank); // Return to Add Settings Menu
            return;
        }

        // Toggle the selected setting
        const selectedSetting = tpSettings[response.selection];
        const currentScore = scores[selectedSetting.name];
        const newScore = currentScore === 1 ? 0 : 1;

        player.runCommandAsync(`scoreboard players set ${selectedSetting.name} ${rankScoreboardName} ${newScore}`).then(() => {
            player.sendMessage(`§a${selectedSetting.description} is now ${newScore === 1 ? "enabled" : "disabled"}.`);
            tp_settings(player, rank); // Refresh the TP Settings menu
        }).catch(err => {
            console.error("Error toggling TP setting:", err);
            player.sendMessage(`§cFailed to toggle ${selectedSetting.description}.`);
        });
    }).catch(err => {
        console.error("Error in tp_settings:", err);
        player.sendMessage("§cAn error occurred while managing TP settings.");
    });
}



export function player_settings(player, rank) {
    const scoreboardName = `rank_${rank.name}`; // Use the rank-specific scoreboard
    const playerSettings = [
        { name: "PlayerSettings_banmenu", description: "Ban Menu", texture: "textures/ui/hammer_icon" },
        { name: "PlayerSettings_mpb", description: "Modify Players' Balance", texture: "textures/ui/gold_ingot" },
        { name: "PlayerSettings_inventory", description: "View Players' Inventory", texture: "textures/ui/chest_icon" },
        { name: "PlayerSettings_playerbases", description: "Player Bases", texture: "textures/ui/home_icon" }
    ];

    const form = new ActionFormData()
        .title("§4Player Settings")
        .body("Manage player-related permissions.\n\n" +
            "§aBan Menu§r: Enable or disable access to the ban menu.\n" +
            "§aModify Players' Balance§r: Allow modifying players' balance.\n" +
            "§aView Players' Inventory§r: Allow viewing players' inventory.\n" +
            "§aPlayer Bases§r: Enable or disable managing player bases.");

    const scores = {}; // Cache scores to determine on/off state

    // Fetch scores for each setting
    playerSettings.forEach(setting => {
        try {
            const participant = world.scoreboard.getObjective(scoreboardName).getParticipants()
                .find(p => p.displayName === setting.name);
            scores[setting.name] = participant ? world.scoreboard.getObjective(scoreboardName).getScore(participant) : 0;
        } catch {
            scores[setting.name] = 0; // Default to 0 if not found
        }

        const status = scores[setting.name] === 1 ? "§a(On)" : "§c(Off)";
        form.button(`${setting.description} ${status}`, setting.texture);
    });

    form.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then(response => {
        if (response.canceled) {
            return; // Exit the menu
        }

        if (response.selection === playerSettings.length) {
            addSettingsMenu(player, rank); // Return to Add Settings Menu
            return;
        }

        // Toggle the selected setting
        const selectedSetting = playerSettings[response.selection];
        const currentScore = scores[selectedSetting.name];
        const newScore = currentScore === 1 ? 0 : 1;

        player.runCommandAsync(`scoreboard players set ${selectedSetting.name} ${scoreboardName} ${newScore}`).then(() => {
            player.sendMessage(`§a${selectedSetting.description} is now ${newScore === 1 ? "enabled" : "disabled"}.`);
            player_settings(player, rank); // Refresh the Player Settings menu
        }).catch(err => {
            console.error("Error toggling Player Settings:", err);
            player.sendMessage(`§cFailed to toggle ${selectedSetting.description}.`);
        });
    }).catch(err => {
        console.error("Error in player_settings:", err);
        player.sendMessage("§cAn error occurred while managing Player Settings.");
    });
}


export function stores_settings(player, rank) {
    const scoreboardName = `rank_${rank.name}`; // Use the rank-specific scoreboard
    const storeSettings = [
        { name: "StoreSettings_ServerShop", description: "Server Shop", texture: "textures/ui/store" },
        { name: "StoreSettings_PlayertoPlayer", description: "Player to Player Shop", texture: "textures/ui/exchange" },
        { name: "StoreSettings_SellShop", description: "Sell Shop", texture: "textures/ui/sell" }
    ];

    const form = new ActionFormData()
        .title("§4Store Settings")
        .body("Manage store-related permissions.\n\n" +
            "§aServer Shop§r: Enable or disable access to the server shop.\n" +
            "§aPlayer to Player Shop§r: Allow players to trade with each other.\n" +
            "§aSell Shop§r: Enable or disable selling items to the shop.");

    const scores = {}; // Cache scores to determine on/off state

    // Fetch scores for each setting
    storeSettings.forEach(setting => {
        try {
            const participant = world.scoreboard.getObjective(scoreboardName).getParticipants()
                .find(p => p.displayName === setting.name);
            scores[setting.name] = participant ? world.scoreboard.getObjective(scoreboardName).getScore(participant) : 0;
        } catch {
            scores[setting.name] = 0; // Default to 0 if not found
        }

        const status = scores[setting.name] === 1 ? "§a(On)" : "§c(Off)";
        form.button(`${setting.description} ${status}`, setting.texture);
    });

    form.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then(response => {
        if (response.canceled) {
            return; // Exit the menu
        }

        if (response.selection === storeSettings.length) {
            addSettingsMenu(player, rank); // Return to Add Settings Menu
            return;
        }

        // Toggle the selected setting
        const selectedSetting = storeSettings[response.selection];
        const currentScore = scores[selectedSetting.name];
        const newScore = currentScore === 1 ? 0 : 1;

        player.runCommandAsync(`scoreboard players set ${selectedSetting.name} ${scoreboardName} ${newScore}`).then(() => {
            player.sendMessage(`§a${selectedSetting.description} is now ${newScore === 1 ? "enabled" : "disabled"}.`);
            stores_settings(player, rank); // Refresh the Store Settings menu
        }).catch(err => {
            console.error("Error toggling Store Settings:", err);
            player.sendMessage(`§cFailed to toggle ${selectedSetting.description}.`);
        });
    }).catch(err => {
        console.error("Error in stores_settings:", err);
        player.sendMessage("§cAn error occurred while managing Store Settings.");
    });
}


export function gamemode_settings(player, rank) {
    const scoreboardName = `rank_${rank.name}`; // Use the rank-specific scoreboard
    const gamemodeSettings = [
        { name: "gamemode_self", description: "Self Gamemode", texture: "textures/ui/person" },
        { name: "gamemode_others", description: "Others Gamemode", texture: "textures/ui/group" }
    ];

    const form = new ActionFormData()
        .title("§4Gamemode Settings")
        .body("Manage gamemode permissions.\n\n" +
            "§aSelf Gamemode§r: Allow admins to change their own gamemode.\n" +
            "§aOthers Gamemode§r: Allow admins to change other players' gamemode.");

    const scores = {}; // Cache scores to determine on/off state

    // Fetch scores for each setting
    gamemodeSettings.forEach(setting => {
        try {
            const participant = world.scoreboard.getObjective(scoreboardName).getParticipants()
                .find(p => p.displayName === setting.name);
            scores[setting.name] = participant ? world.scoreboard.getObjective(scoreboardName).getScore(participant) : 0;
        } catch {
            scores[setting.name] = 0; // Default to 0 if not found
        }

        const status = scores[setting.name] === 1 ? "§a(On)" : "§c(Off)";
        form.button(`${setting.description} ${status}`, setting.texture);
    });

    form.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then(response => {
        if (response.canceled) {
            return; // Exit the menu
        }

        if (response.selection === gamemodeSettings.length) {
            addSettingsMenu(player, rank); // Return to Add Settings Menu
            return;
        }

        // Toggle the selected setting
        const selectedSetting = gamemodeSettings[response.selection];
        const currentScore = scores[selectedSetting.name];
        const newScore = currentScore === 1 ? 0 : 1;

        player.runCommandAsync(`scoreboard players set ${selectedSetting.name} ${scoreboardName} ${newScore}`).then(() => {
            player.sendMessage(`§a${selectedSetting.description} is now ${newScore === 1 ? "enabled" : "disabled"}.`);
            gamemode_settings(player, rank); // Refresh the Gamemode Settings menu
        }).catch(err => {
            console.error("Error toggling Gamemode Settings:", err);
            player.sendMessage(`§cFailed to toggle ${selectedSetting.description}.`);
        });
    }).catch(err => {
        console.error("Error in gamemode_settings:", err);
        player.sendMessage("§cAn error occurred while managing Gamemode Settings.");
    });
}


