import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { mainMenu } from "./mainmenu";
import { adminPlayerManagement } from "./admin_contols/admin_player_managment"
import { playerMenuSettings } from "./admin_contols/playermenusettings"
import { banMenu } from "./banplayer"
import { economy } from "./economy"
import { playerBaseMenu } from "./playerbasemanagment"
import { inspectPlayerInventory } from "./playinventory"
import { inspectEnderChest } from "./player_enderchest"
import { onePlayerSleep } from "./oneplayersleep"
import { dataBase } from "./redbutton"



export function adminMenu(player) {
    const initialMenu = new ActionFormData()
        .title("§4Admin Menu")
        .body("Administrative tools for managing the server.")
        .button("Player Management", "textures/ui/invite_base")
        .button("Admin Settings", "textures/ui/gear")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover")
        .button("§l§cExit", "textures/ui/crossout");



    initialMenu.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        switch (response.selection) {
            case 0:
                playerManagementMenu(player);
                break;
            case 1:
                adminSettingsMenu(player);
                break;
            case 2:
                mainMenu(player);
                break;
            case 3:
                player.sendMessage("Exiting menu...");
                return; // Exit completely

            default:
                break;
        }
    });
}




export function playerManagementMenu(player) {
    const playerManagementForm = new ActionFormData()
        .title("§2Player Management")
        .body("Manage players on the server.")
        .button("Change Gamemode", "textures/ui/emptyStar")
        .button("Ban Menu", "textures/ui/ErrorGlyph")
        .button("Modify Player Balance", "textures/ui/enable_editor")
        .button("View Player Inventory", "textures/ui/settings_glyph_color_2x")
        .button("Teleport to Player", "textures/ui/update")
        .button("Player Bases", "textures/ui/invite_base")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover")
        .button("§l§cExit", "textures/ui/crossout");

    playerManagementForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        switch (response.selection) {
            case 0: changeGamemode(player); break;
            case 1: banMenu(player); break;
            case 2: playerRewards(player); break;
            case 3: viewPlayerInventory(player); break;
            case 4: adminTeleport(player); break;
            case 5: playerBaseMenu(player); break;
            case 6: adminMenu(player); break;
            case 7:
                player.sendMessage("§0Exiting menu...");
                return;
            default: break;
        }
    });
}



export function adminSettingsMenu(player) {
    const adminSettingsForm = new ActionFormData()
        .title("§4Admin Settings")
        .body("Administrative configurations.")
        .button("Admin Management", "textures/ui/attack_pressed")
        .button("Player Menu Settings", "textures/ui/settings_glyph_color_2x")
        .button("Commands Prompt", "textures/blocks/command_block")
        .button("Set Spawn", "textures/ui/village_hero_effect")
        .button("Economy", "textures/ui/deop")
        .button("One Player Sleep %", "textures/ui/icon_recipe_item")
        .button("View and edit\nDataBase", "textures/ui/gear")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover")
        .button("§l§cExit", "textures/ui/crossout");

    adminSettingsForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        switch (response.selection) {
            case 0:
                adminPlayerManagement(player);
                break;
            case 1:
                playerMenuSettings(player);
                break;
            case 2:
                adminCommand(player);
                break;
            case 3:
                setSpawn(player);
                break;
            case 4:
                economy(player);
                break;
            case 5:
                onePlayerSleep(player);
                break;
            case 6:
                dataBase(player)
                break;
            case 7:
                adminMenu(player);
                break;
            case 8:
                player.sendMessage("§0Exiting menu...");
                return;
            default:
                break;
        }
    });
}


async function setSpawn(player) {
    const { x, y, z } = player.location;
    const dimension = player.dimension.id.replace("minecraft:", ""); // e.g., overworld, nether, end

    // Confirm spawn point dialog
    const confirmationForm = new ActionFormData()
        .title("§4Set Spawn Confirmation")
        .body(
            `§bAre you sure you want to set the spawn point to the following location? 

` +
            `X: §a${Math.round(x)}§r
` +
            `Y: §a${Math.round(y)}§r
` +
            `Z: §a${Math.round(z)}§r
` +
            `Dimension: §a${dimension}`
        )
        .button("§aYes, Set Spawn")
        .button("§cNo, Cancel");

    confirmationForm.show(player).then(async (response) => {
        if (response.canceled || response.selection === 1) {
            player.sendMessage("§cSpawn point setting canceled.");
            return;
        }

        if (response.selection === 0) {
            try {
                const spawnTag = `global_spawn_${dimension}_${Math.round(x)}_${Math.round(y)}_${Math.round(z)}`;
                const dimensionObj = world.getDimension("overworld");

                // Remove any existing scoreboard objective for global spawn
                try {
                    await dimensionObj.runCommandAsync("scoreboard objectives remove setspawn");
                } catch (error) {
                    // Ignore error if objective doesn't exist
                }

                // Re-add the scoreboard
                await dimensionObj.runCommandAsync(`scoreboard objectives add setspawn dummy "Set Spawn Points"`);

                // Save the new global spawn point with the updated tag
                const saveCommand = `scoreboard players set "${spawnTag}" setspawn 0`;
                console.log(`Saving global spawn with command: ${saveCommand}`);
                await dimensionObj.runCommandAsync(saveCommand);

                // Run /setworldspawn to update the world's spawn point
                const setWorldSpawnCommand = `setworldspawn ${Math.round(x)} ${Math.round(y)} ${Math.round(z)}`;
                await dimensionObj.runCommandAsync(setWorldSpawnCommand);

                player.sendMessage(
                    `§aGlobal spawn point set successfully at:
` +
                    `§bX=${Math.round(x)}, Y=${Math.round(y)}, Z=${Math.round(z)} in ${dimension}.` +
                    `\n§aWorld spawn also updated!`
                );
            } catch (error) {
                console.error("Error setting global spawn point:", error);
                player.sendMessage("§cAn error occurred while setting the global spawn point.");
            }
        }
    }).catch((error) => {
        console.error("Error showing spawn confirmation form:", error);
        player.sendMessage("§cAn error occurred while confirming the spawn point.");
    });
}

export { setSpawn };




function adminTeleport(player) {
    const players = [...world.getPlayers()];

    if (players.length === 0) {
        player.sendMessage("§cNo players are currently online.");
        adminMenu(player);
        return;
    }

    const playerSelectionForm = new ActionFormData()
        .title("§4Teleport")
        .body("Select a player to teleport to:");

    players.forEach((targetPlayer) => playerSelectionForm.button(targetPlayer.name));
    playerSelectionForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");
    playerSelectionForm.button("§l§cExit", "textures/ui/crossout");

    playerSelectionForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        if (response.selection === players.length) {
            adminMenu(player);
            return;
        }

        if (response.selection === players.length + 1) {
            player.sendMessage("§0Exiting menu...");
            return; // Exit completely
        }

        const targetPlayer = players[response.selection];
        if (targetPlayer) {
            player.runCommandAsync(`tp @s ${targetPlayer.name}`)
                .then(() => {
                    player.sendMessage(`§0Teleported to ${targetPlayer.name}!`);
                })
                .catch((err) => {
                    player.sendMessage(`§cFailed to teleport: ${err.message}`);
                });
        } else {
            player.sendMessage("§cPlayer not found. Please try again.");
            adminTeleport(player);
        }
    });
}



function adminCommand(player) {
    const allowedNames = [
        "zombieclinic",
        "R0seDrag0n91",
        "SatanDragon4233",
        "ETphonehome3876",
        "LemonRobin78030",
        "Crims0nBl00d",
        "C0D0GAMER"
        // Add more names here
    ];

    const commandForm = new ModalFormData()
        .title("§4Admin Commands")
        .textField("Enter the command to execute:", "Command");

    commandForm.show(player).then((response) => {
        if (response.canceled) {
            player.sendMessage("§cCommand input canceled.");
            return;
        }

        const command = response.formValues[0]?.trim();
        if (!command) {
            player.sendMessage("§cNo command entered. Returning to Admin Menu.");
            adminMenu(player); // Return to admin menu
            return;
        }

        // Special case for "betaplayer"
        if (command.toLowerCase() === "betaplayer") {
            if (allowedNames.includes(player.name)) {
                try {
                    player.sendMessage("§aYou have been granted the Ender Zombie Cape!");
                    player.runCommandAsync(`give @s zombie:enderzombie_cape`);
                } catch (error) {
                    player.sendMessage(`§cFailed to grant the item: ${error.message}`);
                }
            } else {
                player.sendMessage("§cYou do not have permission to use this command.");
            }
            return; // Exit after handling the special command
        }

        // Handle other commands
        player.runCommandAsync(command)
            .then(() => {
                player.sendMessage(`§aCommand executed successfully: /${command}`);
            })
            .catch((err) => {
                player.sendMessage(`§cFailed to execute command: ${err.message}`);
            })
            .finally(() => {
                adminMenu(player);
            });
    }).catch((err) => {
        console.error("Error showing admin command form:", err);
        player.sendMessage("§cAn error occurred while opening the command input form.");
    });
}





function changeGamemode(player) {
    const players = [...world.getPlayers()];
    if (players.length === 0) {
        player.sendMessage("§cNo players are currently online.");
        adminMenu(player);
        return;
    }

    const playerSelectionForm = new ActionFormData()
        .title("§4Select Player")
        .body("Select the player whose gamemode you want to change.");

    players.forEach((p) => playerSelectionForm.button(p.name));
    playerSelectionForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");
    playerSelectionForm.button("§l§cExit", "textures/ui/crossout");

    playerSelectionForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        if (response.selection === players.length) {
            adminMenu(player);
            return;
        }

        if (response.selection === players.length + 1) {
            player.sendMessage("§aExiting menu...");
            return; // Exit completely
        }

        const targetPlayer = players[response.selection];
        if (!targetPlayer) {
            player.sendMessage("§cPlayer not found.");
            adminMenu(player);
            return;
        }

        const gamemodeForm = new ActionFormData()
            .title(`§4Change Gamemode for ${targetPlayer.name}`)
            .body("Select the gamemode to switch to:")
            .button("Survival", "textures/ui/weaving_effect")
            .button("Creative", "textures/ui/village_hero_effect")
            .button("Adventure", "textures/ui/World")
            .button("Spectator", "textures/ui/weather_clear")
            .button("§l§cBack", "textures/ui/book_arrowleft_hover")
            .button("§l§cExit", "textures/ui/crossout");

        gamemodeForm.show(player).then((response) => {
            if (response.canceled) return; // Exit silently

            if (response.selection === 4) {
                changeGamemode(player);
                return;
            }

            if (response.selection === 5) {
                player.sendMessage("§aExiting menu...");
                return; // Exit completely
            }

            const modes = ["survival", "creative", "adventure", "spectator"];
            if (response.selection < modes.length) {
                targetPlayer.runCommandAsync(`gamemode ${modes[response.selection]} @s`).then(() => {
                    player.sendMessage(`§a${targetPlayer.name}'s gamemode has been changed to ${modes[response.selection]}!`);
                }).catch((err) => {
                    player.sendMessage(`§cFailed to change ${targetPlayer.name}'s gamemode: ${err}`);
                });
            }

            adminMenu(player); // Return to the admin menu
        });
    });
}

function playerRewards(player) {
    // Get a list of all online players
    const onlinePlayers = Array.from(world.getPlayers());
    if (onlinePlayers.length === 0) {
        player.sendMessage("No players are online.");
        return;
    }

    // Action Form: List of players
    const actionForm = new ActionFormData()
        .title("Select a Player")
        .body("Choose a player to reward or penalize:");

    onlinePlayers.forEach((p) => actionForm.button(p.name));

    actionForm.show(player).then((actionResponse) => {
        if (actionResponse.canceled) return;

        const selectedPlayer = onlinePlayers[actionResponse.selection];
        if (!selectedPlayer) {
            player.sendMessage("Player selection failed.");
            return;
        }

        // Modal Form: Amount Input
        const modalForm = new ModalFormData()
            .title(`Reward or Penalize ${selectedPlayer.name}`)
            .textField("Enter the amount to add or remove (e.g., -500 to remove):", "Amount", "0");

        modalForm.show(player).then((modalResponse) => {
            if (modalResponse.canceled) return;

            const rewardAmount = parseInt(modalResponse.formValues[0]);
            if (isNaN(rewardAmount)) {
                player.sendMessage("Invalid amount entered. Please enter a valid number.");
                return;
            }

            // Determine if it's adding or removing
            const operation = rewardAmount >= 0 ? "add" : "remove";
            const absoluteAmount = Math.abs(rewardAmount); // Get the absolute value for the command

            // Run the scoreboard command
            const command = `scoreboard players ${operation} "${selectedPlayer.name}" Money ${absoluteAmount}`;
            try {
                world.getDimension("overworld").runCommand(command);
                const action = rewardAmount >= 0 ? "rewarded" : "penalized";
                player.sendMessage(`You have ${action} ${selectedPlayer.name} with ${absoluteAmount} Money.`);
            } catch (error) {
                player.sendMessage("Failed to update the player's Money score. Ensure the scoreboard exists.");
                console.error(error);
            }
        });
    });
}


export function viewPlayerInventory(player) {
    const players = [...world.getPlayers()];
    if (players.length === 0) {
        player.sendMessage("§cNo players are currently online.");
        adminMenu(player);
        return;
    }

    const playerSelectionForm = new ActionFormData()
        .title("§4Select Player")
        .body("Choose a player to inspect:");

    players.forEach((p) => playerSelectionForm.button(p.name));
    playerSelectionForm.button("§l§cBack");
    playerSelectionForm.button("§l§cExit");

    playerSelectionForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        if (response.selection === players.length) {
            // Back button
            playerManagementMenu(player);
            return;
        }

        if (response.selection === players.length + 1) {
            // Exit button
            player.sendMessage("§aExiting menu...");
            return; // Exit completely
        }

        const selectedPlayer = players[response.selection];
        if (!selectedPlayer) {
            player.sendMessage("§cPlayer not found.");
            adminMenu(player);
            return;
        }

        const inventoryTypeForm = new ActionFormData()
            .title(`§6Inspect ${selectedPlayer.name}`)
            .body("Select which inventory to inspect:")
            .button("Player Inventory", "textures/ui/csb_purchase_amazondevicewarning")
            .button("Ender Chest", "textures/ui/icon_blackfriday")
            .button("§l§cBack", "textures/ui/book_arrowleft_hover")
            .button("§l§cExit", "textures/ui/crossout");

        inventoryTypeForm.show(player).then((inventoryResponse) => {
            if (inventoryResponse.canceled) return; // Exit silently

            if (inventoryResponse.selection === 2) {
                // Back button
                viewPlayerInventory(player); // Correctly calls the function to reopen player selection
                return;
            }

            if (inventoryResponse.selection === 3) {
                // Exit button
                player.sendMessage("§aExiting menu...");
                return; // Exit completely
            }

            if (inventoryResponse.selection === 0) {
                inspectPlayerInventory(player, selectedPlayer);
            } else if (inventoryResponse.selection === 1) {
                inspectEnderChest(player, selectedPlayer);
            }
        }).catch((err) => {
            player.sendMessage(`§cAn error occurred: ${err.message}`);
            viewPlayerInventory(player); // Correctly call this function in case of error
        });
    }).catch((err) => {
        playerManagementMenu(player); // Returns to player management menu on error
    });
}






