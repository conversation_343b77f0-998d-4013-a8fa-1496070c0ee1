import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { playerSettingsMenu } from "./admin_ranks_menu"


/*************************************************
 * 4a) Ban Menu
 *************************************************/
export function banMenu(player, rank) {
    const banObj = world.scoreboard.getObjective("BanList");
    if (!banObj) {
        player.sendMessage("§cNo Ban List found. Creating one...");
        player.runCommandAsync('scoreboard objectives add BanList dummy "Banned Players"');
        banMenu(player, rank);
        return;
    }

    const form = new ActionFormData()
        .title("§4Ban Menu")
        .body("Manage the ban list.")
        .button("Add Player", "textures/ui/anvil_icon")
        .button("Type Player Manually", "textures/ui/haste_effect")
        .button("View Ban List", "textures/ui/icon_book_writable")
        .button("Back", "textures/ui/book_arrowleft_hover");

    form.show(player).then(resp => {
        if (resp.canceled) return;
        switch (resp.selection) {
            case 0: addPlayerToList(player, rank); break;
            case 1: typePlayerManually(player, rank); break;
            case 2: viewBanList(player, rank); break;
            case 3: playerSettingsMenu(player, rank); break;
        }
    });
}

function addPlayerToList(player, rank) {
    const onlinePlayers = [...world.getPlayers()];
    const banned = getBanList();
    const eligible = onlinePlayers.filter(p => !banned.includes(p.name));

    if (!eligible.length) {
        player.sendMessage("§cNo eligible players to ban.");
        banMenu(player, rank);
        return;
    }

    const f = new ActionFormData()
        .title("Add Player to Ban")
        .body("Select a player to ban:");
    const actions = [];
    eligible.forEach(p => {
        f.button(p.name);
        actions.push(() => confirmBanPlayer(player, rank, p.name));
    });

    // Back
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => banMenu(player, rank));

    f.show(player).then(r => {
        if (r.canceled) return;
        if (r.selection < actions.length) actions[r.selection]();
    });
}

function typePlayerManually(player, rank) {
    const f = new ModalFormData()
        .title("Type Player In")
        .textField("Name to ban:", "Player Name");

    f.show(player).then(r => {
        if (r.canceled || !r.formValues[0].trim()) {
            banMenu(player, rank);
            return;
        }
        confirmBanPlayer(player, rank, r.formValues[0].trim());
    });
}

function confirmBanPlayer(player, rank, name) {
    const c = new ActionFormData()
        .title("Confirm Ban")
        .body(`Ban ${name}?`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    c.show(player).then(r => {
        if (r.canceled || r.selection === 1) {
            banMenu(player, rank);
            return;
        }
        player.runCommandAsync(`scoreboard players add "ban_${name}" BanList 1`).then(() => {
            player.sendMessage(`§a${name} has been banned.`);
            const target = [...world.getPlayers()].find(p => p.name === name);
            if (target) target.runCommandAsync('kick You have been banned.');
        });
    });
}

function viewBanList(player, rank) {
    const banned = getBanList();
    if (!banned.length) {
        player.sendMessage("§cBan List is empty.");
        banMenu(player, rank);
        return;
    }

    const f = new ActionFormData()
        .title("§4Ban List")
        .body("Select a player to unban:");
    const actions = [];
    banned.forEach(n => {
        f.button(n);
        actions.push(() => confirmUnbanPlayer(player, rank, n));
    });

    // Back
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => banMenu(player, rank));

    f.show(player).then(r => {
        if (r.canceled) return;
        if (r.selection < actions.length) actions[r.selection]();
    });
}

function confirmUnbanPlayer(player, rank, name) {
    const c = new ActionFormData()
        .title("Confirm Unban")
        .body(`Unban ${name}?`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    c.show(player).then(r => {
        if (r.canceled || r.selection === 1) {
            viewBanList(player, rank);
            return;
        }
        player.runCommandAsync(`scoreboard players reset "ban_${name}" BanList`).then(() => {
            player.sendMessage(`§a${name} unbanned.`);
            viewBanList(player, rank);
        });
    });
}

function getBanList() {
    const obj = world.scoreboard.getObjective("BanList");
    if (!obj) return [];
    return obj.getParticipants()
        .filter(p => p.displayName.startsWith("ban_"))
        .map(p => p.displayName.replace("ban_", ""));
}
