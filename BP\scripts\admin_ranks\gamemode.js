import { world } from "@minecraft/server";
import { ActionFormData } from "@minecraft/server-ui";
import { playerSettingsMenu } from "./admin_ranks_menu"



/*************************************************
 * 6) Gamemode Settings Submenu
 *************************************************/
export function gamemodeMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const gmOptions = [
        { name: "gamemode_self", description: "Self Gamemode", texture: "textures/ui/person" },
        { name: "gamemode_others", description: "Others Gamemode", texture: "textures/ui/group" }
    ];
    const obj = world.scoreboard.getObjective(rankScoreboardName);
    const scores = {};
    gmOptions.forEach(opt => {
        const part = obj.getParticipants().find(p => p.displayName === opt.name);
        scores[opt.name] = part ? obj.getScore(part) : 0;
    });

    const f = new ActionFormData()
        .title("Gamemode Settings")
        .body("Select a gamemode option:");
    const buttonActions = [];

    gmOptions.forEach(opt => {
        if (scores[opt.name] === 1) {
            f.button(opt.description, opt.texture);
            if (opt.name === "gamemode_self") {
                buttonActions.push(() => selfGamemodeMenu(player, rank));
            } else {
                buttonActions.push(() => othersGamemodeMenu(player, rank));
            }
        }
    });

    // Back => rank
    f.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => playerSettingsMenu(player));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (buttonActions[resp.selection]) buttonActions[resp.selection]();
    });
}

// Self Gamemode
function selfGamemodeMenu(player, rank) {
    const modes = [
        { mode: "survival", display: "Survival" },
        { mode: "creative", display: "Creative" },
        { mode: "adventure", display: "Adventure" },
        { mode: "spectator", display: "Spectator" }
    ];
    const f = new ActionFormData()
        .title("Self Gamemode")
        .body("Select your new gamemode:");
    const actions = [];

    modes.forEach(gm => {
        f.button(gm.display);
        actions.push(() => {
            player.runCommandAsync(`gamemode ${gm.mode} "${player.name}"`).then(() => {
                player.sendMessage(`§aYour gamemode is now ${gm.display}.`);
                selfGamemodeMenu(player, rank);
            }).catch(() => {
                player.sendMessage("§cFailed to set gamemode.");
            });
        });
    });

    // Back => gamemode
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => gamemodeMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (actions[resp.selection]) actions[resp.selection]();
    });
}

// Others Gamemode
function othersGamemodeMenu(player, rank) {
    const others = [...world.getPlayers()].filter(p => p.name !== player.name);
    const f = new ActionFormData()
        .title("Others Gamemode")
        .body("Select a player:");
    const actions = [];

    others.forEach(p => {
        f.button(p.name);
        actions.push(() => changeOthersGamemodeMenu(player, rank, p));
    });

    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => gamemodeMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (actions[resp.selection]) actions[resp.selection]();
    });
}

function changeOthersGamemodeMenu(player, rank, target) {
    const gmodes = [
        { mode: "survival", display: "Survival" },
        { mode: "creative", display: "Creative" },
        { mode: "adventure", display: "Adventure" },
        { mode: "spectator", display: "Spectator" }
    ];
    const f = new ActionFormData()
        .title(`Change ${target.name}'s Gamemode`)
        .body("Select new gamemode:");
    const buttonActions = [];

    gmodes.forEach(gm => {
        f.button(gm.display);
        buttonActions.push(() => {
            const c = new ActionFormData()
                .title("Confirm Gamemode")
                .body(`Change ${target.name} to ${gm.display}?`)
                .button("Yes", "textures/ui/check")
                .button("No", "textures/ui/crossout");

            c.show(player).then(resp => {
                if (resp.canceled || resp.selection === 1) {
                    othersGamemodeMenu(player, rank);
                    return;
                }
                player.runCommandAsync(`gamemode ${gm.mode} "${target.name}"`).then(() => {
                    player.sendMessage(`§aSet ${target.name}'s gamemode to ${gm.display}.`);
                    othersGamemodeMenu(player, rank);
                }).catch(() => {
                    player.sendMessage("§cFailed to change gamemode.");
                });
            });
        });
    });

    f.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => othersGamemodeMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (buttonActions[resp.selection]) buttonActions[resp.selection]();
    });
}