import { ItemStack, ItemTypes, world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { openInventoryTypeMenu } from "./admin_ranks_menu"

const allItems = ItemTypes.getAll();

export class EnderChestInspector {
    constructor(player) {
        this.player = player;
    }

    runCommand(command) {
        try {
            const commandResult = this.player.runCommand(command);
            return commandResult.successCount > 0;
        } catch (err) {
            return false;
        }
    }

    hasAnyOfItem(item) {
        return this.runCommand(`testfor @s[hasitem={location=slot.enderchest,item=${item.id}}]`);
    }

    hasAtLeastItemQuantityInSlot(slot, item, quantity) {
        return this.runCommand(`testfor @s[hasitem={location=slot.enderchest, slot=${slot},item=${item.id},quantity=${quantity}..}]`);
    }

    async getEnderChestContents() {
        const enderChestContents = new Map();
        const foundTypes = new Set();

        for (const itemType of allItems) {
            if (this.hasAnyOfItem(itemType)) {
                foundTypes.add(itemType);
            }
        }

        for (let slot = 0; slot < 27; slot++) {
            for (const itemType of foundTypes) {
                if (!this.hasAtLeastItemQuantityInSlot(slot, itemType, 1)) continue;

                const qty = this.bisectExactQuantity(slot, new ItemStack(itemType));
                if (qty > 0) {
                    const constructedItem = new ItemStack(itemType, qty);
                    enderChestContents.set(slot, {
                        amount: qty,
                        slot,
                        item: constructedItem,
                    });
                }
            }
        }

        return enderChestContents;
    }

    bisectExactQuantity(slot, item) {
        let low = 1;
        let high = item.maxAmount;

        while (low <= high) {
            const mid = Math.floor((low + high) / 2);

            if (this.hasAtLeastItemQuantityInSlot(slot, item.type, mid)) {
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }

        return high;
    }
}

export function inspectEnderChest(player, selectedPlayer) {
    const enderChestInspector = new EnderChestInspector(selectedPlayer);

    enderChestInspector.getEnderChestContents().then((enderChestContents) => {
        const items = Array.from(enderChestContents.values());

        if (items.length === 0) {
            player.sendMessage(`§6"${selectedPlayer.name}"'s Ender Chest is empty.`);
            viewPlayerInventory(player);
            return;
        }

        showInventoryForm(player, selectedPlayer, items);
    }).catch((err) => {
        player.sendMessage(`§cFailed to retrieve Ender Chest contents: ${err.message}`);
        viewPlayerInventory(player);
    });
}


function viewPlayerInventory(player) {
    const players = [...world.getPlayers()];
    if (players.length === 0) {
        player.sendMessage("§cNo players are currently online.");
        adminMenu(player);
        return;
    }

    const playerSelectionForm = new ActionFormData()
        .title("§4Select Player")
        .body("Choose a player to inspect:");

    players.forEach((p) => playerSelectionForm.button(p.name));
    playerSelectionForm.button("§l§cBack");
    playerSelectionForm.button("§l§cExit");

    playerSelectionForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        if (response.selection === players.length) {
            // Back button
            openInventoryTypeMenu(player);
            return;
        }

        if (response.selection === players.length + 1) {
            // Exit button
            player.sendMessage("§aExiting menu...");
            return; // Exit completely
        }

        const selectedPlayer = players[response.selection];
        if (!selectedPlayer) {
            player.sendMessage("§cPlayer not found.");
            adminMenu(player);
            return;
        }

        const inventoryTypeForm = new ActionFormData()
            .title(`§6Inspect "${selectedPlayer.name}"`)
            .body("Select which inventory to inspect:")
            .button("Player Inventory", "textures/ui/csb_purchase_amazondevicewarning")
            .button("Ender Chest", "textures/ui/icon_blackfriday")
            .button("§l§cBack", "textures/ui/book_arrowleft_hover")
            .button("§l§cExit", "textures/ui/crossout");

        inventoryTypeForm.show(player).then((inventoryResponse) => {
            if (inventoryResponse.canceled) return; // Exit silently

            if (inventoryResponse.selection === 2) {
                // Back button
                viewPlayerInventory(player);
                return;
            }

            if (inventoryResponse.selection === 3) {
                // Exit button
                player.sendMessage("§aExiting menu...");
                return; // Exit completely
            }

            if (inventoryResponse.selection === 0) {
                inspectPlayerInventory(player, selectedPlayer);
            } else if (inventoryResponse.selection === 1) {
                inspectEnderChest(player, selectedPlayer);
            }
        }).catch((err) => {
            player.sendMessage(`§cAn error occurred: ${err.message}`);
            viewPlayerInventory(player);
        });
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err.message}`);
        adminMenu(player);
    });
}

function showInventoryForm(player, selectedPlayer, items) {
    const inventoryForm = new ActionFormData()
        .title(`§6"${selectedPlayer.name}"'s Inventory`)
        .body("Select an item to view.\n §4Warning:§r enchantments are not compatible at this moment.");


    items.forEach((entry) => {
        if (!entry || !entry.item || !entry.item.typeId) {
            console.warn("Invalid item detected, skipping:", entry); // Debugging
            return;
        }
        const itemName = entry.item.typeId || "§cUnknown Item";
        const itemAmount = entry.amount || 0;
        inventoryForm.button(`${itemAmount}x ${itemName}`);
    });

    inventoryForm.button("§l§cBack", "textures/ui/book_arrowleft_hover")
    inventoryForm.button("§l§cExit", "textures/ui/crossout");

    inventoryForm.show(player).then((response) => {
        if (response.canceled) return; // Exit silently

        if (response.selection === items.length) {
            // Back button
            viewPlayerInventory(player);
            return;
        }

        if (response.selection === items.length + 1) {
            // Exit button
            player.sendMessage("§aExiting menu...");
            return; // Exit completely
        }

        const selectedEntry = items[response.selection];
        if (!selectedEntry || !selectedEntry.item) {
            player.sendMessage("§cInvalid item selected.");
            showInventoryForm(player, selectedPlayer, items);
            return;
        }

        showItemDetails(player, selectedPlayer, selectedEntry, items);
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err.message}`);
        adminMenu(player);
    });
}

function showItemDetails(player, selectedPlayer, selectedEntry, items) {
    const { slot, item } = selectedEntry;

    if (!item || !item.typeId) {
        player.sendMessage("§cInvalid item selected.");
        showInventoryForm(player, selectedPlayer, items);
        return;
    }

    const customName = item.nameTag || item.typeId || "Unknown Item";
    const itemAmount = item.amount || 0;

    const enchantments = GetEnchants(item);
    const enchantmentDetails = enchantments.length
        ? enchantments.map((e) => `§e${e.type}: Level ${e.level}`).join("\n")
        : "None";

    const detailsForm = new ActionFormData()
        .title(`§6Item Details: ${customName}`)
        .body(
            `§eName: ${customName}\n` +
            `§eAmount: ${itemAmount}\n` +
            `§eSlot: ${slot}\n`
        )
        .button("Take Item")
        .button("Remove Item")
        .button("Back")
        .button("Admin Menu");

    detailsForm.show(player).then((response) => {
        if (response.canceled) {
            showInventoryForm(player, selectedPlayer, items);
            return;
        }

        switch (response.selection) {
            case 0: // Take Item
                showConfirmationMenu(player, selectedPlayer, selectedEntry, "take", items);
                break;

            case 1: // Remove Item
                showConfirmationMenu(player, selectedPlayer, selectedEntry, "remove", items);
                break;

            case 2: // Back to Inventory
                showInventoryForm(player, selectedPlayer, items);
                return;

            case 3: // Admin Menu
                adminMenu(player);
                return;

            default:
                player.sendMessage("§cInvalid selection.");
                break;
        }
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err.message}`);
        showInventoryForm(player, selectedPlayer, items);
    });
}


export function GetEnchants(item) {
    if (!item || !item.hasComponent("minecraft:enchantable")) return [];

    try {
        const enchantComponent = item.getComponent("minecraft:enchantable");
        if (!enchantComponent) return [];
        const enchantments = enchantComponent.getEnchantments();
        return enchantments.map((enchantment) => ({
            type: enchantment.type.id.replace("minecraft:", ""),
            level: enchantment.level,
        }));
    } catch (error) {
        console.error("Error retrieving enchantments:", error);
        return [];
    }
}

function showConfirmationMenu(player, selectedPlayer, selectedEntry, action, items) {
    const { slot, item } = selectedEntry;

    if (!item || !item.typeId) {
        player.sendMessage("§cInvalid item selected.");
        showItemDetails(player, selectedPlayer, selectedEntry, items);
        return;
    }

    const customName = item.nameTag || item.typeId || "Unknown Item";
    const itemAmount = item.amount || 0;

    const confirmationForm = new ActionFormData()
        .title("§6Confirmation")
        .body(
            `Are you sure you want to ${action}:\n\n` +
            `§e${itemAmount}x ${customName}?`
        )
        .button("§aYes")
        .button("§cNo");

    confirmationForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            // No or cancel
            showItemDetails(player, selectedPlayer, selectedEntry, items);
            return;
        }

        const enderChestInspector = new EnderChestInspector(selectedPlayer);

        if (action === "take" || action === "remove") {
            try {
                // Replace the slot with air first
                const command = `replaceitem entity "${selectedPlayer.name}" slot.enderchest ${slot} air`;

                selectedPlayer.runCommand(command);

                if (action === "take") {
                    const adminInventory = player.getComponent("minecraft:inventory")?.container;
                    if (adminInventory) {
                        const remaining = adminInventory.addItem(item);

                        if (remaining) {
                            player.sendMessage("§cYour inventory is full. The item couldn't be fully transferred.");
                        } else {
                            player.sendMessage(`§aYou took ${itemAmount}x ${customName} from "${selectedPlayer.name}".`);
                        }
                    } else {
                        player.sendMessage("§cUnable to access your inventory.");
                    }
                } else {
                    player.sendMessage(`§aYou removed ${itemAmount}x ${customName} from "${selectedPlayer.name}"'s Ender Chest.`);
                }

                // Refresh the Ender Chest inventory view
                enderChestInspector.getEnderChestContents().then((updatedContents) => {
                    const updatedItems = Array.from(updatedContents.values());

                    if (updatedItems.length === 0) {
                        player.sendMessage(`§6"${selectedPlayer.name}"'s Ender Chest is empty.`);
                        viewPlayerInventory(player);
                    } else {
                        showInventoryForm(player, selectedPlayer, updatedItems);
                    }
                }).catch((err) => {
                    player.sendMessage(`§cFailed to refresh Ender Chest: ${err.message}`);
                    viewPlayerInventory(player);
                });
            } catch (error) {
                player.sendMessage(`§cFailed to ${action} item: ${error.message}`);
                showInventoryForm(player, selectedPlayer, items); // Return to inventory on failure
            }
        }
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err.message}`);
        showItemDetails(player, selectedEntry, items);
    });
}
