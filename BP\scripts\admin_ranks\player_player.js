import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { storeSettingsMenu } from "./admin_ranks_menu";



export function playerstoreplayer(player) {
    const scoreboardId = "admin";
    const fakePlayerName = "playerstoresbutton";

    // Ensure the scoreboard exists
    const storeScoreboard = world.scoreboard.getObjective(scoreboardId)
        || world.scoreboard.addObjective(scoreboardId, "Admin Controls");

    // Get the current state of the button
    const participant = storeScoreboard.getParticipants().find((p) => p.displayName === fakePlayerName);
    const currentScore = participant ? storeScoreboard.getScore(participant) : 0;

    const form = new ActionFormData()
        .title("Player Store Management")
        .body("Choose an option:")
        .button(`Make Player-to-Player Stores Viewable: ${currentScore === 1 ? "§aOn" : "§cOff"}`, "textures/ui/MCoin")
        .button("Set Up Player-to-Player Stores", "textures/ui/mashup_world")
        .button("Remove Player Store", "textures/ui/cancel")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0:
                toggleStoreViewable(player, fakePlayerName, storeScoreboard);
                break;
            case 1:
                setupP2PStores(player);
                break;
            case 2:
                removePlayerStore(player);
                break;
            case 3:
                storeSettingsMenu(player);
                break;
        }
    });
}

function toggleStoreViewable(player, fakePlayerName, scoreboard) {
    const participant = scoreboard.getParticipants().find((p) => p.displayName === fakePlayerName);
    const currentScore = participant ? scoreboard.getScore(participant) : 0;
    const newScore = currentScore === 1 ? 0 : 1;

    try {
        scoreboard.setScore(fakePlayerName, newScore);
        player.sendMessage(`Player-to-Player Stores are now ${newScore === 1 ? "§aViewable" : "§cHidden"}.`);
    } catch (error) {
        player.sendMessage("§cFailed to toggle store visibility. Please try again.");
        console.error(error);
    }

    // Refresh the menu
    playerstoreplayer(player);
}

function setupP2PStores(player) {
    const setupForm = new ModalFormData()
        .title("Set Up Player-to-Player Stores")
        .textField("How many stores can each player have?", "Enter a number", "1")
        .textField("Cost per store (0 for free):", "Enter cost", "0");

    setupForm.show(player).then((response) => {
        if (response.canceled) {
            player.sendMessage("Store setup canceled.");
            playerstoreplayer(player);
            return;
        }

        const storeCount = parseInt(response.formValues[0]);
        const storeCost = parseInt(response.formValues[1]);

        if (isNaN(storeCount) || storeCount <= 0) {
            player.sendMessage("Invalid store count. Please enter a positive number.");
            setupP2PStores(player);
            return;
        }

        if (isNaN(storeCost) || storeCost < 0) {
            player.sendMessage("Invalid store cost. Please enter 0 or a positive number.");
            setupP2PStores(player);
            return;
        }

        try {
            const overworld = world.getDimension("overworld");
            // Wrap the “players” (fake scoreboard entries) in quotes if they might have spaces
            overworld.runCommandAsync(`scoreboard players set "P2Pcount" admin ${storeCount}`);
            overworld.runCommandAsync(`scoreboard players set "P2Pcost" admin ${storeCost}`);

            player.sendMessage(
                `Player-to-Player store setup complete:\n§aStores per player: ${storeCount}\n` +
                `§aCost per store: ${storeCost === 0 ? "Free" : storeCost}`
            );
        } catch (error) {
            player.sendMessage("§cFailed to save store setup. Please try again.");
            console.error(error);
        }

        playerstoreplayer(player);
    });
}

function removePlayerStore(player) {
    const storeList = [];

    // Gather all player-created store scoreboards
    world.scoreboard.getObjectives().forEach((objective) => {
        if (objective.id.includes("_store_")) {
            // Convert the scoreboard objective ID back to a user-facing name
            const storeName = objective.id
                .split("_store_")[1]
                .replace(/_/g, " ")
                .replace(/¤/g, "§")
                .replace(/¦/g, "&");
            storeList.push({ id: objective.id, name: storeName });
        }
    });

    if (storeList.length === 0) {
        player.sendMessage("§cThere are no stores available to remove.");
        return;
    }

    const storeForm = new ActionFormData()
        .title("Remove Player Store")
        .body("Select a store to remove:");

    storeList.forEach((store) => storeForm.button(store.name, "textures/ui/MCoin"));

    storeForm.show(player).then((response) => {
        if (response.canceled) return;
        const selectedStore = storeList[response.selection];
        confirmRemoveStore2(player, selectedStore);
    });
}

function confirmRemoveStore2(player, store) {
    const confirmForm = new ActionFormData()
        .title("Confirm Store Deletion")
        .body(
            `Are you sure you want to delete the store "${store.name}"?\n` +
            "All items and data will be lost forever."
        )
        .button("§l§cAgree", "textures/ui/check")
        .button("§l§aCancel", "textures/ui/cancel");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            player.sendMessage("§eStore deletion canceled.");
            return;
        }

        if (response.selection === 0) {
            const overworld = world.getDimension("overworld");

            // Remove the store scoreboard. Wrap the store id in quotes.
            overworld.runCommandAsync(`scoreboard objectives remove "${store.id}"`)
                .then(() => {
                    player.sendMessage(`§aThe store "${store.name}" has been successfully deleted.`);

                    // Update the player's store ownership on the "storeowner" scoreboard.
                    // Assume store.id is in the format "playername_store_storename".
                    const parts = store.id.split("_store_");
                    if (parts.length < 2) {
                        player.sendMessage("§cInvalid store ID format.");
                        return removePlayerStore(player);
                    }
                    const playerName = parts[0]; // e.g. "johnsmith"
                    const storeName = parts[1];  // e.g. "mystore"

                    const storeOwnerObj = world.scoreboard.getObjective("storeowner");
                    if (storeOwnerObj) {
                        // Remove the fake store entry for this store.
                        const fakeStoreKey = `${playerName}_${storeName}`;
                        storeOwnerObj.removeParticipant(fakeStoreKey);

                        // Update the overall store count using the fake key: playername_count
                        const countKey = `${playerName}_count`;
                        let countParticipant = storeOwnerObj.getParticipants().find(p => p.displayName === countKey);
                        if (!countParticipant) {
                            // Create it with a score of 0 if it doesn't exist.
                            storeOwnerObj.setScore(countKey, 0);
                        } else {
                            const currentCount = storeOwnerObj.getScore(countParticipant);
                            const newCount = currentCount > 0 ? currentCount - 1 : 0;
                            storeOwnerObj.setScore(countKey, newCount);
                        }
                        player.sendMessage(`§aYour store count has been updated.`);
                    }

                    // Return to the Remove Player Store menu.
                    removePlayerStore(player);
                })
                .catch((error) => {
                    player.sendMessage("§cFailed to delete the store. Please try again.");
                    console.error(error);
                    // Even on error, return to the remove store menu.
                    removePlayerStore(player);
                });
        }
    }).catch((error) => {
        player.sendMessage("§cAn error occurred while confirming deletion: " + error);
        removePlayerStore(player);
    });
}

