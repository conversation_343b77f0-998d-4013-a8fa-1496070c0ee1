import { world, system } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { storeSettingsMenu } from "./admin_ranks_menu";



/*************************************************
 * Sell Shop Main Menu
 *************************************************/

export function sellShop(player, rank) {
    const shops = getSellShops();

    const adminObjective = world.scoreboard.getObjective("admin");
    let toggleLabel = "Sell Shop: Off";
    if (adminObjective) {
        const participant = adminObjective.getParticipants().find(p => p.displayName === "sellshop");
        const currentScore = participant ? adminObjective.getScore(participant) : 0;
        toggleLabel = currentScore === 1 ? "Sell Shop: §aOn" : "Sell Shop: §cOff";
    }

    const form = new ActionFormData()
        .title("Sell Shop")
        .body(shops.length === 0 ? "No shops available. Add a shop to get started!" : "Select a shop to edit or create a new one.")
        .button(toggleLabel, "textures/ui/mashup_world")  // Toggle button
        .button("Add Shop", "textures/ui/mining_fatigue_effect")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    shops.forEach(shop => {
        const displayName = shop.replace(/¤/g, "§").replace(/_/g, " ");
        form.button(displayName, "textures/ui/MCoin");
    });

    form.show(player).then(response => {
        if (response.canceled) return;

        if (response.selection === 0) {
            // Toggle the sell shop visibility.
            if (adminObjective) {
                const participant = adminObjective.getParticipants().find(p => p.displayName === "sellshop");
                const currentScore = participant ? adminObjective.getScore(participant) : 0;
                const newScore = currentScore === 1 ? 0 : 1;
                adminObjective.setScore("sellshop", newScore);
            }
            sellShop(player, rank); // Refresh menu, pass rank if available.
        } else if (response.selection === 1) {
            createSellShop(player, () => sellShop(player, rank));
        } else if (response.selection === 2) {
            // Here we call storeSettingsMenu.
            // If you have a valid rank available in this context, pass it; otherwise, the default in storeSettingsMenu will be used.
            storeSettingsMenu(player, rank);
        } else {
            const selectedShop = shops[response.selection - 3];
            if (selectedShop) {
                manageShop(player, selectedShop);
            } else {
                player.sendMessage("Failed to open shop. Please try again.");
            }
        }
    });
}

/*************************************************
 * Create New Sell Shop
 *************************************************/
function createSellShop(player, callback) {
    const form = new ModalFormData()
        .title("Create New Sell Shop")
        .textField("Enter shop name:", "Shop Name");

    form.show(player).then(response => {
        if (response.canceled) return;

        let shopName = response.formValues[0];
        if (!shopName) {
            player.sendMessage("Shop name cannot be empty!");
            return;
        }

        // Replace special characters for scoreboard compatibility
        shopName = shopName
            .replace(/&/g, "¦")
            .replace(/\s+/g, "_")
            .replace(/§/g, "¤");

        const scoreboardName = `Sellshop_${shopName}`;
        try {
            world.getDimension("overworld").runCommand(`scoreboard objectives add ${scoreboardName} dummy`);
            player.sendMessage(`Sell Shop '${shopName.replace(/¤/g, "§").replace(/¦/g, "&")}' has been created!`);
            if (callback) callback();
        } catch (error) {
            player.sendMessage("Failed to create Sell Shop. Please try again.");
            console.error(error);
        }
    });
}

/*************************************************
 * Manage Shop Menu & Items
 *************************************************/
function manageShop(player, shopName) {
    const items = getShopItems(shopName);

    const form = new ActionFormData()
        .title(`Manage Shop: ${shopName.replace(/¤/g, "§").replace(/¦/g, "&")}`)
        .body(items.length === 0 ? "No items added yet. Add items to get started!" : "Manage your shop items.")
        .button("§l§cBack to Shop List") // Back button at the top
        .button("Add Item", "textures/ui/mashup_world")
        .button("Remove Shop", "textures/ui/mining_fatigue_effect");

    items.forEach(item => {
        // Determine friendly name:
        // If the identifier contains a colon, remove the part before it.
        let friendlyIdentifier = item.baseIdentifier;
        if (friendlyIdentifier.includes(":")) {
            friendlyIdentifier = friendlyIdentifier.split(":")[1];
        }
        // Replace underscores with spaces
        friendlyIdentifier = friendlyIdentifier.replace(/_/g, " ");
        form.button(`${friendlyIdentifier} x${item.amount} - Price: ${item.price}`, "textures/ui/MCoin");
    });

    form.show(player).then(response => {
        if (response.canceled) return;

        if (response.selection === 0) {
            sellShop(player); // Back button logic
        } else if (response.selection === 1) {
            addItemToShop(player, shopName);
        } else if (response.selection === 2) {
            confirmRemoveShop(player, shopName);
        } else {
            const selectedItem = items[response.selection - 3]; // Adjust index for the three buttons at top
            if (selectedItem) {
                editShopItem(player, shopName, selectedItem);
            }
        }
    });
}


/*************************************************
 * Add Item (with Identifier, Amount, Price)
 *************************************************/
function addItemToShop(player, shopName) {
    const form = new ModalFormData()
        .title(`Add Item to ${shopName.replace(/¤/g, "§")}`)
        .textField("Enter item identifier (e.g., stone, dirt, or custom:identifier):", "Item Identifier")
        .textField("Enter amount (number of items):", "Amount", "1")
        .textField("Enter sell price for the item:", "Sell Price (e.g., 20)");

    form.show(player).then(async (response) => {
        if (response.canceled) {
            manageShop(player, shopName);
            return;
        }
        let [inputIdentifier, inputAmount, sellPrice] = response.formValues;

        if (!inputIdentifier) {
            player.sendMessage("Item identifier cannot be empty!");
            return;
        }
        // Normalize identifier: lowercase, trim, replace spaces with underscores
        inputIdentifier = inputIdentifier.toLowerCase().trim().replace(/\s+/g, "_");
        if (!inputIdentifier.includes(":")) {
            inputIdentifier = `minecraft:${inputIdentifier}`;
        }

        const amount = parseInt(inputAmount, 10);
        if (isNaN(amount) || amount <= 0) {
            player.sendMessage("Invalid amount! Please enter a positive number.");
            return;
        }

        sellPrice = parseInt(sellPrice, 10);
        if (isNaN(sellPrice) || sellPrice <= 0) {
            player.sendMessage("Invalid sell price! Please enter a positive number.");
            return;
        }

        // Confirmation form
        const confirmForm = new ActionFormData()
            .title("Confirm Item Addition")
            .body(`Identifier: ${inputIdentifier}\nAmount: ${amount}\nPrice: ${sellPrice}\n\nIs this correct?`)
            .button("Yes, Save Item")
            .button("No, Cancel");
        confirmForm.show(player).then(confirmResponse => {
            if (confirmResponse.canceled || confirmResponse.selection === 1) {
                manageShop(player, shopName);
                return;
            }
            // Create the fake participant name as: identifier_amount
            const fakeParticipantName = `${inputIdentifier}_${amount}`;

            // Validate the item with a give command
            player.runCommandAsync(`give @s ${inputIdentifier} 0`).then(() => {
                const shopScoreboardName = `Sellshop_${shopName
                    .replace(/§/g, "¤")
                    .replace(/&/g, "¦")
                    .replace(/\s+/g, "_")}`;
                try {
                    world.getDimension("overworld").runCommand(`scoreboard players set "${fakeParticipantName}" ${shopScoreboardName} ${sellPrice}`);
                    const friendlyName = inputIdentifier.replace(/^minecraft:/, "").replace(/_/g, " ");
                    player.sendMessage(`Added ${friendlyName} x${amount} to ${shopName.replace(/¤/g, "§")} with sell price ${sellPrice}.`);
                    manageShop(player, shopName);
                } catch (error) {
                    player.sendMessage("Failed to add item. Please try again.");
                    console.error(error);
                }
            }).catch(error => {
                if (error.message.includes("Syntax error: Unexpected")) {
                    player.sendMessage(`Invalid item identifier: '${inputIdentifier}'. Please try again.`);
                } else {
                    player.sendMessage("An error occurred while validating the item. Please try again.");
                }
                console.error(`Error validating item '${inputIdentifier}':`, error);
                manageShop(player, shopName);
            });
        });
    });
}

/*************************************************
 * Retrieve Shop Items (Parsing Identifier & Amount)
 *************************************************/
function getShopItems(shopName) {
    const shopScoreboardName = `Sellshop_${shopName
        .replace(/§/g, "¤")
        .replace(/&/g, "¦")
        .replace(/\s+/g, "_")}`;
    const items = [];

    try {
        const objective = world.scoreboard.getObjective(shopScoreboardName);
        if (!objective) {
            console.error(`Scoreboard for shop '${shopName}' not found.`);
            return items;
        }
        const participants = objective.getParticipants();
        participants.forEach(participant => {
            const fullIdentifier = participant.displayName; // e.g. "minecraft:stone_3"
            const cost = objective.getScore(participant);
            // Split the fake name by the last underscore to get base identifier and amount
            const lastUnderscoreIndex = fullIdentifier.lastIndexOf("_");
            if (lastUnderscoreIndex !== -1) {
                const baseIdentifier = fullIdentifier.substring(0, lastUnderscoreIndex);
                const amount = fullIdentifier.substring(lastUnderscoreIndex + 1);
                items.push({
                    fullIdentifier, // The stored participant name (identifier_amount)
                    baseIdentifier, // The pure identifier (e.g. "minecraft:stone")
                    amount,         // The amount as a string
                    price: cost
                });
            } else {
                // Fallback if no underscore (should not normally happen)
                items.push({
                    fullIdentifier,
                    baseIdentifier: fullIdentifier,
                    amount: "1",
                    price: cost
                });
            }
        });
    } catch (error) {
        console.error(`Failed to retrieve items for shop '${shopName}':`, error);
    }
    return items;
}

/*************************************************
 * Edit Shop Item Menu
 *************************************************/
function editShopItem(player, shopName, item) {
    // Build a friendly display name: if a colon exists, use only the part after it
    let friendlyName = item.baseIdentifier;
    if (friendlyName.includes(":")) {
        friendlyName = friendlyName.split(":")[1];
    }
    friendlyName = friendlyName.replace(/_/g, " ") + ` x${item.amount}`;

    const form = new ActionFormData()
        .title(`Edit Item: ${friendlyName}`)
        .button("Change Amount")
        .button("Change Price")
        .button("Remove Item")
        .button("§l§cBack to Shop Menu");

    form.show(player).then(response => {
        if (response.canceled) {
            manageShop(player, shopName);
            return;
        }

        if (response.selection === 0) {
            changeItemAmount(player, shopName, item);
        } else if (response.selection === 1) {
            changeItemPrice(player, shopName, item);
        } else if (response.selection === 2) {
            confirmRemoveItem(player, shopName, item.fullIdentifier);
        } else {
            manageShop(player, shopName);
        }
    });
}


/*************************************************
 * Change Item Amount
 *************************************************/
function changeItemAmount(player, shopName, item) {
    const shopScoreboardName = `Sellshop_${shopName
        .replace(/§/g, "¤")
        .replace(/&/g, "¦")
        .replace(/\s+/g, "_")}`;
    const form = new ModalFormData()
        .title(`Change Amount for ${item.baseIdentifier.replace(/^minecraft:/, "").replace(/_/g, " ")}`)
        .textField("Enter new amount:", "New Amount", item.amount);

    form.show(player).then(response => {
        if (response.canceled) {
            editShopItem(player, shopName, item);
            return;
        }

        const newAmount = parseInt(response.formValues[0], 10);
        if (isNaN(newAmount) || newAmount <= 0) {
            player.sendMessage("Invalid amount! Please enter a positive number.");
            changeItemAmount(player, shopName, item);
            return;
        }

        // Build the new fake participant name using the original baseIdentifier and new amount
        const newFakeName = `${item.baseIdentifier}_${newAmount}`;
        const currentPrice = item.price;
        try {
            // Remove the old entry...
            world.getDimension("overworld").runCommand(`scoreboard players reset "${item.fullIdentifier}" ${shopScoreboardName}`);
            // ...and add a new one with the new amount and same price.
            world.getDimension("overworld").runCommand(`scoreboard players set "${newFakeName}" ${shopScoreboardName} ${currentPrice}`);
            player.sendMessage(`Updated amount to ${newAmount} for ${item.baseIdentifier.replace(/^minecraft:/, "").replace(/_/g, " ")}`);
        } catch (error) {
            player.sendMessage("Failed to update amount. Please try again.");
            console.error(error);
        }
        manageShop(player, shopName);
    });
}

/*************************************************
 * Change Item Price
 *************************************************/
function changeItemPrice(player, shopName, item) {
    const shopScoreboardName = `Sellshop_${shopName
        .replace(/§/g, "¤")
        .replace(/&/g, "¦")
        .replace(/\s+/g, "_")}`;
    const form = new ModalFormData()
        .title(`Change Price for ${item.baseIdentifier.replace(/^minecraft:/, "").replace(/_/g, " ")}`)
        .textField("Enter new price:", "New Price", `${item.price}`);

    form.show(player).then(response => {
        if (response.canceled) {
            editShopItem(player, shopName, item);
            return;
        }

        const newPrice = parseInt(response.formValues[0], 10);
        if (isNaN(newPrice) || newPrice <= 0) {
            player.sendMessage("Invalid price! Please enter a positive number.");
            changeItemPrice(player, shopName, item);
            return;
        }

        try {
            world.getDimension("overworld").runCommand(`scoreboard players set "${item.fullIdentifier}" ${shopScoreboardName} ${newPrice}`);
            player.sendMessage(`Updated price for ${item.baseIdentifier.replace(/^minecraft:/, "").replace(/_/g, " ")} to ${newPrice}`);
        } catch (error) {
            player.sendMessage("Failed to update price. Please try again.");
            console.error(error);
        }
        manageShop(player, shopName);
    });
}

/*************************************************
 * Confirm & Remove Item
 *************************************************/
function confirmRemoveItem(player, shopName, fullIdentifier) {
    const form = new ActionFormData()
        .title("Confirm Item Removal")
        .body(`Are you sure you want to remove the item '${fullIdentifier}' from the shop?`)
        .button("Yes, Remove Item")
        .button("No, Go Back");

    form.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            manageShop(player, shopName);
            return;
        }
        if (response.selection === 0) {
            removeItem(player, shopName, fullIdentifier);
        }
    });
}

function removeItem(player, shopName, identifier) {
    const shopScoreboardName = `Sellshop_${shopName
        .replace(/§/g, "¤")
        .replace(/&/g, "¦")
        .replace(/\s+/g, "_")}`;
    try {
        world.getDimension("overworld").runCommand(`scoreboard players reset "${identifier}" ${shopScoreboardName}`);
        player.sendMessage(`Item '${identifier}' has been removed.`);
    } catch (error) {
        player.sendMessage(`Error removing item: ${error.message}`);
    }
    manageShop(player, shopName);
}

/*************************************************
 * Retrieve Sell Shop Names
 *************************************************/
function getSellShops() {
    const objectives = world.scoreboard.getObjectives();
    const shopNames = [];

    objectives.forEach(obj => {
        if (obj.id.startsWith("Sellshop_")) {
            let shopName = obj.id.replace("Sellshop_", "");
            shopName = shopName
                .replace(/¤/g, "§")
                .replace(/¦/g, "&")
                .replace(/_/g, " "); // Reverse replacements for display
            shopNames.push(shopName);
        }
    });

    return shopNames;
}


