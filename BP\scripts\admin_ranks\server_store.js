import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { storeSettingsMenu } from "./admin_ranks_menu";

// -------------------------------------------------------------------------
// Simple encode/decode functions (using underscores for spaces)
// -------------------------------------------------------------------------
function encodeString(input) {
    return input.replace(/&/g, "¦").replace(/§/g, "¤").replace(/ /g, "_");
}

function decodeString(input) {
    return input.replace(/¦/g, "&").replace(/¤/g, "§").replace(/_/g, " ");
}


export function configureServerStores(player, rank) {
    const scoreboardId = "admin";
    const visibilityKey = encodeString("menu_serverStores");

    const adminScoreboard = world.scoreboard.getObjective(scoreboardId) || world.scoreboard.addObjective(scoreboardId, "Admin Controls");

    const participant = adminScoreboard.getParticipants().find(p => p.displayName === visibilityKey);
    const currentScore = participant ? adminScoreboard.getScore(participant) : 0;

    const serverStoresForm = new ActionFormData()
        .title("Configure Server Stores")
        .body("Manage server stores and their settings.")
        .button("Add Shop", "textures/ui/mashup_world")
        .button(`Make Server Stores Viewable ${currentScore === 1 ? "§a(On)" : "§c(Off)"}`, "textures/ui/mining_fatigue_effect")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    const scoreboards = world.scoreboard.getObjectives();
    const stores = scoreboards.filter(obj => obj.id.startsWith("store_"));

    stores.forEach(store => {
        const storeName = decodeString(store.id.replace("store_", ""));
        serverStoresForm.button(storeName, "textures/ui/MCoin");
    });

    serverStoresForm.show(player).then((response) => {
        if (response.canceled) return;

        if (response.selection === 0) {
            addStore(player);
        } else if (response.selection === 1) {
            const newScore = currentScore === 1 ? 0 : 1;
            adminScoreboard.setScore(visibilityKey, newScore);
            player.sendMessage(`Server Stores are now ${newScore === 1 ? "visible" : "hidden"}.`);
            configureServerStores(player);
        } else if (response.selection === 2) {
            storeSettingsMenu(player, rank);
        } else {
            const selectedStore = stores[response.selection - 3].id.replace("store_", "");
            manageStore(player, selectedStore);
        }
    });
}





function addStore(player) {
    const addStoreForm = new ModalFormData()
        .title("Add Store")
        .textField("Enter a name for the store :", "Store Name");

    addStoreForm.show(player).then((response) => {
        if (response.canceled) {
            configureServerStores(player);
            return;
        }

        let storeName = response.formValues[0].trim();

        if (!storeName || storeName.length === 0) {
            player.sendMessage("§cStore name cannot be empty.");
            configureServerStores(player);
            return;
        }

        const sanitizedStoreName = encodeString(storeName);
        const scoreboardId = `store_${sanitizedStoreName}`;

        player.runCommandAsync(`scoreboard objectives add "${scoreboardId}" dummy "${storeName}"`).then(() => {
            player.sendMessage(`§aStore '${storeName}' added successfully.`);
            configureServerStores(player);
        }).catch(err => {
            player.sendMessage(`§cFailed to create store scoreboard: ${err}`);
        });
    }).catch(err => {
        player.sendMessage(`§cAn error occurred while adding the store: ${err}`);
        configureServerStores(player);
    });
}



function confirmRemoveStore(player, storeName) {
    const confirmForm = new ActionFormData()
        .title("Confirm Store Removal")
        .body(`Are you sure you want to remove the store '${storeName}'?`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            configureServerStores(player);
            return;
        }

        player.runCommandAsync(`scoreboard objectives remove store_${storeName}`).then(() => {
            player.sendMessage(`§aStore '${storeName}' removed successfully.`);
            configureServerStores(player);
        }).catch(err => {
            player.sendMessage(`§cFailed to remove store: ${err}`);
        });
    });
}

function manageStore(player, storeName) {
    const decodedStoreName = decodeString(storeName);

    const manageStoreForm = new ActionFormData()
        .title(`§0Manage Store: ${decodedStoreName}`)
        .body("Manage store settings and items.");

    // Add the Back button first.
    manageStoreForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");
    // Add buttons for managing items.
    manageStoreForm
        .button("Add Item", "textures/ui/Add-Ons_Nav_Icon36x36")
        .button("Remove Store", "textures/ui/bad_omen_effect");

    const storeObjective = world.scoreboard.getObjective(`store_${storeName}`);
    const items = storeObjective ? storeObjective.getParticipants() : [];

    // Process each stored item.
    items.forEach((item) => {
        const fullName = item.displayName; // e.g. "minecraft:stone_3" or "zombie:dirt_5"
        const lastUnderscore = fullName.lastIndexOf("_");
        if (lastUnderscore === -1) return; // Skip invalid format.
        const baseIdentifier = fullName.substring(0, lastUnderscore);
        const setAmount = fullName.substring(lastUnderscore + 1);
        const score = storeObjective.getScore(item);
        // Remove any namespace (e.g. "minecraft:" or "zombie:")
        let friendlyName = baseIdentifier;
        if (friendlyName.includes(":")) {
            friendlyName = friendlyName.split(":")[1];
        }
        friendlyName = friendlyName.replace(/_/g, " ");
        manageStoreForm.button(`${decodeString(friendlyName)} x${setAmount} - $${score}`, "textures/ui/icon_minecoin_9x9");
    });

    manageStoreForm.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0: // Back button
                configureServerStores(player);
                break;
            case 1: // Add Item button
                addItemToStore(player, storeName);
                break;
            case 2: // Remove Store button
                confirmRemoveStore(player, storeName);
                break;
            default:
                // Adjust for the three buttons at the top.
                const selectedItemIndex = response.selection - 3;
                if (selectedItemIndex >= 0 && selectedItemIndex < items.length) {
                    const selectedItem = items[selectedItemIndex].displayName;
                    createEditItemForm(player, storeName, selectedItem);
                } else {
                    player.sendMessage("§cInvalid selection. Please try again.");
                }
        }
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err}`);
    });
}






function createEditItemForm(player, storeName, selectedItem) {
    const storeObjective = world.scoreboard.getObjective(`store_${storeName}`);
    if (!storeObjective) {
        player.sendMessage(`§cStore '${decodeString(storeName)}' does not exist.`);
        manageStore(player, storeName);
        return;
    }

    // Find the selected item's participant.
    const participant = storeObjective.getParticipants().find(
        (p) => p.displayName === selectedItem
    );

    if (!participant) {
        player.sendMessage("§cInvalid item selected.");
        manageStore(player, storeName);
        return;
    }

    // Parse the fake name using the last underscore.
    const lastUnderscore = selectedItem.lastIndexOf("_");
    if (lastUnderscore === -1) {
        player.sendMessage("§cInvalid item format.");
        manageStore(player, storeName);
        return;
    }
    const baseIdentifier = selectedItem.substring(0, lastUnderscore);
    const currentAmount = selectedItem.substring(lastUnderscore + 1);
    const currentPrice = storeObjective.getScore(participant);

    // Create a friendly display name.
    let friendlyName = baseIdentifier;
    if (friendlyName.includes(":")) {
        friendlyName = friendlyName.split(":")[1];
    }
    friendlyName = friendlyName.replace(/_/g, " ");

    const editItemForm = new ModalFormData()
        .title(`Edit Item: ${decodeString(friendlyName)}`)
        .textField("Enter the new amount (set size):", "Amount", `${currentAmount}`)
        .textField("Enter the new price:", "Price", `${currentPrice}`)
        .toggle("Remove this item?", false);

    editItemForm.show(player).then((response) => {
        if (response.canceled) {
            manageStore(player, storeName);
            return;
        }

        const [newAmountStr, newPriceStr, removeItem] = response.formValues;
        if (removeItem) {
            // Remove the item from the scoreboard.
            player.runCommandAsync(`scoreboard players reset "${selectedItem}" store_${storeName}`).then(() => {
                player.sendMessage(`§aItem '${decodeString(friendlyName)}' removed from store '${decodeString(storeName)}'.`);
                manageStore(player, storeName);
            }).catch((err) => {
                player.sendMessage(`§cFailed to remove item: ${err}`);
            });
        } else {
            const newAmount = parseInt(newAmountStr, 10);
            const newPrice = parseInt(newPriceStr, 10);
            if (isNaN(newAmount) || newAmount < 1 || isNaN(newPrice) || newPrice < 0) {
                player.sendMessage("§cInvalid amount or price entered. Please try again.");
                manageStore(player, storeName);
                return;
            }
            // Build the new fake name.
            const newFakeName = `${baseIdentifier}_${newAmount}`;
            // If the fake name has changed, remove the old entry first.
            const updateCommand = (newFakeName !== selectedItem)
                ? `scoreboard players set "${newFakeName}" store_${storeName} ${newPrice} && scoreboard players reset "${selectedItem}" store_${storeName}`
                : `scoreboard players set "${selectedItem}" store_${storeName} ${newPrice}`;
            player.runCommandAsync(updateCommand).then(() => {
                player.sendMessage(`§aItem '${decodeString(friendlyName)}' updated: amount ${newAmount}, price $${newPrice}.`);
                manageStore(player, storeName);
            }).catch((err) => {
                player.sendMessage(`§cFailed to update item: ${err}`);
            });
        }
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err}`);
    });
}


function addItemToStore(player, storeName) {
    const addItemForm = new ModalFormData()
        .title(`Add Item to ${decodeString(storeName)}`)
        .textField("Enter the item identifier (e.g., stone, dirt, or custom:identifier):", "Item Identifier")
        .textField("Enter the amount (set size):", "Amount", "1")
        .textField("Enter the price for the item:", "Price", "0");

    addItemForm.show(player).then((response) => {
        if (response.canceled) {
            manageStore(player, storeName);
            return;
        }

        let [itemIdentifier, amountStr, price] = response.formValues;

        // If the identifier doesn't include a colon, assume it's a vanilla item.
        if (!itemIdentifier.includes(":")) {
            itemIdentifier = `minecraft:${itemIdentifier}`;
        }
        // Replace any spaces with underscores for consistency.
        itemIdentifier = itemIdentifier.replace(/\s+/g, '_');

        const amount = parseInt(amountStr, 10);
        if (!itemIdentifier || isNaN(amount) || amount < 1 || isNaN(price) || price < 0) {
            player.sendMessage("§cInvalid item identifier, amount, or price. Please try again.");
            manageStore(player, storeName);
            return;
        }

        // Build the fake participant name in the format: identifier_amount
        const fakePlayerName = `${itemIdentifier}_${amount}`;

        // Validate the item by trying to give 0 of it.
        player.runCommandAsync(`give @s ${itemIdentifier} 0`).then(() => {
            player.runCommandAsync(`scoreboard players set "${fakePlayerName}" store_${storeName} ${price}`).then(() => {
                player.sendMessage(`§aItem '${itemIdentifier}' (set of ${amount}) added to store '${decodeString(storeName)}' with price $${price}.`);
                manageStore(player, storeName);
            }).catch((err) => {
                player.sendMessage(`§cFailed to add item to scoreboard: ${err}`);
            });
        }).catch(() => {
            player.sendMessage(`§cInvalid item identifier: '${itemIdentifier}'. Please try again.`);
            manageStore(player, storeName);
        });
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err}`);
        manageStore(player, storeName);
    });
}




function playerstoreplayer(player) {

    const adminObjective = world.scoreboard.getObjective("admin");
    let toggleLabel = "Make Player-to-Player Stores Viewable: Off";
    if (adminObjective) {
        const participant = adminObjective.getParticipants().find(p => p.displayName === "playerstoresbutton");
        const currentScore = participant ? adminObjective.getScore(participant) : 0;
        toggleLabel = currentScore === 1 ? "Make Player-to-Player Stores Viewable: §aOn" : "Make Player-to-Player Stores Viewable: §cOff";
    }
    const form = new ActionFormData()
        .title("Player Store Management")
        .body("Choose an option:")
        .button(toggleLabel, "textures/ui/MCoin")
        .button("Remove Player Store", "textures/ui/cancel")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    form.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0:
                if (adminObjective) {
                    const participant = adminObjective.getParticipants().find(p => p.displayName === "playerstoresbutton");
                    const currentScore = participant ? adminObjective.getScore(participant) : 0;
                    const newScore = currentScore === 1 ? 0 : 1;
                    adminObjective.setScore("playerstoresbutton", newScore);
                }
                playerstoreplayer(player); // Refresh menu
                break;
            case 1:
                // Open the list of stores for deletion
                removePlayerStore(player);
                break;
            case 2:
                playerMenuSettings(player)
                break;
        }
    });
}

function removePlayerStore(player) {
    const storeList = [];

    // Gather all player-created store scoreboards
    world.scoreboard.getObjectives().forEach((objective) => {
        if (objective.id.includes("_store_")) {
            const storeName = objective.id
                .split("_store_")[1]
                .replace(/_/g, " ")
                .replace(/¤/g, "§")
                .replace(/¦/g, "&"); // Reverse replacements
            storeList.push({ id: objective.id, name: storeName });
        }
    });

    if (storeList.length === 0) {
        player.sendMessage("§cThere are no stores available to remove.");
        return;
    }

    const storeForm = new ActionFormData()
        .title("Remove Player Store")
        .body("Select a store to remove:");

    storeList.forEach((store) => storeForm.button(store.name, "textures/ui/MCoin"));

    storeForm.show(player).then((response) => {
        if (response.canceled) return;

        const selectedStore = storeList[response.selection];

        // Confirm store deletion
        confirmRemoveStore2(player, selectedStore);
    });
}

function confirmRemoveStore2(player, store) {
    const confirmForm = new ActionFormData()
        .title("Confirm Store Deletion")
        .body(`Are you sure you want to delete the store "${store.name}"?\nAll items and data will be lost forever.`)
        .button("§l§cAgree", "textures/ui/check")
        .button("§l§aCancel", "textures/ui/cancel");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            player.sendMessage("§eStore deletion canceled.");
            return;
        }

        if (response.selection === 0) {
            const overworld = world.getDimension("overworld");

            // Remove the selected store scoreboard
            overworld.runCommandAsync(`scoreboard objectives remove ${store.id}`)
                .then(() => {
                    player.sendMessage(`§aThe store "${store.name}" has been successfully deleted.`);

                    // Extract the player name from the store ID and remove the fake player
                    const playerName = store.id.split("_store_")[0]; // Extract player name
                    const storeOwnerObjective = world.scoreboard.getObjective("storeowner");

                    if (storeOwnerObjective) {
                        const fakePlayerName = `${playerName}_store`;
                        const fakePlayer = storeOwnerObjective.getParticipants().find(participant => participant.displayName === fakePlayerName);

                        if (fakePlayer) {
                            overworld.runCommandAsync(`scoreboard players reset "${fakePlayerName}" storeowner`)
                                .then(() => {
                                    player.sendMessage(`§aRemoved store ownership for ${playerName}.`);
                                })
                                .catch((error) => {
                                    player.sendMessage("§cFailed to remove store ownership. Please try again.");
                                    console.error(error);
                                });
                        }
                    }
                })
                .catch((error) => {
                    player.sendMessage("§cFailed to delete the store. Please try again.");
                    console.error(error);
                });
        }
    });
}
