import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { mainMenu } from "../mainmenu.js"; // If you have a separate main menu file

/*************************************************
 * 1) formRank – Main Rank Menu (Dynamic)
 *************************************************/
export function formRank(player) {
    const adminObjective = world.scoreboard.getObjective("admin");
    if (!adminObjective) {
        player.sendMessage("§cNo 'admin' scoreboard found.");
        return;
    }

    // 1) Find player's admin score
    const adminParticipant = adminObjective.getParticipants().find(p => p.displayName === `admin_${player.name}`);
    if (!adminParticipant) {
        player.sendMessage("§cYou are not recognized as an admin with a rank.");
        return;
    }
    const adminScore = adminObjective.getScore(adminParticipant);

    // 2) Find rank participant matching that score, e.g. "rank_Mod"
    const rankParticipant = adminObjective
        .getParticipants()
        .find(p => p.displayName.startsWith("rank_") && adminObjective.getScore(p) === adminScore);
    if (!rankParticipant) {
        player.sendMessage(`§cNo rank found for your admin score of '${adminScore}'.`);
        return;
    }

    const rawRankName = rankParticipant.displayName.replace("rank_", "");
    const rankScoreboardName = `rank_${rawRankName}`;

    // 3) Load rank_<Name> scoreboard
    const rankObjective = world.scoreboard.getObjective(rankScoreboardName);
    if (!rankObjective) {
        player.sendMessage(`§cThe rank scoreboard '${rankScoreboardName}' does not exist.`);
        return;
    }

    // 4) Build form with dynamic toggles
    const form = new ActionFormData()
        .title(`§4Rank Menu: ${rawRankName}`)
        .body("Select an option:");

    const buttonActions = [];
    const isEnabled = (fakeName) => {
        const part = rankObjective.getParticipants().find(p => p.displayName === fakeName);
        return part && rankObjective.getScore(part) === 1;
    };

    // Command Prompt
    if (isEnabled("commandprompt")) {
        form.button("CommandPrompt", "textures/ui/creator_glyph_color");
        buttonActions.push(() => commandPromptMenu(player, { name: rawRankName }));
    }

    // Teleport
    if (isEnabled("tp_settings")) {
        form.button("Teleport", "textures/ui/up_arrow");
        buttonActions.push(() => teleportMenu(player, { name: rawRankName }));
    }

    // Player Settings
    if (isEnabled("player_settings")) {
        form.button("Player Settings", "textures/ui/bubble");
        buttonActions.push(() => playerSettingsMenu(player, { name: rawRankName }));
    }

    // Store Settings
    if (isEnabled("stores_settings")) {
        form.button("Store Settings", "textures/ui/icon_panda");
        buttonActions.push(() => storeSettingsMenu(player, { name: rawRankName }));
    }

    // Gamemode Settings
    if (isEnabled("gamemode_settings")) {
        form.button("Gamemode Settings", "textures/ui/op");
        buttonActions.push(() => gamemodeMenu(player, { name: rawRankName }));
    }

    // Custom Commands
    if (isEnabled("custom_commands_settings")) {
        form.button("Custom Commands", "textures/ui/icon_timer");
        buttonActions.push(() => customCommandsMenu(player, { name: rawRankName }));
    }

    // Back => main admin menu
    form.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => mainMenu(player));

    form.show(player).then(resp => {
        if (resp.canceled) return;
        const idx = resp.selection;
        if (buttonActions[idx]) buttonActions[idx]();
    }).catch(err => {
        player.sendMessage(`§cError in Rank Menu: ${err}`);
    });
}

/*************************************************
 * 2) Command Prompt Submenu
 *************************************************/
export async function commandPromptMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const rankObjective = world.scoreboard.getObjective(rankScoreboardName);

    if (!rankObjective) {
        player.sendMessage(`§cThe rank scoreboard '${rankScoreboardName}' does not exist.`);
        return;
    }

    const bannedCommands = getScoreboardEntries(rankScoreboardName, "commandprompt_");
    const blockedKeywords = getScoreboardEntries(rankScoreboardName, "commandkey_");

    const commandForm = new ModalFormData()
        .title(`§4Command Prompt: ${rank.name}`)
        .textField("Enter command to execute:", "e.g., say Hello");

    const response = await commandForm.show(player);
    if (response.canceled) {
        formRank(player);
        return;
    }

    const command = response.formValues[0]?.trim();
    if (!command) {
        player.sendMessage("§cNo command entered.");
        formRank(player);
        return;
    }

    if (bannedCommands.includes(command)) {
        player.sendMessage(`§cThis command is banned: "${command}".`);
        formRank(player);
        return;
    }

    const words = command.split(" ");
    if (blockedKeywords.some(keyword => words[0] === keyword)) {
        player.sendMessage(`§cCannot use commands starting with '${words[0]}'`);
        formRank(player);
        return;
    }

    try {
        await player.runCommandAsync(command);
        player.sendMessage(`§aCommand executed: /${command}`);
    } catch (err) {
        player.sendMessage(`§cFailed to execute: ${err.message}`);
    }

    formRank(player);
}

function getScoreboardEntries(scoreboardName, prefix) {
    const obj = world.scoreboard.getObjective(scoreboardName);
    if (!obj) return [];
    return obj.getParticipants()
        .map(p => p.displayName)
        .filter(name => name.startsWith(prefix))
        .map(name => decodeCommandString(name.replace(prefix, "")));
}

function decodeCommandString(encodedCommand) {
    return encodedCommand
        .replace(/_at_/g, "@")
        .replace(/_space_/g, " ")
        .replace(/_slash_/g, "/")
        .replace(/_colon_/g, ":")
        .replace(/_dot_/g, ".");
}

/*************************************************
 * 3) Teleport Submenu
 *************************************************/
export function teleportMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const options = [
        { name: "Tplayer", desc: "Teleport to Other Players", icon: "textures/ui/check" },
        { name: "TPPlayer", desc: "Teleport Player to Player", icon: "textures/ui/arrow" }
    ];
    const scores = {};

    const obj = world.scoreboard.getObjective(rankScoreboardName);
    options.forEach(opt => {
        const part = obj.getParticipants().find(p => p.displayName === opt.name);
        scores[opt.name] = part ? obj.getScore(part) : 0;
    });

    const form = new ActionFormData()
        .title("Teleport Settings")
        .body("Select a teleport option:");

    const buttonActions = [];
    options.forEach(opt => {
        if (scores[opt.name] === 1) {
            form.button(opt.desc, opt.icon);
            if (opt.name === "Tplayer") {
                buttonActions.push(() => teleportToPlayer(player, rank));
            } else {
                buttonActions.push(() => teleportPlayerToPlayer(player, rank));
            }
        }
    });

    // Back => rank
    form.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => formRank(player));

    form.show(player).then(resp => {
        if (resp.canceled) return;
        const idx = resp.selection;
        if (buttonActions[idx]) buttonActions[idx]();
    }).catch(err => {
        player.sendMessage(`§cError in teleportMenu: ${err}`);
    });
}

function teleportToPlayer(player, rank) {
    const list = [...world.getPlayers()];
    if (!list.length) {
        player.sendMessage("§cNo players online.");
        teleportMenu(player, rank);
        return;
    }

    const f = new ActionFormData()
        .title("Teleport to Player")
        .body("Select a player:");
    const actions = [];
    list.forEach(p => {
        f.button(p.name);
        actions.push(() => confirmTeleport(player, rank, p));
    });

    // Back
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => teleportMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (resp.selection >= actions.length) return;
        actions[resp.selection]();
    });
}

function confirmTeleport(player, rank, target) {
    const { x, y, z } = target.location;
    const cForm = new ActionFormData()
        .title("Confirm Teleport")
        .body(`Teleport to ${target.name} at [${Math.floor(x)},${Math.floor(y)},${Math.floor(z)}]?`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    cForm.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            teleportToPlayer(player, rank);
            return;
        }
        player.runCommandAsync(`tp @s ${x} ${y} ${z}`);
        player.sendMessage(`§aTeleported to ${target.name}.`);
    });
}

function teleportPlayerToPlayer(player, rank) {
    const list = [...world.getPlayers()];
    if (list.length < 2) {
        player.sendMessage("§cNot enough players online.");
        teleportMenu(player, rank);
        return;
    }

    const names = list.map(p => p.name);
    const form = new ModalFormData()
        .title("Teleport Player to Player")
        .dropdown("Select who to teleport:", names)
        .dropdown("Select destination player:", names);

    form.show(player).then(resp => {
        if (resp.canceled) {
            teleportMenu(player, rank);
            return;
        }
        const src = list[resp.formValues[0]];
        const dest = list[resp.formValues[1]];
        if (!src || !dest || src === dest) {
            player.sendMessage("§cInvalid selection.");
            teleportMenu(player, rank);
            return;
        }
        confirmTeleportPlayerToPlayer(player, rank, src, dest);
    });
}

function confirmTeleportPlayerToPlayer(player, rank, src, dest) {
    const { x, y, z } = dest.location;
    const cForm = new ActionFormData()
        .title("Confirm Teleport")
        .body(`Teleport ${src.name} to ${dest.name}?\nCoords: [${Math.floor(x)},${Math.floor(y)},${Math.floor(z)}]`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    cForm.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            teleportPlayerToPlayer(player, rank);
            return;
        }
        player.runCommandAsync(`tp "${src.name}" ${x} ${y} ${z}`);
        player.sendMessage(`§aTeleported ${src.name} to ${dest.name}.`);
    });
}

/*************************************************
 * 4) Player Settings Submenu
 *************************************************/
export function playerSettingsMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const obj = world.scoreboard.getObjective(rankScoreboardName);

    // Potential toggles
    const possibleSettings = [
        { name: "PlayerSettings_banmenu", desc: "Ban Menu", icon: "textures/ui/icon_trending", func: () => banMenu(player, rank) },
        { name: "PlayerSettings_mpb", desc: "Modify Players' Score", icon: "textures/ui/icon_map", func: () => modifyPlayerBalance(player, rank) },
        { name: "PlayerSettings_inventory", desc: "View Player Inventory", icon: "textures/ui/chest_icon", func: () => viewPlayerInventoryMenu(player, rank) },
        { name: "PlayerSettings_playerbases", desc: "Player Bases", icon: "textures/ui/home_icon", func: () => playerBaseMenu(player, rank) },
    ];

    const form = new ActionFormData()
        .title("Player Settings")
        .body("Choose a player-related option:");

    const buttonActions = [];
    possibleSettings.forEach(opt => {
        const part = obj.getParticipants().find(p => p.displayName === opt.name);
        const score = part ? obj.getScore(part) : 0;
        if (score === 1) {
            form.button(opt.desc, opt.icon);
            buttonActions.push(opt.func);
        }
    });

    // Back => rank
    form.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => formRank(player));

    form.show(player).then(resp => {
        if (resp.canceled) return;
        const idx = resp.selection;
        if (buttonActions[idx]) buttonActions[idx]();
    });
}

/*************************************************
 * 4a) Ban Menu
 *************************************************/
export function banMenu(player, rank) {
    const banObj = world.scoreboard.getObjective("BanList");
    if (!banObj) {
        player.sendMessage("§cNo Ban List found. Creating one...");
        player.runCommandAsync('scoreboard objectives add BanList dummy "Banned Players"');
        banMenu(player, rank);
        return;
    }

    const form = new ActionFormData()
        .title("§4Ban Menu")
        .body("Manage the ban list.")
        .button("Add Player", "textures/ui/anvil_icon")
        .button("Type Player Manually", "textures/ui/haste_effect")
        .button("View Ban List", "textures/ui/icon_book_writable")
        .button("Back", "textures/ui/book_arrowleft_hover");

    form.show(player).then(resp => {
        if (resp.canceled) return;
        switch (resp.selection) {
            case 0: addPlayerToList(player, rank); break;
            case 1: typePlayerManually(player, rank); break;
            case 2: viewBanList(player, rank); break;
            case 3: playerSettingsMenu(player, rank); break;
        }
    });
}

function addPlayerToList(player, rank) {
    const onlinePlayers = [...world.getPlayers()];
    const banned = getBanList();
    const eligible = onlinePlayers.filter(p => !banned.includes(p.name));

    if (!eligible.length) {
        player.sendMessage("§cNo eligible players to ban.");
        banMenu(player, rank);
        return;
    }

    const f = new ActionFormData()
        .title("Add Player to Ban")
        .body("Select a player to ban:");
    const actions = [];
    eligible.forEach(p => {
        f.button(p.name);
        actions.push(() => confirmBanPlayer(player, rank, p.name));
    });

    // Back
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => banMenu(player, rank));

    f.show(player).then(r => {
        if (r.canceled) return;
        if (r.selection < actions.length) actions[r.selection]();
    });
}

function typePlayerManually(player, rank) {
    const f = new ModalFormData()
        .title("Type Player In")
        .textField("Name to ban:", "Player Name");

    f.show(player).then(r => {
        if (r.canceled || !r.formValues[0].trim()) {
            banMenu(player, rank);
            return;
        }
        confirmBanPlayer(player, rank, r.formValues[0].trim());
    });
}

function confirmBanPlayer(player, rank, name) {
    const c = new ActionFormData()
        .title("Confirm Ban")
        .body(`Ban ${name}?`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    c.show(player).then(r => {
        if (r.canceled || r.selection === 1) {
            banMenu(player, rank);
            return;
        }
        player.runCommandAsync(`scoreboard players add "ban_${name}" BanList 1`).then(() => {
            player.sendMessage(`§a${name} has been banned.`);
            const target = [...world.getPlayers()].find(p => p.name === name);
            if (target) target.runCommandAsync('kick You have been banned.');
        });
    });
}

function viewBanList(player, rank) {
    const banned = getBanList();
    if (!banned.length) {
        player.sendMessage("§cBan List is empty.");
        banMenu(player, rank);
        return;
    }

    const f = new ActionFormData()
        .title("§4Ban List")
        .body("Select a player to unban:");
    const actions = [];
    banned.forEach(n => {
        f.button(n);
        actions.push(() => confirmUnbanPlayer(player, rank, n));
    });

    // Back
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => banMenu(player, rank));

    f.show(player).then(r => {
        if (r.canceled) return;
        if (r.selection < actions.length) actions[r.selection]();
    });
}

function confirmUnbanPlayer(player, rank, name) {
    const c = new ActionFormData()
        .title("Confirm Unban")
        .body(`Unban ${name}?`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    c.show(player).then(r => {
        if (r.canceled || r.selection === 1) {
            viewBanList(player, rank);
            return;
        }
        player.runCommandAsync(`scoreboard players reset "ban_${name}" BanList`).then(() => {
            player.sendMessage(`§a${name} unbanned.`);
            viewBanList(player, rank);
        });
    });
}

function getBanList() {
    const obj = world.scoreboard.getObjective("BanList");
    if (!obj) return [];
    return obj.getParticipants()
        .filter(p => p.displayName.startsWith("ban_"))
        .map(p => p.displayName.replace("ban_", ""));
}

/*************************************************
 * 4b) Modify Player Balance
 *************************************************/
export function modifyPlayerBalance(player, rank) {
    const online = [...world.getPlayers()];
    if (!online.length) {
        player.sendMessage("§cNo players online.");
        playerSettingsMenu(player, rank);
        return;
    }

    const form = new ActionFormData()
        .title("Modify Player Balance")
        .body("Select a player:");
    const actions = [];
    online.forEach(p => {
        form.button(p.name);
        actions.push(() => inputRewardAmount(player, rank, p));
    });

    // Back
    form.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => playerSettingsMenu(player, rank));

    form.show(player).then(r => {
        if (r.canceled) return;
        if (r.selection < actions.length) actions[r.selection]();
    });
}

function inputRewardAmount(player, rank, target) {
    const m = new ModalFormData()
        .title(`Modify Balance for ${target.name}`)
        .textField("Amount (+ for reward, - for penalty):", "Amount", "0");

    m.show(player).then(r => {
        if (r.canceled) {
            modifyPlayerBalance(player, rank);
            return;
        }
        const amt = parseInt(r.formValues[0]);
        if (isNaN(amt)) {
            player.sendMessage("§cInvalid amount.");
            inputRewardAmount(player, rank, target);
            return;
        }
        confirmModifyBalance(player, rank, target, amt);
    });
}

function confirmModifyBalance(player, rank, target, amt) {
    const op = amt >= 0 ? "Reward" : "Penalty";
    const absVal = Math.abs(amt);

    const c = new ActionFormData()
        .title(`Confirm ${op}`)
        .body(`Are you sure you want to ${op.toLowerCase()} ${target.name} by ${absVal} Money?`)
        .button("Yes", "textures/ui/check")
        .button("No", "textures/ui/crossout");

    c.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            modifyPlayerBalance(player, rank);
            return;
        }
        // Scoreboard command
        const cmd = `scoreboard players add "${target.name}" Money ${amt}`;
        world.getDimension("overworld").runCommandAsync(cmd).then(() => {
            player.sendMessage(`§aSuccessfully ${op.toLowerCase()}ed ${target.name} by ${absVal}.`);
        }).catch(err => {
            player.sendMessage(`§cFailed updating Money: ${err}`);
        });
        playerSettingsMenu(player, rank);
    });
}

/*************************************************
 * 4c) View Player Inventory / Ender Chest
 *************************************************/
export function viewPlayerInventoryMenu(player, rank) {
    // This is the “entry point” to choose a player
    // -> Then open the inventory or ender chest logic
    const players = [...world.getPlayers()];
    if (!players.length) {
        player.sendMessage("§cNo players online.");
        playerSettingsMenu(player, rank);
        return;
    }

    const selForm = new ActionFormData()
        .title("§6Select Player")
        .body("Choose a player to inspect:");

    players.forEach(p => selForm.button(p.name));

    // Back
    selForm.button("Back", "textures/ui/book_arrowleft_hover");
    const actions = players.map(p => () => openInventoryTypeMenu(player, rank, p));
    actions.push(() => playerSettingsMenu(player, rank));

    selForm.show(player).then(resp => {
        if (resp.canceled) return;
        if (resp.selection < actions.length) actions[resp.selection]();
    });
}

function openInventoryTypeMenu(player, rank, selectedPlayer) {
    // Prompt: View main inventory or ender chest
    const invForm = new ActionFormData()
        .title(`Inspect ${selectedPlayer.name}`)
        .body("Which inventory to view?")
        .button("Player Inventory", "textures/ui/chest_icon")
        .button("Ender Chest", "textures/ui/icon_blackfriday")
        .button("Back", "textures/ui/book_arrowleft_hover");

    invForm.show(player).then(r => {
        if (r.canceled) return;
        switch (r.selection) {
            case 0:
                inspectPlayerInventory(player, rank, selectedPlayer);
                break;
            case 1:
                inspectEnderChest(player, rank, selectedPlayer);
                break;
            default:
                viewPlayerInventoryMenu(player, rank);
                break;
        }
    });
}

// =========== Player Inventory Logic ===========
function inspectPlayerInventory(player, rank, target) {
    const inv = target.getComponent("minecraft:inventory")?.container;
    if (!inv) {
        player.sendMessage("§cUnable to fetch inventory.");
        viewPlayerInventoryMenu(player, rank);
        return;
    }

    // Gather items
    const items = [];
    for (let slot = 0; slot < inv.size; slot++) {
        const it = inv.getItem(slot);
        if (it) items.push({ slot, item: it });
    }
    showInventoryForm(player, rank, target, items, "player");
}

function showInventoryForm(player, rank, target, items, type) {
    // type = "player" or "ender"
    const invForm = new ActionFormData()
        .title(`§6"${target.name}"'s ${type === "ender" ? "Ender Chest" : "Inventory"}`)
        .body("Select an item to view details.");

    items.forEach(entry => {
        if (!entry.item?.typeId) return;
        const name = entry.item.typeId;
        const amt = entry.item.amount || 1;
        invForm.button(`${amt}x ${name}`);
    });

    invForm.button("Back", "textures/ui/book_arrowleft_hover");
    invForm.button("Exit", "textures/ui/crossout");

    invForm.show(player).then(resp => {
        if (resp.canceled) return;
        if (resp.selection === items.length) {
            // Back
            viewPlayerInventoryMenu(player, rank);
            return;
        }
        if (resp.selection === items.length + 1) {
            // Exit
            player.sendMessage("§aExiting menu...");
            return;
        }
        const selEntry = items[resp.selection];
        if (!selEntry) {
            showInventoryForm(player, rank, target, items, type);
            return;
        }
        showItemDetails(player, rank, target, selEntry, items, type);
    });
}

function showItemDetails(player, rank, target, selEntry, items, type) {
    const { slot, item } = selEntry;
    if (!item?.typeId) {
        showInventoryForm(player, rank, target, items, type);
        return;
    }

    const customName = item.nameTag || item.typeId;
    const amt = item.amount || 0;

    const details = new ActionFormData()
        .title(`Item: ${customName}`)
        .body(
            `§eName: ${customName}\n` +
            `§eAmount: ${amt}\n` +
            `§eSlot: ${slot}\n` // If you track enchantments, show them here
        )
        .button("Take Item")
        .button("Remove Item")
        .button("Back")
        .button("Exit");

    details.show(player).then(resp => {
        if (resp.canceled) {
            showInventoryForm(player, rank, target, items, type);
            return;
        }
        switch (resp.selection) {
            case 0: // Take
                confirmTakeItem(player, rank, target, selEntry, items, type);
                break;
            case 1: // Remove
                confirmRemoveItem(player, rank, target, selEntry, items, type);
                break;
            case 2: // Back
                showInventoryForm(player, rank, target, items, type);
                break;
            case 3: // Exit
                player.sendMessage("§aExiting menu...");
                break;
        }
    });
}

function confirmTakeItem(player, rank, target, selEntry, items, type) {
    const { slot, item } = selEntry;
    const name = item.nameTag || item.typeId;
    const amt = item.amount || 0;

    const c = new ActionFormData()
        .title("Confirm Take")
        .body(`Take ${amt}x ${name} from ${target.name}?`)
        .button("Yes")
        .button("No");

    c.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            showItemDetails(player, rank, target, selEntry, items, type);
            return;
        }
        // Actually remove from the target and add to admin
        const inv = target.getComponent("minecraft:inventory")?.container;
        const adminInv = player.getComponent("minecraft:inventory")?.container;
        if (!inv || !adminInv) {
            player.sendMessage("§cError fetching inventories.");
            showInventoryForm(player, rank, target, items, type);
            return;
        }

        inv.setItem(slot, null);
        const leftover = adminInv.addItem(item);
        if (leftover) {
            player.sendMessage("§cYour inventory is too full to take all items.");
        } else {
            player.sendMessage(`§aTook ${amt}x ${name} from ${target.name}.`);
        }

        // Refresh
        const newItems = [];
        for (let i = 0; i < inv.size; i++) {
            const it = inv.getItem(i);
            if (it) newItems.push({ slot: i, item: it });
        }
        showInventoryForm(player, rank, target, newItems, type);
    });
}

function confirmRemoveItem(player, rank, target, selEntry, items, type) {
    const { slot, item } = selEntry;
    const name = item.nameTag || item.typeId;
    const amt = item.amount || 0;

    const c = new ActionFormData()
        .title("Confirm Remove")
        .body(`Remove ${amt}x ${name} from ${target.name}?`)
        .button("Yes")
        .button("No");

    c.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            showItemDetails(player, rank, target, selEntry, items, type);
            return;
        }
        // Actually remove from target
        const inv = target.getComponent("minecraft:inventory")?.container;
        if (!inv) {
            player.sendMessage("§cError fetching target's inventory.");
            showInventoryForm(player, rank, target, items, type);
            return;
        }
        inv.setItem(slot, null);
        player.sendMessage(`§aRemoved ${amt}x ${name} from ${target.name}.`);

        // Refresh
        const newItems = [];
        for (let i = 0; i < inv.size; i++) {
            const it = inv.getItem(i);
            if (it) newItems.push({ slot: i, item: it });
        }
        showInventoryForm(player, rank, target, newItems, type);
    });
}

// =========== Ender Chest Logic ===========
function inspectEnderChest(player, rank, target) {
    // Uses the script-based inspector approach
    const inspector = new EnderChestInspector(target);
    inspector.getEnderChestContents().then(contents => {
        const items = Array.from(contents.values());
        if (!items.length) {
            player.sendMessage(`§6"${target.name}"'s Ender Chest is empty.`);
            viewPlayerInventoryMenu(player, rank);
            return;
        }
        showEnderChestForm(player, rank, target, items);
    }).catch(err => {
        player.sendMessage(`§cFailed: ${err.message}`);
        viewPlayerInventoryMenu(player, rank);
    });
}

function showEnderChestForm(player, rank, target, items) {
    const form = new ActionFormData()
        .title(`§6"${target.name}"'s Ender Chest`)
        .body("Select an item to view.");

    items.forEach(entry => {
        const itemName = entry.item.typeId || "Unknown";
        const amt = entry.amount || 0;
        form.button(`${amt}x ${itemName}`);
    });

    form.button("Back", "textures/ui/book_arrowleft_hover");
    form.button("Exit", "textures/ui/crossout");

    form.show(player).then(resp => {
        if (resp.canceled) return;
        if (resp.selection === items.length) {
            viewPlayerInventoryMenu(player, rank);
            return;
        }
        if (resp.selection === items.length + 1) {
            player.sendMessage("§aExiting menu...");
            return;
        }
        const selItem = items[resp.selection];
        showEnderChestItemDetails(player, rank, target, selItem, items);
    });
}

function showEnderChestItemDetails(player, rank, target, selEntry, allItems) {
    const { slot, item } = selEntry;
    const name = item.nameTag || item.typeId;
    const amt = item.amount || 0;

    const df = new ActionFormData()
        .title(`Item: ${name}`)
        .body(`§eName: ${name}\n§eAmount: ${amt}\n§eSlot: ${slot}`)
        .button("Take Item")
        .button("Remove Item")
        .button("Back")
        .button("Exit");

    df.show(player).then(resp => {
        if (resp.canceled) {
            showEnderChestForm(player, rank, target, allItems);
            return;
        }
        switch (resp.selection) {
            case 0:
                confirmEnderChestAction(player, rank, target, selEntry, "take", allItems);
                break;
            case 1:
                confirmEnderChestAction(player, rank, target, selEntry, "remove", allItems);
                break;
            case 2:
                showEnderChestForm(player, rank, target, allItems);
                break;
            case 3:
                player.sendMessage("§aExiting menu...");
                break;
        }
    });
}

function confirmEnderChestAction(player, rank, target, selEntry, action, allItems) {
    const { slot, item } = selEntry;
    const name = item.nameTag || item.typeId;
    const amt = item.amount || 0;

    const cf = new ActionFormData()
        .title("Confirm Action")
        .body(`Are you sure you want to ${action} ${amt}x ${name}?`)
        .button("Yes")
        .button("No");

    cf.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            showEnderChestItemDetails(player, rank, target, selEntry, allItems);
            return;
        }
        // Actually remove item from Ender Chest
        try {
            const removeCmd = `replaceitem entity "${target.name}" slot.enderchest ${slot} air`;
            target.runCommandAsync(removeCmd);

            if (action === "take") {
                const adminInv = player.getComponent("minecraft:inventory")?.container;
                if (adminInv) {
                    const leftover = adminInv.addItem(item);
                    if (leftover) {
                        player.sendMessage("§cYour inventory is too full to take everything.");
                    } else {
                        player.sendMessage(`§aTook ${amt}x ${name} from ${target.name}'s Ender Chest.`);
                    }
                }
            } else {
                player.sendMessage(`§aRemoved ${amt}x ${name} from ${target.name}'s Ender Chest.`);
            }

            // Refresh
            const inspector = new EnderChestInspector(target);
            inspector.getEnderChestContents().then(contents => {
                const newItems = Array.from(contents.values());
                if (!newItems.length) {
                    player.sendMessage(`§6"${target.name}"'s Ender Chest is empty.`);
                    viewPlayerInventoryMenu(player, rank);
                } else {
                    showEnderChestForm(player, rank, target, newItems);
                }
            });
        } catch (err) {
            player.sendMessage(`§cFailed to ${action} item: ${err}`);
            showEnderChestForm(player, rank, target, allItems);
        }
    });
}

// The EnderChestInspector from your snippet
import { ItemTypes } from "@minecraft/server";
const allItems = ItemTypes.getAll();

class EnderChestInspector {
    constructor(player) {
        this.player = player;
    }
    runCommand(command) {
        try {
            const cr = this.player.runCommand(command);
            return cr.successCount > 0;
        } catch {
            return false;
        }
    }
    hasAnyOfItem(itemType) {
        return this.runCommand(`testfor @s[hasitem={location=slot.enderchest,item=${itemType.id}}]`);
    }
    hasAtLeastItemQuantityInSlot(slot, itemType, qty) {
        return this.runCommand(`testfor @s[hasitem={location=slot.enderchest,slot=${slot},item=${itemType.id},quantity=${qty}..}]`);
    }
    async getEnderChestContents() {
        const contents = new Map();
        const foundTypes = new Set();

        // First find which itemTypes exist at all
        for (const it of allItems) {
            if (this.hasAnyOfItem(it)) {
                foundTypes.add(it);
            }
        }

        // Then bisect each possible slot
        for (let slot = 0; slot < 27; slot++) {
            for (const itemType of foundTypes) {
                if (!this.hasAtLeastItemQuantityInSlot(slot, itemType, 1)) continue;
                const qty = this.bisectExactQuantity(slot, itemType);
                if (qty > 0) {
                    // Construct item
                    const constructedItem = { typeId: itemType.id, amount: qty };
                    contents.set(slot, { slot, amount: qty, item: constructedItem });
                }
            }
        }
        return contents;
    }
    bisectExactQuantity(slot, itemType) {
        let low = 1;
        let high = 64; // Typically max stack could be 64

        while (low <= high) {
            const mid = Math.floor((low + high) / 2);
            if (this.hasAtLeastItemQuantityInSlot(slot, itemType, mid)) {
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }
        return high;
    }
}

/*************************************************
 * 4d) Player Bases Submenu
 *************************************************/
export function playerBaseMenu(player, rank) {
    const form = new ActionFormData()
        .title("§0Base Navigation Menu")
        .body("§bChoose an option to navigate bases:")
        .button("By Security Systems")
        .button("By Home Bases")
        .button("§cBack");

    form.show(player).then(resp => {
        if (resp.canceled) return;
        switch (resp.selection) {
            case 0:
                playerBaseList(player, rank);
                break;
            case 1:
                homeBaseList(player, rank);
                break;
            default:
                playerSettingsMenu(player, rank);
                break;
        }
    });
}

// =========== Security Systems ===========

function playerBaseList(player, rank) {
    const sb = world.scoreboard.getObjective("basesecurity");
    if (!sb) {
        player.sendMessage("§cNo bases found in the system.");
        return;
    }

    // Gather bases
    const baseList = sb.getParticipants().map(part => {
        const entry = part.displayName;
        const m = entry.match(/^(.+?)_(-?\d+)_(-?\d+)_(-?\d+)$/);
        if (m) {
            const [_, playerName, x, y, z] = m;
            return { playerName, x: parseInt(x), y: parseInt(y), z: parseInt(z) };
        }
        return null;
    }).filter(Boolean);

    if (!baseList.length) {
        player.sendMessage("§cNo valid bases found.");
        return;
    }

    const f = new ActionFormData()
        .title("§0Player Base List")
        .body("Select a base to view:")
        .button("§cBack");
    const actions = [];
    baseList.forEach(b => {
        f.button(`${b.playerName}: X=${b.x}, Y=${b.y}, Z=${b.z}`);
        actions.push(() => confirmTeleport(player, b, () => playerBaseList(player, rank)));
    });

    f.show(player).then(resp => {
        if (resp.canceled || resp.selection === 0) {
            playerBaseMenu(player, rank);
            return;
        }
        if (resp.selection > actions.length) return;
        actions[resp.selection - 1]();
    });
}

function confirmTeleport(player, base, goBack) {
    const cForm = new ActionFormData()
        .title("Base Options")
        .body(`§bChoose an action:\nPlayer: ${base.playerName}\nCoords: X=${base.x}, Y=${base.y}, Z=${base.z}`)
        .button("Yes, Teleport")
        .button("§cNo, Cancel")
        .button("§4Remove Base");

    cForm.show(player).then(resp => {
        if (resp.canceled) {
            goBack();
            return;
        }
        switch (resp.selection) {
            case 0: // Teleport
                player.runCommandAsync(`tp @s ${base.x} ${base.y} ${base.z}`)
                    .then(() => {
                        player.sendMessage(`§aTeleported to ${base.playerName}'s base at X=${base.x}, Y=${base.y}, Z=${base.z}.`);
                    }).catch(e => {
                        player.sendMessage("§cFailed to teleport.");
                    });
                break;
            case 1:
                goBack();
                break;
            case 2:
                confirmRemoveBase(player, base, goBack);
                break;
        }
    });
}

function confirmRemoveBase(player, base, goBack) {
    const f = new ActionFormData()
        .title("Confirm Removal")
        .body(`Remove base from:\nPlayer: ${base.playerName}\nCoords: X=${base.x}, Y=${base.y}, Z=${base.z}?`)
        .button("§aYes, Remove")
        .button("§cNo, Cancel");

    f.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            goBack();
            return;
        }
        removeBase(player, base);
        goBack();
    });
}

function removeBase(player, base) {
    const sb = world.scoreboard.getObjective("basesecurity");
    if (!sb) {
        player.sendMessage("§cNo scoreboard found for bases.");
        return;
    }

    const fakeName = `${base.playerName}_${Math.round(base.x)}_${Math.round(base.y)}_${Math.round(base.z)}`;
    const part = sb.getParticipants().find(p => p.displayName === fakeName);
    if (part) {
        try {
            sb.removeParticipant(part);
            player.sendMessage(`§aRemoved base at X=${base.x}, Y=${base.y}, Z=${base.z} for ${base.playerName}.`);
        } catch (err) {
            player.sendMessage("§cFailed removing base from system.");
        }
    } else {
        player.sendMessage(`§cNo base found for ${base.playerName} at X=${base.x}, Z=${base.z}.`);
    }
}

// =========== Home Bases ===========

function homeBaseList(player, rank) {
    const sb = world.scoreboard.getObjective("PlayerTP");
    if (!sb) {
        player.sendMessage("§cNo home bases found.");
        return;
    }

    const parts = sb.getParticipants();
    const playersWithHomes = new Set();
    for (const p of parts) {
        const entry = p.displayName;
        if (entry.match(/^(.+?)_cords_(\d+)_x$/)) {
            const plName = entry.split("_cords_")[0];
            playersWithHomes.add(plName);
        }
    }

    const sortedPlayers = Array.from(playersWithHomes).sort();
    if (!sortedPlayers.length) {
        player.sendMessage("§cNo valid home bases found.");
        return;
    }

    const f = new ActionFormData()
        .title("§0Home Base List")
        .body("Select a player to view their home bases:")
        .button("§cBack");
    const actions = [];
    sortedPlayers.forEach(pl => {
        f.button(pl);
        actions.push(() => showPlayerHomeBases(player, rank, pl));
    });

    f.show(player).then(resp => {
        if (resp.canceled || resp.selection === 0) {
            playerBaseMenu(player, rank);
            return;
        }
        if (resp.selection > actions.length) return;
        actions[resp.selection - 1]();
    });
}

function showPlayerHomeBases(player, rank, pName) {
    const sb = world.scoreboard.getObjective("PlayerTP");
    if (!sb) {
        player.sendMessage(`§cNo home bases scoreboard.`);
        return;
    }

    const parts = sb.getParticipants();
    // All home baseNumbers
    const baseNums = parts.filter(part => part.displayName.startsWith(`${pName}_cords_`))
        .map(part => {
            const m = part.displayName.match(new RegExp(`^${pName}_cords_(\\d+)_x$`));
            return m ? parseInt(m[1]) : null;
        })
        .filter(Boolean);

    if (!baseNums.length) {
        player.sendMessage(`§cNo home bases found for ${pName}.`);
        return;
    }

    const f = new ActionFormData()
        .title(`${pName}'s Home Bases`)
        .body("Select a base:")
        .button("Back");
    const actions = [];

    baseNums.forEach(num => {
        const xPart = parts.find(pt => pt.displayName === `${pName}_cords_${num}_x`);
        const yPart = parts.find(pt => pt.displayName === `${pName}_cords_${num}_y`);
        const zPart = parts.find(pt => pt.displayName === `${pName}_cords_${num}_z`);
        const nickPart = parts.find(pt => pt.displayName.startsWith(`${pName}_cords_${num}_nickname`));

        if (!xPart || !yPart || !zPart) return;
        const x = sb.getScore(xPart);
        const y = sb.getScore(yPart);
        const z = sb.getScore(zPart);

        const nickname = nickPart ? nickPart.displayName.split("_").pop() : `Home ${num}`;
        f.button(`${nickname}:\nX=${x}, Y=${y}, Z=${z}`);
        actions.push(() => homeBaseOptions(player, rank, pName, num, { x, y, z }));
    });

    f.show(player).then(resp => {
        if (resp.canceled || resp.selection === 0) {
            homeBaseList(player, rank);
            return;
        }
        if (resp.selection <= actions.length) {
            actions[resp.selection - 1]();
        }
    });
}

function homeBaseOptions(player, rank, pName, baseNum, coords) {
    const f = new ActionFormData()
        .title(`Home ${baseNum} Options`)
        .body(`Coordinates: X=${coords.x}, Y=${coords.y}, Z=${coords.z}\nWhat do you want to do?`)
        .button("Teleport")
        .button("Remove")
        .button("Back");

    f.show(player).then(resp => {
        if (resp.canceled) {
            showPlayerHomeBases(player, rank, pName);
            return;
        }
        switch (resp.selection) {
            case 0: // Teleport
                player.runCommandAsync(`tp @s ${coords.x} ${coords.y} ${coords.z}`)
                    .then(() => {
                        player.sendMessage(`§aTeleported to X=${coords.x},Y=${coords.y},Z=${coords.z}`);
                    }).catch(() => {
                        player.sendMessage("§cTeleport failed.");
                    });
                showPlayerHomeBases(player, rank, pName);
                break;
            case 1: // Remove
                confirmRemoveHomeBase(player, rank, pName, baseNum, coords, () => {
                    showPlayerHomeBases(player, rank, pName);
                });
                break;
            default:
                showPlayerHomeBases(player, rank, pName);
                break;
        }
    });
}

function confirmRemoveHomeBase(player, rank, pName, baseNum, coords, goBack) {
    const c = new ActionFormData()
        .title("Confirm Removal")
        .body(`Remove home base #${baseNum} for ${pName} at X=${coords.x},Y=${coords.y},Z=${coords.z}?`)
        .button("Yes")
        .button("No");

    c.show(player).then(resp => {
        if (resp.canceled || resp.selection === 1) {
            goBack();
            return;
        }
        removePlayerHomeBase(player, pName, baseNum, goBack);
    });
}

function removePlayerHomeBase(player, pName, baseNum, goBack) {
    const sb = world.scoreboard.getObjective("PlayerTP");
    if (!sb) {
        player.sendMessage("§cNo PlayerTP scoreboard found.");
        return;
    }
    const baseFakeName = `${pName}_cords_${baseNum}`;
    try {
        player.runCommandAsync(`scoreboard players reset "${baseFakeName}_x" PlayerTP`);
        player.runCommandAsync(`scoreboard players reset "${baseFakeName}_y" PlayerTP`);
        player.runCommandAsync(`scoreboard players reset "${baseFakeName}_z" PlayerTP`);
        // Also remove nickname if any
        const nickParticipant = sb.getParticipants().find(pt => pt.displayName.startsWith(`${baseFakeName}_nickname`));
        if (nickParticipant) {
            player.runCommandAsync(`scoreboard players reset "${nickParticipant.displayName}" PlayerTP`);
        }

        // Optional: If your system increments a count of homes, e.g. scoreboard players remove <pName> PlayerTP 1
        player.runCommandAsync(`scoreboard players remove "${pName}" PlayerTP 1`);
        player.sendMessage(`§aRemoved home base #${baseNum} for ${pName}.`);
    } catch (err) {
        player.sendMessage("§cError removing home base.");
    }
    if (goBack) goBack();
}

/*************************************************
 * 5) Store Settings Submenu
 *************************************************/
export function storeSettingsMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const storeOptions = [
        { name: "StoreSettings_ServerShop", description: "Server Shop", texture: "textures/ui/store" },
        { name: "StoreSettings_PlayertoPlayer", description: "Player to Player Shop", texture: "textures/ui/exchange" },
        { name: "StoreSettings_SellShop", description: "Sell Shop", texture: "textures/ui/sell" }
    ];
    const scores = {};

    const obj = world.scoreboard.getObjective(rankScoreboardName);
    storeOptions.forEach(opt => {
        const part = obj.getParticipants().find(p => p.displayName === opt.name);
        scores[opt.name] = part ? obj.getScore(part) : 0;
    });

    const form = new ActionFormData()
        .title("Store Settings")
        .body("Select a store option:");
    const buttonActions = [];

    storeOptions.forEach(opt => {
        if (scores[opt.name] === 1) {
            form.button(opt.description, opt.texture);
            buttonActions.push(() => {
                // For now, just demonstrate refresh
                player.runCommandAsync(`say [Store Settings] ${opt.description} for rank ${rank.name}`);
                storeSettingsMenu(player, rank);
            });
        }
    });

    // Back => rank
    form.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => formRank(player));

    form.show(player).then(r => {
        if (r.canceled) return;
        if (buttonActions[r.selection]) buttonActions[r.selection]();
    });
}

/*************************************************
 * 6) Gamemode Settings Submenu
 *************************************************/
export function gamemodeMenu(player, rank) {
    const rankScoreboardName = `rank_${rank.name}`;
    const gmOptions = [
        { name: "gamemode_self", description: "Self Gamemode", texture: "textures/ui/person" },
        { name: "gamemode_others", description: "Others Gamemode", texture: "textures/ui/group" }
    ];
    const obj = world.scoreboard.getObjective(rankScoreboardName);
    const scores = {};
    gmOptions.forEach(opt => {
        const part = obj.getParticipants().find(p => p.displayName === opt.name);
        scores[opt.name] = part ? obj.getScore(part) : 0;
    });

    const f = new ActionFormData()
        .title("Gamemode Settings")
        .body("Select a gamemode option:");
    const buttonActions = [];

    gmOptions.forEach(opt => {
        if (scores[opt.name] === 1) {
            f.button(opt.description, opt.texture);
            if (opt.name === "gamemode_self") {
                buttonActions.push(() => selfGamemodeMenu(player, rank));
            } else {
                buttonActions.push(() => othersGamemodeMenu(player, rank));
            }
        }
    });

    // Back => rank
    f.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => formRank(player));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (buttonActions[resp.selection]) buttonActions[resp.selection]();
    });
}

// Self Gamemode
function selfGamemodeMenu(player, rank) {
    const modes = [
        { mode: "survival", display: "Survival" },
        { mode: "creative", display: "Creative" },
        { mode: "adventure", display: "Adventure" },
        { mode: "spectator", display: "Spectator" }
    ];
    const f = new ActionFormData()
        .title("Self Gamemode")
        .body("Select your new gamemode:");
    const actions = [];

    modes.forEach(gm => {
        f.button(gm.display);
        actions.push(() => {
            player.runCommandAsync(`gamemode ${gm.mode} "${player.name}"`).then(() => {
                player.sendMessage(`§aYour gamemode is now ${gm.display}.`);
                selfGamemodeMenu(player, rank);
            }).catch(() => {
                player.sendMessage("§cFailed to set gamemode.");
            });
        });
    });

    // Back => gamemode
    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => gamemodeMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (actions[resp.selection]) actions[resp.selection]();
    });
}

// Others Gamemode
function othersGamemodeMenu(player, rank) {
    const others = [...world.getPlayers()].filter(p => p.name !== player.name);
    const f = new ActionFormData()
        .title("Others Gamemode")
        .body("Select a player:");
    const actions = [];

    others.forEach(p => {
        f.button(p.name);
        actions.push(() => changeOthersGamemodeMenu(player, rank, p));
    });

    f.button("Back", "textures/ui/book_arrowleft_hover");
    actions.push(() => gamemodeMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (actions[resp.selection]) actions[resp.selection]();
    });
}

function changeOthersGamemodeMenu(player, rank, target) {
    const gmodes = [
        { mode: "survival", display: "Survival" },
        { mode: "creative", display: "Creative" },
        { mode: "adventure", display: "Adventure" },
        { mode: "spectator", display: "Spectator" }
    ];
    const f = new ActionFormData()
        .title(`Change ${target.name}'s Gamemode`)
        .body("Select new gamemode:");
    const buttonActions = [];

    gmodes.forEach(gm => {
        f.button(gm.display);
        buttonActions.push(() => {
            const c = new ActionFormData()
                .title("Confirm Gamemode")
                .body(`Change ${target.name} to ${gm.display}?`)
                .button("Yes", "textures/ui/check")
                .button("No", "textures/ui/crossout");

            c.show(player).then(resp => {
                if (resp.canceled || resp.selection === 1) {
                    othersGamemodeMenu(player, rank);
                    return;
                }
                player.runCommandAsync(`gamemode ${gm.mode} "${target.name}"`).then(() => {
                    player.sendMessage(`§aSet ${target.name}'s gamemode to ${gm.display}.`);
                    othersGamemodeMenu(player, rank);
                }).catch(() => {
                    player.sendMessage("§cFailed to change gamemode.");
                });
            });
        });
    });

    f.button("Back", "textures/ui/book_arrowleft_hover");
    buttonActions.push(() => othersGamemodeMenu(player, rank));

    f.show(player).then(resp => {
        if (resp.canceled) return;
        if (buttonActions[resp.selection]) buttonActions[resp.selection]();
    });
}

/*************************************************
 * 7) Custom Commands Submenu (Placeholder)
 *************************************************/
export function customCommandsMenu(player, rank) {
    const f = new ActionFormData()
        .title("Custom Commands")
        .body("Feature under construction.")
        .button("Back", "textures/ui/book_arrowleft_hover");

    f.show(player).then(resp => {
        if (resp.canceled) return;
        formRank(player);
    });
}
