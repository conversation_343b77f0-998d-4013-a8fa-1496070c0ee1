import { world, system, Player, ItemStack } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { transferMoney } from "./transfermoney"
import { mainMenu } from "./mainmenu"

let currentEconomyName = "ZCoins"; // Default economy name
let currentEconomyItemType = "zombie:zcoin"; // Default item type

export function bank(player) {

    const moneyDisplayObjective = world.scoreboard.getObjective("MoneyDisplay");
    if (moneyDisplayObjective) {
        currentEconomyName = moneyDisplayObjective.displayName || "Money"; // Fetch the display name dynamically
    }

    const bankForm = new ActionFormData()
        .title(` ${currentEconomyName} Bank`)
        .body(`Welcome to the bank!`)
        .button("Check Balance", "textures/ui/icon_book_writable")
        .button(`Deposit ${currentEconomyName}`, "textures/ui/icon_best3")
        .button(`Withdraw ${currentEconomyName}`, "textures/ui/haste_effect")
        .button(`Transfer  ${currentEconomyName}`, "textures/ui/dressing_room_customization")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover")
        .button("§l§cExit", "textures/ui/crossout");

    bankForm.show(player).then((response) => {
        if (response.canceled) {
            player.sendMessage("Bank menu closed.");
            return;
        }

        switch (response.selection) {
            case 0:
                checkBalance(player);
                bank(player);
                break;
            case 1:
                depositEconomy(player);
                break;
            case 2:
                withdrawEconomy(player);
                break;
            case 3:
                transferMoney(player);
                break;
            case 4:
                mainMenu(player);
                break;
            default:
                player.sendMessage("Invalid selection.");
        }
    });
}


function checkBalance(player) {
    const moneyObjective = world.scoreboard.getObjective("Money");
    if (!moneyObjective) {
        player.sendMessage("Money scoreboard not found.");
        return;
    }

    const balance = moneyObjective.getScore(player) || 0;
    player.sendMessage(`Your current balance is §4${currentEconomyName}: §a${balance}`);
}

function depositEconomy(player) {
    const inventory = player.getComponent("minecraft:inventory")?.container;
    if (!inventory) {
        player.sendMessage("Unable to access your inventory.");
        return;
    }

    let economyItemCount = 0;

    for (let slot = 0; slot < inventory.size; slot++) {
        const item = inventory.getItem(slot);
        if (item && item.typeId === currentEconomyItemType) {
            economyItemCount += item.amount;
        }
    }

    if (economyItemCount <= 0) {
        player.sendMessage(`You have no §4${currentEconomyName} in your inventory to deposit.`);
        return;
    }

    const depositForm = new ModalFormData()
        .title(`Deposit ${currentEconomyName}`)
        .textField(`You have ${economyItemCount} §4${currentEconomyName}. Enter amount to deposit:`, "Amount");

    depositForm.show(player).then((response) => {
        if (response.canceled) {
            bank(player);
            return;
        }

        const amountToDeposit = parseInt(response.formValues[0], 10);

        if (isNaN(amountToDeposit) || amountToDeposit <= 0 || amountToDeposit > economyItemCount) {
            player.sendMessage("Invalid deposit amount.");
            bank(player);
            return;
        }

        const moneyObjective = world.scoreboard.getObjective("Money");
        if (!moneyObjective) {
            player.sendMessage("Money scoreboard not found.");
            return;
        }

        const currentBalance = moneyObjective.getScore(player) || 0;
        moneyObjective.setScore(player, currentBalance + amountToDeposit);

        let remaining = amountToDeposit;
        for (let slot = 0; slot < inventory.size && remaining > 0; slot++) {
            const item = inventory.getItem(slot);
            if (item && item.typeId === currentEconomyItemType) {
                if (item.amount > remaining) {
                    item.amount -= remaining;
                    inventory.setItem(slot, item);
                    remaining = 0;
                } else {
                    remaining -= item.amount;
                    inventory.setItem(slot, null);
                }
            }
        }

        player.sendMessage(`You successfully deposited §a${amountToDeposit} §4${currentEconomyName}!`);
        bank(player);
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err.message}`);
        bank(player);
    });
}

function withdrawEconomy(player) {
    const moneyObjective = world.scoreboard.getObjective("Money");
    if (!moneyObjective) {
        player.sendMessage("Money scoreboard not found.");
        return;
    }

    const balance = moneyObjective.getScore(player) || 0;

    if (balance <= 0) {
        player.sendMessage(`You have no §4${currentEconomyName} to withdraw.`);
        return;
    }

    const withdrawForm = new ModalFormData()
        .title(`Withdraw ${currentEconomyName}`)
        .textField(`Your balance is ${balance}. Enter amount to withdraw:`, "Amount");

    withdrawForm.show(player).then((response) => {
        if (response.canceled) return;

        const amountToWithdraw = parseInt(response.formValues[0], 10);

        if (isNaN(amountToWithdraw) || amountToWithdraw <= 0 || amountToWithdraw > balance) {
            player.sendMessage("Invalid withdrawal amount.");
            bank(player);
            return;
        }

        moneyObjective.setScore(player, balance - amountToWithdraw);

        const inventory = player.getComponent("minecraft:inventory")?.container;
        if (!inventory) {
            player.sendMessage("Unable to access your inventory.");
            return;
        }

        let remaining = amountToWithdraw;
        for (let slot = 0; slot < inventory.size && remaining > 0; slot++) {
            const item = inventory.getItem(slot);
            if (!item) {
                const maxStack = 64;
                if (remaining <= maxStack) {
                    const newItem = new ItemStack(currentEconomyItemType, remaining);
                    inventory.setItem(slot, newItem);
                    remaining = 0;
                } else {
                    const newItem = new ItemStack(currentEconomyItemType, maxStack);
                    inventory.setItem(slot, newItem);
                    remaining -= maxStack;
                }
            } else if (item.typeId === currentEconomyItemType && item.amount < item.maxAmount) {
                const space = item.maxAmount - item.amount;
                if (remaining <= space) {
                    item.amount += remaining;
                    inventory.setItem(slot, item);
                    remaining = 0;
                } else {
                    item.amount = item.maxAmount;
                    inventory.setItem(slot, item);
                    remaining -= space;
                }
            }
        }

        if (remaining > 0) {
            player.sendMessage(`Your inventory is full. Unable to add all §4${currentEconomyName}.`);
        } else {
            player.sendMessage(`You successfully withdrew §a${amountToWithdraw} §4${currentEconomyName}!`);
        }

        bank(player);
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err.message}`);
        bank(player);
    });
}

export function economySetup(player) {
    const economyForm = new ActionFormData()
        .title("Economy Setup")
        .body("Do you want to start an economy?")
        .button("Yes", "textures/ui/confirm")
        .button("No", "textures/ui/cancel");

    economyForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            world.getDimension("overworld").runCommandAsync("scoreboard players set initialized admin 0");
            return;
        }

        showNameEconomyForm(player);
    });
}

function showNameEconomyForm(player) {
    const nameEconomyForm = new ModalFormData()
        .title("Name Your Economy")
        .textField("Economy Name", "Examples: Coins, Gems, Gold");

    nameEconomyForm.show(player).then((nameResponse) => {
        if (nameResponse.canceled || !nameResponse.formValues || !nameResponse.formValues[0]) {
            return;
        }

        const economyName = nameResponse.formValues[0].trim();
        const formattedName = economyName.replace(/[^a-zA-Z0-9_]/g, "_");

        currentEconomyName = formattedName;
        currentEconomyItemType = `zombie:${formattedName.toLowerCase()}`;

        world.getDimension("overworld").runCommandAsync(`scoreboard objectives add MoneyDisplay dummy "${economyName}"`);

        displayEconomyOptions(player, economyName, formattedName);
    });
}

function displayEconomyOptions(player, economyName, formattedName) {
    const displayForm = new ActionFormData()
        .title("Display Options")
        .body(`How do you want your economy "${economyName}" to be displayed?`)
        .button("Sidebar", "textures/ui/book_icon")
        .button("List", "textures/ui/options")
        .button("Don't Show", "textures/ui/cancel");

    displayForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        if (response.canceled) {
            overworld.runCommandAsync("scoreboard players set initialized admin 0");
            return;
        }

        switch (response.selection) {
            case 0:
                overworld.runCommandAsync(`scoreboard objectives setdisplay sidebar MoneyDisplay`);
                break;
            case 1:
                displayListOptions(player, economyName, formattedName);
                return;
            case 2:
                break;
        }

        overworld.runCommandAsync("scoreboard players set initialized admin 0");
    });
}

function displayListOptions(player, economyName, formattedName) {
    const listForm = new ActionFormData()
        .title("List Display Options")
        .body(`How do you want "${economyName}" to be sorted in the list?`)
        .button("Ascending", "textures/ui/up_arrow")
        .button("Descending", "textures/ui/down_arrow");

    listForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        if (response.canceled) {
            overworld.runCommandAsync("scoreboard players set initialized admin 0");
            return;
        }

        overworld.runCommandAsync(`scoreboard objectives setdisplay list MoneyDisplay`);

        if (response.selection === 0) {
            player.sendMessage(`"${economyName}" scores will appear in ascending order.`);
        } else if (response.selection === 1) {
            player.sendMessage(`"${economyName}" scores will appear in descending order.`);
        }

        overworld.runCommandAsync("scoreboard players set initialized admin 0");
    }).catch((err) => {
        player.sendMessage(`An error occurred: ${err.message}`);
    });
}
