import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { playerManagementMenu } from "./admin_menu";

export function banMenu(player) {
    const scoreboard = world.scoreboard.getObjective("BanList");
    if (!scoreboard) {
        player.sendMessage("§cNo Ban List found. Creating one...");
        player.runCommandAsync('scoreboard objectives add BanList dummy "Banned Players"');
        banMenu(player);
        return;
    }

    const banMenuForm = new ActionFormData()
        .title("§4Ban Menu")
        .body("Manage the ban list.")
        .button("Add Player to List", "textures/ui/anvil_icon")
        .button("Type Player In Manually", "textures/ui/haste_effect")
        .button("View Ban List", "textures/ui/icon_book_writable")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover")
        .button("§l§cExit", "textures/ui/crossout");

    banMenuForm.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0:
                addPlayerToList(player);
                break;
            case 1:
                typePlayerManually(player);
                break;
            case 2:
                viewBanList(player);
                break;
            case 3:
                playerManagementMenu(player);
                break;
            case 4:
                player.sendMessage("§0Exiting menu...");
                break;
        }
    });
}

function addPlayerToList(player) {
    const onlinePlayers = world.getPlayers();
    const bannedPlayers = getBanList();

    const eligiblePlayers = onlinePlayers.filter(p => !bannedPlayers.includes(p.name));

    if (eligiblePlayers.length === 0) {
        player.sendMessage("§cNo eligible players to ban.");
        banMenu(player);
        return;
    }

    const addPlayerForm = new ActionFormData()
        .title("§4Add Player to List")
        .body("Select a player to ban.");

    eligiblePlayers.forEach(p => addPlayerForm.button(p.name));

    addPlayerForm.show(player).then((response) => {
        if (response.canceled) {
            banMenu(player);
            return;
        }

        const selectedPlayer = eligiblePlayers[response.selection];
        confirmBanPlayer(player, selectedPlayer.name);
    });
}

function typePlayerManually(player) {
    const manualForm = new ModalFormData()
        .title("§4Type Player In Manually")
        .textField("Enter the player's name to ban:", "Player Name");

    manualForm.show(player).then((response) => {
        if (response.canceled || !response.formValues[0].trim()) {
            banMenu(player);
            return;
        }

        const playerName = response.formValues[0].trim();
        confirmBanPlayer(player, playerName);
    });
}

function confirmBanPlayer(player, playerName) {
    const confirmForm = new ActionFormData()
        .title("§4Confirm Ban")
        .body(`Are you sure you want to ban ${playerName}?`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            banMenu(player);
            return;
        }

        // Owner Check: Prevent banning the owner of the world
        const isOwner = isPlayerOwner(playerName);
        if (isOwner) {
            player.sendMessage(`§c${playerName} is the owner and cannot be banned.`);
            banMenu(player);
            return;
        }

        // Add the player to the Ban List as a fake player name
        const fakeName = `ban_${playerName}`;
        player.runCommandAsync(`scoreboard players add "${fakeName}" BanList 1`).then(() => {
            player.sendMessage(`§a${playerName} has been added to the Ban List.`);
            const target = world.getPlayers().find(p => p.name === playerName);
            if (target) target.runCommandAsync('kick You have been banned.');
        }).catch(err => {
            player.sendMessage(`§cFailed to ban ${playerName}: ${err}`);
        });
    });
}

function viewBanList(player) {
    const bannedPlayers = getBanList();

    if (bannedPlayers.length === 0) {
        player.sendMessage("§cThe Ban List is empty.");
        banMenu(player);
        return;
    }

    const banListForm = new ActionFormData()
        .title("§4Ban List")
        .body("Select a player to unban.");

    bannedPlayers.forEach(p => banListForm.button(p));
    banListForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    banListForm.show(player).then((response) => {
        if (response.canceled || response.selection === bannedPlayers.length) {
            banMenu(player);
            return;
        }

        const selectedBannedPlayer = bannedPlayers[response.selection];
        confirmUnbanPlayer(player, selectedBannedPlayer);
    });
}

function confirmUnbanPlayer(player, playerName) {
    const fakeName = `ban_${playerName}`;
    const confirmForm = new ActionFormData()
        .title("§4Confirm Unban")
        .body(`Are you sure you want to unban ${playerName}?`)
        .button("§aYes", "textures/ui/check")
        .button("§cNo", "textures/ui/crossout");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            viewBanList(player);
            return;
        }

        player.runCommandAsync(`scoreboard players reset "${fakeName}" BanList`).then(() => {
            player.sendMessage(`§a${playerName} has been removed from the Ban List.`);
            viewBanList(player);
        }).catch(err => {
            player.sendMessage(`§cFailed to unban ${playerName}: ${err}`);
        });
    });
}

function getBanList() {
    const scoreboard = world.scoreboard.getObjective("BanList");
    if (!scoreboard) return [];

    return scoreboard.getParticipants()
        .filter(participant => participant.displayName.startsWith("ban_"))
        .map(participant => participant.displayName.replace("ban_", "")); // Remove "ban_" prefix
}

function isPlayerOwner(playerName) {
    const scoreboard = world.scoreboard.getObjective("admin");
    if (!scoreboard) return false;

    // Check if the player has an "owner" designation in the admin scoreboard
    return scoreboard.getParticipants().some(participant => participant.displayName === `admin_${playerName}_owner`);
}
