import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { editFriendsMenu } from "./freindslist_tab";
import { mainMenu } from "./mainmenu";

/**
 * Returns a string listing the current dynamic rules.
 */
function getDynamicRules(player) {
    const adminObjective = world.scoreboard.getObjective("admin");
    if (!adminObjective) {
        return "Base security settings not set. Contact an admin.";
    }

    // Retrieve settings; if not defined, use fallback "Not Set"
    const allowedBases = adminObjective.getScore("basesecuritycount");
    const cost = adminObjective.getScore("basesucurtycost");
    const minDistance0 = adminObjective.getScore("baseXY");
    const minSpawn = adminObjective.getScore("basespawndistance");
    const minOther = adminObjective.getScore("basePlayerDistance");
    const secRadius = adminObjective.getScore("basesecurityrange");

    const aBases = (allowedBases === undefined || allowedBases === null) ? "Not Set" : allowedBases;
    const aCost = (cost === undefined || cost === null) ? "Not Set" : cost;
    const aMin0 = (minDistance0 === undefined || minDistance0 === null) ? "Not Set" : minDistance0;
    const aMinSpawn = (minSpawn === undefined || minSpawn === null) ? "Not Set" : minSpawn;
    const aMinOther = (minOther === undefined || minOther === null) ? "Not Set" : minOther;
    const aSecRadius = (secRadius === undefined || secRadius === null) ? "Not Set" : secRadius;

    return (
        `§7Rules:\n` +
        `- §fAllowed Bases: §e${aBases}\n` +
        `- §fCost per Base: §e${aCost} Money\n` +
        `- §fSecurity Radius: §e${aSecRadius} blocks\n` +
        `- §fMinimum distance from (0,0): §e${aMin0} blocks\n` +
        `- §fMinimum distance from Spawn: §e${aMinSpawn} blocks\n` +
        `- §fMinimum distance from another player's base: §e${aMinOther} blocks`
    );
}

/**
 * Main Base Management menu.
 */
export function baseManagement(player) {
    const dynamicRules = getDynamicRules(player);
    const form = new ActionFormData()
        .title("§aBase Management")
        .body("§6Base Security Settings:\n\n" +
            dynamicRules + "\n\n" +
            "Select one of your security bases to view/edit, or add a new one.")
        .button("Base Management", "textures/ui/icon_summer")
        .button("Edit Basemates", "textures/ui/FriendsIcon")
        .button("Back", "textures/ui/book_arrowleft_hover");

    form.show(player).then(response => {
        if (response.canceled) return;
        switch (response.selection) {
            case 0:
                baseSecurityManagement(player);
                break;
            case 1:
                editFriendsMenu(player);
                break;
            case 2:
                mainMenu(player);
                break;
            default:
                break;
        }
    }).catch(err => {
        player.sendMessage("§cAn error occurred in Base Management.");
        console.error(err);
    });
}

/**
 * Detailed Base Security Management Menu.
 * Shows fixed buttons ("Add Security Base" and "Back"), then one for each of the player's base entries.
 */
export function baseSecurityManagement(player) {
    let baseSecObjective = world.scoreboard.getObjective("basesecurity");
    if (!baseSecObjective) {
        baseSecObjective = world.scoreboard.addObjective("basesecurity", "Base Security");
    }
    const playerKey = player.name.toLowerCase().replace(/\s+/g, "");

    // Filter out special entries (like overall count or "zcoins")
    const baseEntries = baseSecObjective.getParticipants().filter(p =>
        p.displayName.startsWith(playerKey + "_") &&
        p.displayName !== `${playerKey}_count` &&
        p.displayName.toLowerCase() !== "zcoins"
    );

    const adminObjective = world.scoreboard.getObjective("admin");
    const maxAllowed = adminObjective ? (adminObjective.getScore("basesecuritycount") ?? 1) : 1;

    const form = new ActionFormData()
        .title("Base Security Management")
        .body(`You currently have ${baseEntries.length} security base system(s).`);

    // Fixed top buttons
    form.button("Add Security Base", "textures/ui/plus");
    form.button("Back", "textures/ui/book_arrowleft_hover");

    baseEntries.forEach(entry => {
        // Example scoreboard entry: "john_smith_2_300_64_-200"
        // Split on underscores:
        const parts = entry.displayName.split("_");
        if (parts.length < 4) {
            // Not enough parts => skip
            return;
        }
        // Rightmost parts are always x, y, z
        const z = parts[parts.length - 1];
        const y = parts[parts.length - 2];
        const x = parts[parts.length - 3];
        // The one before x,y,z is the baseNumber
        const baseNumber = parts[parts.length - 4];

        // Score is 1 => On, 0 => Off
        const toggle = baseSecObjective.getScore(entry);
        const status = toggle === 1 ? "On" : "Off";

        // Show label like "Off 2: (300, 64, -200)" (no player name shown)
        const label = `${status} ${baseNumber}: (${x}, ${y}, ${z})`;
        form.button(label, "textures/ui/absorption_effect");
    });

    form.show(player).then(response => {
        if (response.canceled) return;
        // Button order: 0 = "Add Security Base", 1 = "Back", then base entries
        if (response.selection === 0) {
            if (baseEntries.length < maxAllowed) {
                addSecurityBase(player);
            } else {
                player.sendMessage("You have reached the maximum number of security systems.");
                baseSecurityManagement(player);
            }
        } else if (response.selection === 1) {
            baseManagement(player);
        } else {
            const selectedBase = baseEntries[response.selection - 2];
            editSecurityBase(player, selectedBase.displayName);
        }
    }).catch(err => {
        player.sendMessage("§cAn error occurred in Detailed Base Security Management.");
        console.error(err);
    });
}




/**
 * Edit Security Base Menu.
 * Allows toggling the system on/off and setting new coordinates.
 */
export function editSecurityBase(player, baseKey) {
    const baseSecObjective = world.scoreboard.getObjective("basesecurity");
    if (!baseSecObjective) {
        player.sendMessage("Base Security scoreboard not found.");
        return;
    }
    const baseParticipant = baseSecObjective.getParticipants().find(p => p.displayName === baseKey);
    if (!baseParticipant) {
        player.sendMessage("Security base not found.");
        return;
    }

    // Get current toggle value (0 = off, 1 = on).
    const currentToggle = baseSecObjective.getScore(baseParticipant);
    const toggleLabel = currentToggle === 1 ? "Toggle Security (§aOn)" : "Toggle Security (§cOff)";
    const form = new ActionFormData()
        .title("Edit Security Base")
        .body(`Editing base: ${baseKey}\nCurrent status: ${toggleLabel}`)
        .button(toggleLabel)
        .button("Set Coordinates")
        .button("Back");

    form.show(player).then(response => {
        if (response.canceled) return;
        if (response.selection === 0) {
            // Toggle security state.
            const newToggle = currentToggle === 1 ? 0 : 1;
            try {
                baseSecObjective.setScore(baseKey, newToggle);
                player.sendMessage(`Security toggled to ${newToggle === 1 ? "On" : "Off"}.`);
            } catch (error) {
                player.sendMessage("Failed to toggle security.");
                console.error(error);
            }
            editSecurityBase(player, baseKey);
        } else if (response.selection === 1) {
            // Set Coordinates with validation.
            setSecurityBaseCoordinates(player, baseKey);
        } else if (response.selection === 2) {
            baseSecurityManagement(player);
        }
    }).catch(err => {
        player.sendMessage("An error occurred while editing the security base.");
        console.error(err);
    });
}

/**
 * Sets new coordinates for a security base.
 * First validates the player's current location against dynamic rules (spawn, origin, and other bases),
 * then shows a confirmation modal before updating the base's "fake" key.
 */
async function setSecurityBaseCoordinates(player, baseKey) {
    const baseSecObjective = world.scoreboard.getObjective("basesecurity");
    if (!baseSecObjective) {
        player.sendMessage("Base Security scoreboard not found.");
        return;
    }
    // Round the player's current coordinates
    const { x, y, z } = player.location;
    const newX = Math.round(x);
    const newY = Math.round(y);
    const newZ = Math.round(z);

    // Validate the new coordinates
    const valid = await validateCoordinates(player, newX, newZ);
    if (!valid) {
        // Return to edit menu if invalid
        return editSecurityBase(player, baseKey);
    }

    // Confirm form
    const confirmForm = new ActionFormData()
        .title("Confirm Coordinates")
        .body(`Set new coordinates to (X=${newX}, Y=${newY}, Z=${newZ})?`)
        .button("Confirm", "textures/ui/check")
        .button("Back", "textures/ui/crossout");

    confirmForm.show(player).then(response => {
        if (response.canceled || response.selection === 1) {
            // Canceled
            editSecurityBase(player, baseKey);
            return;
        }

        // We now reconstruct the scoreboard key by
        // 1) Splitting from the right,
        // 2) Keeping everything up to the base number,
        // 3) Replacing old x,y,z with newX,newY,newZ.
        const parts = baseKey.split("_");
        if (parts.length < 4) {
            player.sendMessage("Invalid base key format. Could not parse base number.");
            return;
        }

        // The last 3 are the old x, y, z
        // The one before that is the baseNumber
        // Everything before that is the player's name (could contain underscores)
        const newBaseKeyParts = parts.slice(0, parts.length - 3);
        // That array now ends with the baseNumber
        // Replace x, y, z
        newBaseKeyParts[newBaseKeyParts.length - 1] = parts[parts.length - 4]; // baseNumber
        newBaseKeyParts.push(newX, newY, newZ);
        // Actually simpler: we can skip reassigning the baseNumber, because it's already in newBaseKeyParts. 
        // Just remove old x,y,z from the end and push new ones:
        // See alternative approach below.

        // Or, more simply:
        //   const baseNumber = parts[parts.length - 4];
        //   const namePart   = parts.slice(0, parts.length - 4);  // everything before baseNumber
        //   const newBaseKeyParts = [ ...namePart, baseNumber, newX, newY, newZ ];
        // Either way is fine.

        // Let’s do the simpler approach:
        const baseNumber = parts[parts.length - 4];
        const namePart = parts.slice(0, parts.length - 4); // all the "player" segments
        const newKeyArray = [...namePart, baseNumber, newX, newY, newZ];
        const newBaseKey = newKeyArray.join("_");

        // Get old scoreboard score and carry it over
        const oldParticipant = baseSecObjective.getParticipants().find(p => p.displayName === baseKey);
        const toggle = oldParticipant ? baseSecObjective.getScore(oldParticipant) : 0;

        // Remove the old entry, add the new
        baseSecObjective.removeParticipant(baseKey);
        baseSecObjective.setScore(newBaseKey, toggle);

        player.sendMessage(`Security base coordinates updated to (X=${newX}, Y=${newY}, Z=${newZ}).`);
        // Refresh the edit menu with the updated key
        editSecurityBase(player, newBaseKey);
    }).catch(err => {
        player.sendMessage("An error occurred while confirming coordinates.");
        console.error(err);
        editSecurityBase(player, baseKey);
    });
}



/**
 * Retrieves the global spawn point from the "setspawn" scoreboard.
 * Expects a participant in the format: "global_spawn_dimension_x_y_z"
 */
function getSpawnPoint() {
    const scoreboard = world.scoreboard.getObjective("setspawn");
    if (!scoreboard) {
        console.log("No 'setspawn' scoreboard found.");
        return null;
    }
    let spawnEntry = null;
    for (const participant of scoreboard.getParticipants()) {
        if (participant.displayName.startsWith("global_spawn")) {
            spawnEntry = participant.displayName;
            break;
        }
    }
    if (!spawnEntry) {
        console.log("No valid global spawn point found.");
        return null;
    }
    const match = spawnEntry.match(/^global_spawn_(\w+)_(-?\d+)_(-?\d+)_(-?\d+)$/);
    if (!match) {
        console.log("Global spawn point format is invalid.");
        return null;
    }
    const [, dimension, x, y, z] = match;
    return { dimension, x: parseInt(x, 10), y: parseInt(y, 10), z: parseInt(z, 10) };
}

/**
 * Validates the new coordinates against dynamic rules:
 * - Checks that X and Z are not too close to the origin.
 * - Checks that the new coordinates are far enough from the global spawn.
 * - Checks that they are not too close to another player's base.
 */
async function validateCoordinates(player, newX, newZ) {
    const adminObjective = world.scoreboard.getObjective("admin");
    if (!adminObjective) {
        player.sendMessage("Admin settings not found. Contact an admin.");
        return false;
    }
    // Retrieve dynamic values or use defaults.
    const minSpawnDistance = adminObjective.getScore("basespawndistance") ?? 1000;
    const minDistance0 = adminObjective.getScore("baseXY") ?? 250;
    const minOtherDistance = adminObjective.getScore("basePlayerDistance") ?? 500;

    // Check distance from origin.
    if (Math.abs(newX) < minDistance0 || Math.abs(newZ) < minDistance0) {
        player.sendMessage(`§cYour coordinates are too close to the origin. Must be at least ${minDistance0} blocks away.`);
        return false;
    }

    // Check distance from global spawn.
    const spawnPoint = getSpawnPoint();
    if (!spawnPoint) {
        player.sendMessage("§cNo global spawn set. Please set spawn first.");
        return false;
    }
    const distanceFromSpawn = Math.sqrt((newX - spawnPoint.x) ** 2 + (newZ - spawnPoint.z) ** 2);
    if (distanceFromSpawn < minSpawnDistance) {
        player.sendMessage(`§cYour coordinates are too close to spawn. Must be at least ${minSpawnDistance} blocks away. (Current: ${Math.round(distanceFromSpawn)} blocks)`);
        return false;
    }

    // Check distance from other players' bases.
    const baseSecObjective = world.scoreboard.getObjective("basesecurity");
    if (baseSecObjective) {
        const playerKey = player.name.toLowerCase().replace(/\s+/g, "");
        for (const participant of baseSecObjective.getParticipants()) {
            // Skip bases belonging to the same player.
            if (participant.displayName.startsWith(playerKey + "_")) continue;
            const match = participant.displayName.match(/^(.+?)_(-?\d+)_(-?\d+)_(-?\d+)$/);
            if (!match) continue;
            const baseX = parseInt(match[2], 10);
            const baseZ = parseInt(match[4], 10);
            const distance = Math.sqrt((newX - baseX) ** 2 + (newZ - baseZ) ** 2);
            if (distance < minOtherDistance) {
                player.sendMessage(`§cYour coordinates are too close to another player's base at (${baseX}, ${baseZ}). Must be at least ${minOtherDistance} blocks away. (Current: ${Math.round(distance)} blocks)`);
                return false;
            }
        }
    }
    return true;
}

/**
 * Adds a new security base system.
 * Checks admin-defined cost and maximum allowed systems,
 * verifies the player's Money and current count,
 * deducts the cost, increments the count,
 * and creates a new base entry with coordinates initially set to 0.
 */
export function addSecurityBase(player) {
    const adminObjective = world.scoreboard.getObjective("admin");
    const moneyObjective = world.scoreboard.getObjective("Money");
    let baseSecObjective = world.scoreboard.getObjective("basesecurity");

    if (!adminObjective || !moneyObjective) {
        player.sendMessage("Required scoreboards not found. Contact an admin.");
        return;
    }
    if (!baseSecObjective) {
        baseSecObjective = world.scoreboard.addObjective("basesecurity", "Base Security");
    }

    const baseCost = adminObjective.getScore("basesucurtycost") ?? 0;
    const maxAllowed = adminObjective.getScore("basesecuritycount") ?? 1;
    const playerMoney = moneyObjective.getScore(player) || 0;
    if (playerMoney < baseCost) {
        player.sendMessage(`You do not have enough Money to add a security base. Cost: ${baseCost} Money.`);
        return baseSecurityManagement(player);
    }

    const playerKey = player.name.toLowerCase().replace(/\s+/g, "");
    const countKey = `${playerKey}_count`;
    let countParticipant = baseSecObjective.getParticipants().find(p => p.displayName === countKey);
    let currentCount = countParticipant ? baseSecObjective.getScore(countParticipant) : 0;
    if (currentCount >= maxAllowed) {
        player.sendMessage(`You have reached the maximum number of security systems (${maxAllowed}).`);
        return baseSecurityManagement(player);
    }

    moneyObjective.setScore(player, playerMoney - baseCost);
    const newCount = currentCount + 1;
    if (!countParticipant) {
        baseSecObjective.setScore(countKey, newCount);
    } else {
        baseSecObjective.setScore(countKey, newCount);
    }
    // Create a new base entry with coordinates initially set to 0.
    const newBaseKey = `${playerKey}_${newCount}_x_y_z`;
    baseSecObjective.setScore(newBaseKey, 0);
    player.sendMessage(`Security base system added successfully. You now have ${newCount} system(s).`);
    baseSecurityManagement(player);
}
