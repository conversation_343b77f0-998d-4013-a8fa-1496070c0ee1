import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { adminSettingsMenu } from "./admin_menu";

export function economy(player) {
    const economyForm = new ActionFormData()
        .title("Economy Management")
        .body("Select an option:")
        .button("Edit Economy", "textures/ui/darkness_effect")
        .button("Remove Economy", "textures/ui/cancel")
        .button("Set Start Amount", "textures/ui/dressing_room_capes")
        .button("Convert Old Econamy", "textures/ui/backup_replace")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    economyForm.show(player).then((response) => {
        if (response.canceled) {
            return;
        }

        switch (response.selection) {
            case 0:
                editEconomy(player);
                break;
            case 1:
                confirmRemoveEconomy(player);
                break;
            case 2:
                setStartAmount(player); // New case for the "Set Start Amount" button
                break;
            case 3:
                convertOldEconomy(player)
                break;
            case 4:
                adminSettingsMenu(player);
                break;
        }
    });
}

let storedEconomyName = null; // Stores the old economy scoreboard name

export function convertOldEconomy(player) {
    const adminObjective = world.scoreboard.getObjective("admin");
    let toggleLabel = "Convert Button: Off";
    if (adminObjective) {
        const participant = adminObjective.getParticipants().find(p => p.displayName === "economyTransfer");
        const currentScore = participant ? adminObjective.getScore(participant) : 0;
        toggleLabel = currentScore === 1 ? "Convert Button: §aOn" : "Convert Button: §cOff";
    }
    const oldEconomyForm = new ActionFormData()
        .title("§0Convert Old Economy Setup")
        .body("Choose an option:")
        .button("§eSet up Convert Old Economy", "textures/ui/dark")
        .button(toggleLabel, "textures/ui/dark")
        .button("§l§cBack", "textures/ui/icon_apple");

    oldEconomyForm.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0: // Setup
                setupOldEconomy(player);
                break;
            case 1: if (adminObjective) {
                const participant = adminObjective.getParticipants().find(p => p.displayName === "economyTransfer");
                const currentScore = participant ? adminObjective.getScore(participant) : 0;
                const newScore = currentScore === 1 ? 0 : 1;
                adminObjective.setScore("economyTransfer", newScore);
            }
                convertOldEconomy(player); // Refresh menu break;
                break;
            case 2: // Back
                economy(player); // Assuming this function exists
                break;
        }
    }).catch((error) => {
        console.error("Error in convertOldEconomy:", error);
        player.sendMessage("§cAn error occurred while opening the conversion setup menu.");
    });
}

function setupOldEconomy(player) {
    // Step 1: Check if an existing economyTransfer scoreboard exists
    const existingTransfer = getExistingEconomyTransfer();

    if (existingTransfer) {
        // Step 2: If it exists, ask the player if they want to remove it
        confirmRemoveExistingTransfer(player, existingTransfer);
    } else {
        // Step 3: If no existing economyTransfer scoreboard, go directly to input form
        askForOldEconomyName(player);
    }
}

// ✅ Step 1: Check if any scoreboard exists that starts with "economyTransfer_"
function getExistingEconomyTransfer() {
    const objectives = world.scoreboard.getObjectives();
    return objectives.find(obj => obj.id.startsWith("economyTransfer_")) || null;
}

// ✅ Step 2: Ask if they want to remove the existing transfer process
function confirmRemoveExistingTransfer(player, existingTransfer) {
    const confirmForm = new ActionFormData()
        .title("§cExisting Transfer Found")
        .body(
            `§eThere is already an economy transfer process:\n` +
            `§6${existingTransfer.id}\n\n` +
            `§cWould you like to remove this transfer process and start a new one?`
        )
        .button("§aYes, Remove")
        .button("§cBack");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            convertOldEconomy(player); // Go back to main menu
            return;
        }

        // Step 3: Remove the existing scoreboard
        removeEconomyScoreboard(player, existingTransfer.id, () => {
            askForOldEconomyName(player); // Proceed after successful removal
        });
    }).catch((error) => {
        console.error("Error in confirmRemoveExistingTransfer:", error);
        player.sendMessage("§cAn error occurred while confirming economy removal.");
    });
}

// ✅ Step 3: Remove the scoreboard properly in Bedrock
function removeEconomyScoreboard(player, scoreboardName, callback) {
    player.runCommandAsync(`scoreboard objectives remove "${scoreboardName}"`)
        .then(() => {
            player.sendMessage(`§cRemoved old economy transfer: §6${scoreboardName}`);
            callback(); // Move to the next step after removal
        })
        .catch((error) => {
            console.error("Error removing existing economy transfer:", error);
            player.sendMessage("§cFailed to remove the existing economy transfer.");
        });
}

// ✅ Step 4: Ask the player to enter their old economy scoreboard name
function askForOldEconomyName(player) {
    const setupForm = new ModalFormData()
        .title("§6Setup Old Economy")
        .textField("Enter your old economy scoreboard name exactly:", "§eExample: §6money");

    setupForm.show(player).then((response) => {
        if (response.canceled) {
            convertOldEconomy(player);
            return;
        }

        let oldEconomyName = response.formValues[0].trim();
        if (!oldEconomyName) {
            player.sendMessage("§cYou must enter a valid scoreboard name.");
            setupOldEconomy(player); // Restart process
            return;
        }

        // Convert § symbols to ¤ for safe storage
        let safeEconomyName = oldEconomyName.replace(/§/g, "¤");

        confirmOldEconomy(player, oldEconomyName, safeEconomyName);
    }).catch((error) => {
        console.error("Error in askForOldEconomyName:", error);
        player.sendMessage("§cAn error occurred while setting up the old economy.");
    });
}

// ✅ Step 5: Confirm the economy name before saving
function confirmOldEconomy(player, oldEconomyName, safeEconomyName) {
    const confirmForm = new ActionFormData()
        .title("§6Confirm Old Economy")
        .body(
            `§eYou entered:\n§6"${oldEconomyName}"\n\n` +
            `§cOnce submitted, you will not be able to change this.\n\n` +
            `Are you sure this is correct?`
        )
        .button("§aSubmit")
        .button("§cBack");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            askForOldEconomyName(player); // Go back to input form
            return;
        }

        // Store the old economy name for future use
        storedEconomyName = `"${safeEconomyName}"`; // **Preserves spaces**
        player.sendMessage(`§aOld Economy stored as: ${storedEconomyName}`);

        // Create the new scoreboard with the stored economy name
        const newScoreboard = `economyTransfer_${safeEconomyName}`;

        player.runCommandAsync(`scoreboard objectives add "${newScoreboard}" dummy "Converted Economy"`)
            .then(() => {
                player.sendMessage(`§aNew economy scoreboard created: §6${newScoreboard}`);
                convertOldEconomy(player); // Return to the main menu
            })
            .catch((error) => {
                console.error("Error creating new scoreboard:", error);
                player.sendMessage("§cFailed to create the new economy scoreboard.");
            });
    }).catch((error) => {
        console.error("Error in confirmOldEconomy:", error);
        player.sendMessage("§cAn error occurred while confirming old economy setup.");
    });
}



async function setStartAmount(player) {
    // Create a modal form for entering the starting amount
    const setStartAmountForm = new ModalFormData()
        .title("Set Starting Amount")
        .textField("Enter the starting amount:", "Amount", "0");

    const response = await setStartAmountForm.show(player);
    if (response.canceled || !response.formValues || !response.formValues[0]) {
        return; // Exit if canceled or invalid input
    }

    const startAmount = parseInt(response.formValues[0]);
    if (isNaN(startAmount) || startAmount < 0) {
        player.sendMessage("§cInvalid amount entered. Please enter a positive number or 0.");
        return;
    }

    const overworld = world.getDimension("overworld");

    try {
        // Remove the old scoreboard, ignore errors if it does not exist
        await overworld.runCommandAsync("scoreboard objectives remove economyStart");

        // Re-add the scoreboard
        await overworld.runCommandAsync('scoreboard objectives add economyStart dummy "Starting Amount"');

        // Set the new starting amount for the fake player 'Economystart' on the 'admin' scoreboard
        await overworld.runCommandAsync(`scoreboard players set Economystart admin ${startAmount}`);

        // Notify the player of success
        player.sendMessage(`§aStarting amount successfully set to ${startAmount}.`);

        // Return to the economy menu
        economy(player);
    } catch (error) {
        // Notify the player of failure and log the error
        player.sendMessage("§cFailed to update the starting amount.");
        console.error(error);
    }
}



function editEconomy(player) {
    const currentObjective = world.scoreboard.getObjective("MoneyDisplay");

    if (!currentObjective) {
        const addEconomyForm = new ActionFormData()
            .title("No Economy Found")
            .body("It seems no economy exists. Would you like to create one?")
            .button("Yes, Create Economy", "textures/ui/dressing_room_capes")
            .button("§l§cBack", "textures/ui/book_arrowleft_hover");

        addEconomyForm.show(player).then((response) => {
            if (response.canceled || response.selection === 1) {
                return; // Exit completely if canceled or Back is clicked
            }

            if (response.selection === 0) {
                showNameEconomyForm(player);
            }
        });

        return;
    }

    const editForm = new ActionFormData()
        .title("Edit Economy")
        .body("What would you like to do?")
        .button("Change Economy Name", "textures/ui/dressing_room_capes")
        .button("Change Display Settings", "textures/ui/sidebar_icons/my_content")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    editForm.show(player).then((response) => {
        if (response.canceled || response.selection === 2) {
            return; // Exit completely if canceled or Back is clicked
        }

        switch (response.selection) {
            case 0:
                changeEconomyName(player);
                break;
            case 1:
                displayEconomyOptions(player, currentObjective.displayName || "Economy", "MoneyDisplay");
                break;
        }
    });
}

function resetFakePlayers() {
    const participants = world.scoreboard.getParticipants();
    participants.forEach((participant) => {
        if (participant.displayName.startsWith("Money_")) {
            world.getDimension("overworld").runCommandAsync(`scoreboard players reset "${participant.displayName}" admin`);
        }
    });
}

function showNameEconomyForm(player) {
    const nameEconomyForm = new ModalFormData()
        .title("Create Economy")
        .textField("Economy Name", "Examples: Coins, Gems, Gold");

    nameEconomyForm.show(player).then((nameResponse) => {
        if (nameResponse.canceled || !nameResponse.formValues || !nameResponse.formValues[0]) {
            return; // Exit completely if canceled
        }

        const economyName = nameResponse.formValues[0].trim();
        const formattedName = economyName.replace(/§/g, "¤").replace(/[^a-zA-Z0-9_¤]/g, "_");

        world.getDimension("overworld").runCommandAsync(`scoreboard objectives add MoneyDisplay dummy "${economyName}"`)
            .then(() => {
                world.getDimension("overworld").runCommandAsync(`scoreboard players set Money_${formattedName} admin 0`);
                displayEconomyOptions(player, economyName, formattedName);
            })
            .catch((error) => {
                player.sendMessage("§cFailed to create the economy.");
                console.error(error);
            });
    });
}

function changeEconomyName(player) {
    const overworld = world.getDimension("overworld");

    const nameEconomyForm = new ModalFormData()
        .title("Change Economy Name")
        .textField("New Economy Name", "Examples: Coins, Gems, Gold");

    nameEconomyForm.show(player).then((nameResponse) => {
        if (nameResponse.canceled || !nameResponse.formValues || !nameResponse.formValues[0]) {
            return; // Exit completely if canceled
        }

        const newName = nameResponse.formValues[0].trim();
        const formattedNewName = newName.replace(/§/g, "¤").replace(/[^a-zA-Z0-9_¤]/g, "_");

        overworld.runCommandAsync(`scoreboard objectives remove MoneyDisplay`)
            .then(() => {
                resetFakePlayers();
                return overworld.runCommandAsync(`scoreboard objectives add MoneyDisplay dummy "${newName}"`);
            })
            .then(() => {
                overworld.runCommandAsync(`scoreboard players set Money_${formattedNewName} admin 0`);
                displayEconomyOptions(player, newName, formattedNewName);
            })
            .catch((error) => {
                player.sendMessage("§cFailed to rename the economy.");
                console.error(error);
            });
    });
}

function confirmRemoveEconomy(player) {
    const confirmForm = new ActionFormData()
        .title("Confirm Removal")
        .body("Are you sure you want to remove the economy?\nThis will reset all player scores in 'Money' but will not remove coins in the world.")
        .button("Yes, Reset Economy", "textures/ui/check")
        .button("§l§cNo, Cancel", "textures/ui/cancel");

    confirmForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            return; // Exit completely if canceled or No is clicked
        }

        if (response.selection === 0) {
            resetEconomy(player);
        }
    });
}

function resetEconomy(player) {
    const overworld = world.getDimension("overworld");

    overworld.runCommandAsync(`scoreboard objectives remove Money`)
        .then(() => overworld.runCommandAsync(`scoreboard objectives remove MoneyDisplay`))
        .then(() => overworld.runCommandAsync(`scoreboard objectives add Money dummy`))
        .then(() => {
            player.sendMessage("§aEconomy has been successfully reset.");
        })
        .catch((error) => {
            player.sendMessage("§cFailed to reset the economy.");
            console.error(error);
        });
}

function displayEconomyOptions(player, economyName, formattedName) {
    const displayForm = new ActionFormData()
        .title("Display Options")
        .body(`How do you want your economy "${economyName}" to be displayed?`)
        .button("Sidebar", "textures/ui/sidebar_icons/csb_sidebar_icon")      // index 0
        .button("List", "textures/ui/sidebar_icons/my_characters")           // index 1
        .button("Both (Sidebar & List)", "textures/ui/joinIcon")             // index 2 (NEW)
        .button("Don't Show", "textures/ui/cancel")                          // index 3
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");            // index 4

    displayForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        // Exit if the player canceled or pressed the "Back" button.
        if (response.canceled || response.selection === 4) {
            return;
        }

        // First, clear any existing scoreboard displays:
        overworld.runCommandAsync("scoreboard objectives setdisplay sidebar");
        overworld.runCommandAsync("scoreboard objectives setdisplay list");

        switch (response.selection) {
            case 0: // Sidebar
                overworld.runCommandAsync("scoreboard objectives setdisplay sidebar MoneyDisplay");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 1`);
                break;

            case 1: // List
                // Use a helper function so we can ask about ascending/descending sorting.
                displayListOptions(player, economyName, formattedName);
                return; // Important to return so the code below doesn't run

            case 2: // Both (Sidebar & List)
                // Display on both the sidebar and list
                overworld.runCommandAsync("scoreboard objectives setdisplay sidebar MoneyDisplay");
                overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 4`);
                break;

            case 3: // Don't Show
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 0`);
                break;
        }

        // Re-initialize scoreboard or any other end-of-selection logic
        overworld.runCommandAsync("scoreboard players set initialized admin 0");
    });
}

function displayListOptions(player, economyName, formattedName) {
    const listForm = new ActionFormData()
        .title("List Display Options")
        .body(`How do you want "${economyName}" to be sorted in the list?`)
        .button("Ascending", "textures/ui/up_arrow")                 // index 0
        .button("Descending", "textures/ui/down_arrow")             // index 1
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");    // index 2

    listForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        // Exit if the player canceled or pressed "Back".
        if (response.canceled || response.selection === 2) {
            return;
        }

        // Clear existing scoreboard displays
        overworld.runCommandAsync("scoreboard objectives setdisplay sidebar");
        overworld.runCommandAsync("scoreboard objectives setdisplay list");

        switch (response.selection) {
            case 0: // Ascending
                overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay ascending");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 2`);
                break;

            case 1: // Descending
                overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay descending");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 3`);
                break;
        }

        // Re-initialize scoreboard if necessary
        overworld.runCommandAsync("scoreboard players set initialized admin 0");
    });
}
