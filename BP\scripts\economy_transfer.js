import { world } from "@minecraft/server";

export async function economyTransfer(player) {
    const playerName = player.name;
    const overworld = world.getDimension("overworld");

    try {
        // Get all scoreboard objectives
        const objectives = world.scoreboard.getObjectives();

        // Find the first economyTransfer scoreboard
        const economyObjective = objectives.find(obj => obj.id.startsWith("economyTransfer_"));

        if (!economyObjective) {
            player.sendMessage("§cNo economy transfer data found.");
            return;
        }

        // Extract the real scoreboard name (convert `¤` to `§`)
        const rawScoreboardName = economyObjective.id.replace("economyTransfer_", "");
        const actualScoreboardName = rawScoreboardName.replace(/¤/g, "§"); // Example: "§4ddd"

        player.sendMessage(`§7Found economy transfer scoreboard: §b"${economyObjective.id}"`);
        player.sendMessage(`§7Converted to actual scoreboard: §b"${actualScoreboardName}"`);

        // Step 1: Get the actual scoreboard (converted name)
        const scoreboardObjective = world.scoreboard.getObjective(actualScoreboardName);
        if (!scoreboardObjective) {
            player.sendMessage(`§cError: Scoreboard "${actualScoreboardName}" does not exist.`);
            return;
        }

        // Step 2: Retrieve the player's score in the actual scoreboard (`§4ddd`)
        let playerScore = scoreboardObjective.getScore(player);
        if (playerScore === undefined || playerScore === null) {
            player.sendMessage(`§cYou have no score in "${actualScoreboardName}".`);
            return;
        }

        if (playerScore <= 0) {
            player.sendMessage("§cYou have no money to convert.");
            return;
        }

        player.sendMessage(`§7Detected score: §6${playerScore}¤`);

        // Step 3: Check economyTransfer scoreboard to prevent double conversion
        const economyTransferObjective = world.scoreboard.getObjective("economyTransfer");
        if (economyTransferObjective) {
            let transferStatus = economyTransferObjective.getScore(player);

            if (transferStatus > 0) {
                player.sendMessage("§cYou have already converted your money. This process cannot be repeated.");
                return;
            } else if (transferStatus === 0 || transferStatus === null || transferStatus === undefined) {
                player.sendMessage("§cContact an admin. You already converted your money.");
                return;
            }
        }

        player.sendMessage(`§7Transferring §6${playerScore}¤§7 to Money...`);

        // Step 4: Transfer score to Money scoreboard
        const moneyObjective = world.scoreboard.getObjective("Money");
        if (!moneyObjective) {
            player.sendMessage("§cError: Money scoreboard does not exist.");
            return;
        }

        moneyObjective.setScore(player, (moneyObjective.getScore(player) || 0) + playerScore);

        // Step 5: Reset the original scoreboard and mark transfer complete
        scoreboardObjective.setScore(player, 0);
        economyTransferObjective?.setScore(player, 1); // Mark as converted

        player.sendMessage(`§aSuccessfully converted §6${playerScore}¤ §ato your Money.`);
    } catch (error) {
        console.error("Error in economyTransfer:", error);
        player.sendMessage(`§cUnexpected error: ${error}`);
    }
}
