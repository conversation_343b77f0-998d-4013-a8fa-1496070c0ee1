import { world } from '@minecraft/server';
import { ActionFormData, ModalFormData } from '@minecraft/server-ui';
import { baseManagement } from "./basemanagment";

// Main Edit Friends Menu
export function editFriendsMenu(player) {
    const scoreboard = world.scoreboard.getObjective("friendslist") ||
        world.scoreboard.addObjective("friendslist", "Friends List");

    const playerFriends = getFriendsList(player, scoreboard);

    const editFriendsForm = new ActionFormData()
        .title("Edit BaseMates")
        .body("Manage your friends list.");

    // Add top-level buttons
    editFriendsForm.button("Add Friends");            // Button index 0
    editFriendsForm.button("Add Friends Manually");     // Button index 1

    // Add current friends as buttons (if any)
    if (playerFriends.length > 0) {
        playerFriends.forEach((friend) => {
            editFriendsForm.button(friend);
        });
    }

    // Add the Back button at the end.
    editFriendsForm.button("Back");

    editFriendsForm.show(player).then((response) => {
        if (response.canceled) return;

        // Button ordering:
        // 0: "Add Friends"
        // 1: "Add Friends Manually"
        // 2 to (2 + playerFriends.length - 1): friend list entries
        // Last: "Back" at index 2 + playerFriends.length
        if (response.selection === 0) {
            addFriendsMenu(player); // Open Add Friends menu (from available players)
        } else if (response.selection === 1) {
            addFriendManually(player); // Open manual friend addition menu
        } else if (response.selection === 2 + playerFriends.length) {
            baseManagement(player); // "Back" button selected
        } else {
            // A friend entry was selected.
            const friendIndex = response.selection - 2;
            const selectedFriend = playerFriends[friendIndex];
            removeFriendMenu(player, selectedFriend, scoreboard); // Open Remove Friend menu
        }
    }).catch((error) => {
        player.sendMessage("§cAn error occurred while opening the Edit Friends menu.");
    });
}

// Add Friends Menu (unchanged except that it now works with our new storage format)
function addFriendsMenu(player) {
    const scoreboard = world.scoreboard.getObjective("friendslist") ||
        world.scoreboard.addObjective("friendslist", "Friends List");

    const onlinePlayers = world.getPlayers().filter((p) => p.name !== player.name);
    const playerFriends = getFriendsList(player, scoreboard);

    const availablePlayers = onlinePlayers.filter((p) => !playerFriends.includes(p.name));
    if (availablePlayers.length === 0) {
        player.sendMessage("§cNo players available to add as friends.");
        return;
    }

    const addFriendsForm = new ActionFormData()
        .title("Add Friends")
        .body("Select a player to add as a friend, or choose 'Add Manually':");

    // Button 0: "Add Manually"
    addFriendsForm.button("Add Manually");

    // Then list available players
    availablePlayers.forEach((p) => addFriendsForm.button(p.name));

    // Final button: "Back"
    addFriendsForm.button("Back");

    addFriendsForm.show(player).then((response) => {
        if (response.canceled) return;

        // If the player selects "Add Manually"
        if (response.selection === 0) {
            addFriendManually(player);
        }
        // If the player selects "Back" (last button)
        else if (response.selection === availablePlayers.length + 1) {
            editFriendsMenu(player);
        }
        // Otherwise, they selected one of the available players.
        else {
            const selectedPlayer = availablePlayers[response.selection - 1];
            if (!selectedPlayer) {
                player.sendMessage("§cFailed to select a player.");
                return;
            }
            confirmAddFriendMenu(player, selectedPlayer.name, scoreboard);
        }
    }).catch((error) => {
        player.sendMessage("§cAn error occurred while opening the Add Friends menu.");
    });
}

// Add Friend Manually Menu
function addFriendManually(player) {
    const form = new ModalFormData()
        .title("Add Friend Manually")
        .textField("Enter the friend's name:", "Friend Name", "");
    // ModalFormData automatically provides Confirm/Cancel.

    form.show(player).then((response) => {
        if (response.canceled) {
            player.sendMessage("§cManual friend addition canceled.");
            return;
        }
        // response.formValues is an array containing the values for each field.
        const friendName = response.formValues[0].trim();
        if (!friendName) {
            player.sendMessage("§cNo name entered.");
            return;
        }
        const scoreboard = world.scoreboard.getObjective("friendslist") ||
            world.scoreboard.addObjective("friendslist", "Friends List");
        const currentFriends = getFriendsList(player, scoreboard);
        if (currentFriends.includes(friendName)) {
            player.sendMessage("§cThat friend is already in your friends list.");
            return;
        }
        const updatedFriends = [...currentFriends, friendName];
        updateFriendsList(player, updatedFriends, scoreboard);
        player.sendMessage(`§a${friendName} has been added to your friends list.`);
    }).catch((error) => {
        console.error("Error in addFriendManually:", error);
        player.sendMessage("§cAn error occurred while adding a friend manually.");
    });
}

// Confirm Add Friend Menu
function confirmAddFriendMenu(player, friendName, scoreboard) {
    const confirmationForm = new ActionFormData()
        .title(`Add ${friendName} as Friend?`)
        .body(`Do you want to add ${friendName} to your friends list?`)
        .button("Yes")
        .button("Back");

    confirmationForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            player.sendMessage("§cAdding friend canceled.");
            return;
        }
        if (response.selection === 0) {
            const currentFriends = getFriendsList(player, scoreboard);
            const updatedFriends = [...currentFriends, friendName];
            updateFriendsList(player, updatedFriends, scoreboard);
            player.sendMessage(`§a${friendName} has been added to your friends list.`);
        }
    }).catch((error) => {
        console.error("Error in confirmAddFriendMenu:", error);
        player.sendMessage("§cAn error occurred while confirming the friend addition.");
    });
}

// Remove Friend Menu
function removeFriendMenu(player, friendName, scoreboard) {
    const confirmationForm = new ActionFormData()
        .title("Remove Friend")
        .body(`Do you want to remove ${friendName} from your friends list?`)
        .button("Yes")
        .button("Back");

    confirmationForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            player.sendMessage("§cRemoving friend canceled.");
            return;
        }
        if (response.selection === 0) {
            const currentFriends = getFriendsList(player, scoreboard);
            const updatedFriends = currentFriends.filter((f) => f !== friendName);
            updateFriendsList(player, updatedFriends, scoreboard);
            player.sendMessage(`§a${friendName} has been removed from your friends list.`);
        }
    }).catch((error) => {
        console.error("Error in removeFriendMenu:", error);
        player.sendMessage("§cAn error occurred while removing the friend.");
    });
}

// Helper Functions

// Updated helper functions to store friend data in the fake player's name

// Get the list of friends for the player
function getFriendsList(player, scoreboard) {
    const playerName = player.name;
    // Look for a fake player entry that starts with "playerName|"
    const entry = scoreboard.getParticipants().find(
        (participant) => participant.displayName.startsWith(`${playerName}|`)
    );
    if (entry) {
        // Expected format: "playerName|friend1¦friend2¦friend3"
        const storedData = entry.displayName; // We store our data in the displayName itself.
        const parts = storedData.split("|");
        if (parts.length < 2) return [];
        return parts[1] ? parts[1].split("¦") : [];
    }
    return [];
}

// Update the player's friends list in the scoreboard
function updateFriendsList(player, friends, scoreboard) {
    const playerName = player.name;
    // Remove any existing entry for this player (search by prefix)
    const existing = scoreboard.getParticipants().find(
        (participant) => participant.displayName.startsWith(`${playerName}|`)
    );
    if (existing) {
        scoreboard.removeParticipant(existing.displayName);
    }
    // Join the friend names with the delimiter "¦"
    const friendsString = friends.join("¦");
    // Create the fake player's name string in the format "playerName|friend1¦friend2¦friend3"
    const savedData = `${playerName}|${friendsString}`;
    // Store the data as a fake player's score (score can be 0)
    scoreboard.setScore(savedData, 0);
}
