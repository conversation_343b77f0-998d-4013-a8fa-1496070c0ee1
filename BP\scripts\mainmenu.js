import { world, ItemStack } from "@minecraft/server";
import { ActionFormData } from "@minecraft/server-ui";
import { adminMenu } from "./admin_menu";
import { playerShop } from "./admin_contols/player_stores";
import { baseManagement } from "./basemanagment";
import { serverSellShop } from "./server_sellshop";
import { bank } from "./bank";
import { playerTpmenu } from "./playertp";
import { formRank } from "./admin_ranks/admin_ranks_menu";
import { serverStoresMenu } from "./server_store_menu";
import { economyTransfer } from "./economy_transfer"; // Importing the function for conversion

export function mainMenu(player) {
    const mainMenuForm = new ActionFormData()
        .title("§4World§2Menu")
        .body("Welcome to the world menu!\nChoose an option below:");

    const adminObjective = world.scoreboard.getObjective("admin");
    if (!adminObjective) {
        player.sendMessage("§cNo 'admin' scoreboard found.");
        return;
    }

    let rank;
    const isOwner = adminObjective.getParticipants().some((p) => p.displayName === `admin_${player.name}_owner`);

    const adminParticipant = adminObjective
        .getParticipants()
        .find((p) => p.displayName === `admin_${player.name}`);

    if (adminParticipant) {
        rank = adminObjective.getScore(adminParticipant);
    }

    let buttonActions = [];

    try {
        const playerTpVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "TpController" && adminObjective.getScore(p) === 1);

        const serverStoresVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "menu_serverStores" && adminObjective.getScore(p) === 1);

        const playerStoresVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "playerstoresbutton" && adminObjective.getScore(p) === 1);

        const baseSecurityVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "basesecuritybutton" && adminObjective.getScore(p) === 1);

        const sellShopVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "sellshop" && adminObjective.getScore(p) === 1);

        const bankControllerVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "BankController" && adminObjective.getScore(p) === 1);

        const economyTransferVisible = adminObjective
            .getParticipants()
            .some((p) => p.displayName === "economyTransfer" && adminObjective.getScore(p) === 1);

        if (playerTpVisible) {
            mainMenuForm.button("Player Tp", "textures/ui/NetherPortalMirror");
            buttonActions.push(() => {
                playerTpmenu(player);
            });
        }

        if (serverStoresVisible) {
            mainMenuForm.button("Server Store", "textures/ui/backup_replace");
            buttonActions.push(() => {
                serverStoresMenu(player);
            });
        }

        if (playerStoresVisible) {
            mainMenuForm.button("PlayerShops", "textures/ui/village_hero_effect");
            buttonActions.push(() => {
                playerShop(player);
            });
        }

        if (baseSecurityVisible) {
            mainMenuForm.button("Base Security", "textures/ui/emptyStarFocus");
            buttonActions.push(() => {
                baseManagement(player);
            });
        }

        if (sellShopVisible) {
            mainMenuForm.button("Sell Shop", "textures/ui/fire_resistance_effect");
            buttonActions.push(() => {
                serverSellShop(player);
            });
        }

        if (bankControllerVisible) {
            mainMenuForm.button("Bank", "textures/ui/icon_minecoin_9x9");
            buttonActions.push(() => {
                bank(player);
            });
        }

        // Economy Transfer Button
        if (economyTransferVisible) {
            mainMenuForm.button("Convert Money", "textures/ui/realmsIcon");
            buttonActions.push(() => {
                economyTransfer(player);
            });
        }

        if (isOwner || typeof rank === "number") {
            mainMenuForm.button("Admin", "textures/ui/world_glyph_desaturated");
            buttonActions.push(() => {
                if (isOwner || rank === 0) {
                    adminMenu(player);
                } else {
                    formRank(player);
                }
            });
        }

        const allowedNames = [
            "zombieclinic",
            "R0seDrag0n91",
            "SatanDragon4233",
            "ETphonehome3876",
            "LemonRobin78030",
            "Crims0nBl00d",
            "C0D0GAMER",
            "SuddenPuppet908",
            "zaybzoril"
        ];
        if (allowedNames.includes(player.name)) {
            mainMenuForm.button("Beta Testers", "textures/ui/creative_icon");
            buttonActions.push(() => {
                betaTestersMenu(player);
            });
        }

        if (buttonActions.length === 0 && rank === undefined && !isOwner) {
            mainMenuForm.button("Exit", "textures/ui/crossout");
            buttonActions.push(() => {
                player.sendMessage("§cMenu needs to be set up. Please contact an admin.");
            });
        }
    } catch (err) {
        player.sendMessage("§cError checking menu visibility: " + err);
    }

    mainMenuForm
        .show(player)
        .then((response) => {
            if (response.canceled) return;

            const action = buttonActions[response.selection];
            if (action) {
                action();
            } else {
                player.sendMessage("§cInvalid selection.");
            }
        })
        .catch((err) => {
            player.sendMessage(`§cAn error occurred: ${err}`);
        });
}



function betaTestersMenu(player) {
    const betaForm = new ActionFormData()
        .title("§6Thank You Beta Testers!")
        .body(
            "We appreciate your efforts in testing the addon, finding bugs, and helping improve it.\n\n" +
            "As a token of our gratitude, please accept this gift!\n\n" +
            "You can claim this reward up to 2 times. After that, you'll need to use the admin menu."
        )
        .button("Accept", "textures/ui/gift_square") // Gift button
        .button("Back", "textures/ui/book_arrowleft_hover"); // Back button

    betaForm.show(player).then((response) => {
        if (response.canceled) return; // Exit the form

        if (response.selection === 0) {
            // Accept button
            try {
                const betaUsersObjective = ensureScoreboardObjective("betausers", "Beta Testers");
                const score = getPlayerScore(betaUsersObjective, player.name);

                if (score >= 2) {
                    player.sendMessage("§cYou have already claimed this reward twice. Please use the admin menu for further assistance.");
                    return;
                }

                const itemStack = new ItemStack("zombie:enderzombie_cape", 1); // Create the ItemStack for the hidden item
                const inventory = player.getComponent("minecraft:inventory").container; // Access player's inventory

                if (inventory) {
                    const slotIndex = findEmptySlot(inventory);

                    if (slotIndex === -1) {
                        player.sendMessage("§cYour inventory is full! Please clear some space and try again.");
                    } else {
                        inventory.setItem(slotIndex, itemStack); // Place the item in the empty slot
                        player.sendMessage("§aThank you for your help! You have been granted the Ender Zombie Cape.");
                        setPlayerScore(betaUsersObjective, player.name, score + 1); // Increment the player's score
                        player.sendMessage(`§aYou have claimed this gift ${score + 1} time(s).`);
                    }
                } else {
                    player.sendMessage("§cUnable to access your inventory. Please try again.");
                }
            } catch (error) {
                player.sendMessage(`§cFailed to grant the item: ${error.message}`);
            }
        } else if (response.selection === 1) {
            // Back button
            mainMenu(player);
        }
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred while showing the Beta Testers menu: ${err}`);
    });
}

/**
 * Finds the first empty slot in the player's inventory.
 * @param {object} inventory - The player's inventory container.
 * @returns {number} The index of the first empty slot, or -1 if full.
 */
function findEmptySlot(inventory) {
    for (let i = 0; i < inventory.size; i++) {
        if (!inventory.getItem(i)) {
            return i; // Return the first empty slot
        }
    }
    return -1; // Inventory is full
}

/**
 * Ensures that the specified scoreboard objective exists. If not, it creates one.
 * @param {string} objectiveId - The ID of the scoreboard objective.
 * @param {string} displayName - The display name of the objective.
 * @returns {object} The scoreboard objective.
 */
function ensureScoreboardObjective(objectiveId, displayName) {
    let objective = world.scoreboard.getObjective(objectiveId);
    if (!objective) {
        world.scoreboard.addObjective(objectiveId, displayName);
        objective = world.scoreboard.getObjective(objectiveId);
    }
    return objective;
}

/**
 * Gets the score of a player from a scoreboard objective.
 * @param {object} objective - The scoreboard objective.
 * @param {string} playerName - The name of the player.
 * @returns {number} The player's score, or 0 if the player is not found.
 */
function getPlayerScore(objective, playerName) {
    const participant = objective.getParticipants().find((p) => p.displayName === playerName);
    return participant ? objective.getScore(participant) : 0;
}

/**
 * Sets the score of a player in a scoreboard objective.
 * @param {object} objective - The scoreboard objective.
 * @param {string} playerName - The name of the player.
 * @param {number} score - The score to set.
 */
function setPlayerScore(objective, playerName, score) {
    world.scoreboard.getObjective(objective.id).setScore(playerName, score);
}

function encodeString(input) {
    return input.replace(/&/g, "¦").replace(/§/g, "¤").replace(/ /g, "_");
}