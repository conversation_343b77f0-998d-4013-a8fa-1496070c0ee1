import { world, system } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { mainMenu } from "./mainmenu";

export class MainMenu {
    async onUse(event) {
        const { source: player } = event;

        const adminScoreboard = world.scoreboard.getObjective("admin");

        if (!adminScoreboard) {
            await startUp(player);
            return;
        }

        const initializedExists = adminScoreboard.getParticipants().some(p => p.displayName === "initialized");

        if (!initializedExists) {
            await startUp(player);
        } else {
            mainMenu(player);
        }
    }
}

async function startUp(player) {
    const startUpForm = new ActionFormData()
        .title("§4Zombie§2Craft§r §1World Menu")
        .body("§7This menu simplifies setting up your world:\n\n§f- §6Configure server stores and player shops\n§f- §6Set up an economy and base security\n§f- §6Manage teleportation and home teleports\n§f- §6Access admin controls and world management\n\n§cBy clicking 'Start,' you agree to become an administrator of this world. This will automatically grant you admin permissions.")
        .button("§0Start", "textures/ui/check")
        .button("§0Cancel", "textures/ui/cancel");

    const response = await startUpForm.show(player);
    if (response.canceled || response.selection === 1) return;

    const overworld = world.getDimension("overworld");

    await overworld.runCommandAsync("scoreboard objectives add Money dummy §4Z§2Coins");
    await overworld.runCommandAsync("scoreboard objectives add storeowner dummy");
    await overworld.runCommandAsync("scoreboard players add @a storeowner 0");
    await overworld.runCommandAsync("scoreboard objectives add basesecurity dummy");
    await overworld.runCommandAsync("scoreboard objectives add admin dummy");
    await overworld.runCommandAsync("scoreboard objectives add BanList dummy");

    // --- Here’s the key change: we create two entries ---
    // 1) A 'real-name' entry with score=1 (general admin rank)
    // 2) An '_owner' entry with score=0 for your owner checks
    await overworld.runCommandAsync(`scoreboard players set "${player.name}" admin 1`);
    await overworld.runCommandAsync(`scoreboard players set "admin_${player.name}_owner" admin 0`);

    economySetup(player);
}


function economySetup(player) {
    const economyForm = new ActionFormData()
        .title("Economy Setup")
        .body("Do you want to start an economy?")
        .button("Yes", "textures/ui/confirm")
        .button("No", "textures/ui/cancel");

    economyForm.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            world.getDimension("overworld").runCommandAsync("scoreboard players set initialized admin 0");
            mainMenu(player);
            return;
        }

        showNameEconomyForm(player);
    });
}

function showNameEconomyForm(player) {
    const nameEconomyForm = new ModalFormData()
        .title("Name Your Economy")
        .textField("Economy Name", "Examples: Coins, Gems, Gold");

    nameEconomyForm.show(player).then((nameResponse) => {
        if (nameResponse.canceled || !nameResponse.formValues || !nameResponse.formValues[0]) {
            mainMenu(player);
            return;
        }

        const economyName = nameResponse.formValues[0].trim();
        const formattedName = economyName.replace(/§/g, "¤").replace(/[^a-zA-Z0-9_¤]/g, "_");

        world.getDimension("overworld").runCommandAsync(`scoreboard objectives add MoneyDisplay dummy "${economyName}"`);
        world.getDimension("overworld").runCommandAsync(`scoreboard players set Money_${formattedName} admin 0`);

        displayEconomyOptions(player, economyName, formattedName);
    });
}

function displayEconomyOptions(player, economyName, formattedName) {
    const displayForm = new ActionFormData()
        .title("Display Options")
        .body(`How do you want your economy "${economyName}" to be displayed?`)
        .button("Sidebar", "textures/ui/absorption_heart")                 // index 0
        .button("List", "textures/ui/Add-Ons_Side-Nav_Icon_24x24")        // index 1
        .button("Both (Sidebar & List)", "textures/ui/conduit_power_effect")          // index 2 (NEW)
        .button("Don't Show", "textures/ui/cancel");                      // index 3

    displayForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        if (response.canceled) {
            overworld.runCommandAsync("scoreboard players set initialized admin 0");
            mainMenu(player);
            return;
        }

        // Clear any existing scoreboard display settings
        overworld.runCommandAsync("scoreboard objectives setdisplay sidebar");
        overworld.runCommandAsync("scoreboard objectives setdisplay list");

        switch (response.selection) {
            case 0: // Sidebar only
                overworld.runCommandAsync("scoreboard objectives setdisplay sidebar MoneyDisplay");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 1`);
                break;

            case 1: // List only
                displayListOptions(player, economyName, formattedName);
                return; // Return here to allow sorting selection.

            case 2: // Both Sidebar & List
                overworld.runCommandAsync("scoreboard objectives setdisplay sidebar MoneyDisplay");
                overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 4`);
                break;

            case 3: // Don't Show
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 0`);
                break;
        }

        overworld.runCommandAsync("scoreboard players set initialized admin 0");
        mainMenu(player);
    });
}

function displayListOptions(player, economyName, formattedName) {
    const listForm = new ActionFormData()
        .title("List Display Options")
        .body(`How do you want "${economyName}" to be sorted in the list?`)
        .button("Ascending", "textures/ui/up_arrow")                 // index 0
        .button("Descending", "textures/ui/down_arrow")             // index 1
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");    // index 2

    listForm.show(player).then((response) => {
        const overworld = world.getDimension("overworld");

        if (response.canceled || response.selection === 2) {
            overworld.runCommandAsync("scoreboard players set initialized admin 0");
            mainMenu(player);
            return;
        }

        // Clear existing scoreboard display
        overworld.runCommandAsync("scoreboard objectives setdisplay sidebar");
        overworld.runCommandAsync("scoreboard objectives setdisplay list");

        switch (response.selection) {
            case 0: // Ascending
                overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay ascending");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 2`);
                break;

            case 1: // Descending
                overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay descending");
                overworld.runCommandAsync(`scoreboard players set Money_${formattedName} admin 3`);
                break;
        }

        overworld.runCommandAsync("scoreboard players set initialized admin 0");
        mainMenu(player);
    });
}
