import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { adminMenu } from "../admin_menu"



export function mobStacker(player) {
    const mobStackerForm = new ActionFormData()
        .title("Mob Stacker Menu")
        .body("Choose how to add mobs to the stack:")
        .button("Add Mobs", "textures/items/spawn_egg.png")
        .button("Type Mobs Manually", "textures/ui/writing.png")
        .button("Back", "textures/ui/return.png");

    mobStackerForm.show(player).then(response => {
        if (response.isCanceled) return;

        switch (response.selection) {
            case 0:
                openAddMobForm(player);
                break;
            case 1:
                openManualMobE<PERSON>ry(player);
                break;
            case 2:
                adminMenu(player);
                break;
        }
    }).catch(error => {
        console.error("Error displaying Mob Stacker menu:", error);
    });
}





// Full list of Minecraft Bedrock mobs (no variants)
const allMobs = [
    "Allay", "Axolo<PERSON>", "<PERSON>", "Bee", "<PERSON>", "Camel", "Cave Spider", "Chicken", "Cod", "Cow", "C<PERSON><PERSON>", "Dolphin",
    "<PERSON>key", "Drowned", "Elder Guardian", "<PERSON>erman", "<PERSON>ermite", "Evoker", "<PERSON>", "Frog", "Ghast", "Glow Squid",
    "Goat", "Guardian", "Hoglin", "Horse", "<PERSON>sk", "Illusioner", "<PERSON> Golem", "Llama", "Magma <PERSON>ube", "<PERSON>oshroom",
    "<PERSON>le", "Ocelot", "<PERSON>da", "Parrot", "Phantom", "Pig", "Piglin", "Piglin Brute", "Pillager", "Polar Bear",
    "Pufferfish", "Rabbit", "Ravager", "Salmon", "Sheep", "Shulker", "Silverfish", "Skeleton", "Skeleton Horse",
    "Slime", "Snow Golem", "Spider", "Squid", "Stray", "Strider", "Turtle", "Vex", "Villager", "Vindicator",
    "Wandering Trader", "Witch", "Wither", "Wither Skeleton", "Wolf", "Zoglin", "Zombie", "Zombie Horse",
    "Zombie Villager", "Zombified Piglin"
];

export function openAddMobForm(player) {
    ensureScoreboardExists(); // Ensure scoreboard exists before running

    const form = new ModalFormData()
        .title("Toggle Mob Stacking");

    const mobStackerObjective = world.scoreboard.getObjective("mobstacker");
    let toggleStates = [];

    allMobs.forEach(mobName => {
        let isOn = false; // Default to Off
        if (mobStackerObjective) {
            const participant = mobStackerObjective.getParticipants().find(p => p.displayName === mobName);
            const currentScore = participant ? mobStackerObjective.getScore(participant) : 0;
            isOn = currentScore === 1;
        }
        toggleStates.push(isOn);
        form.toggle(`${mobName}`, isOn);
    });

    form.show(player).then(response => {
        if (response.canceled) return; // Correctly exits when closed

        const updatedToggles = response.formValues;
        updateMobStackerScores(updatedToggles);
    });
}

function updateMobStackerScores(updatedToggles) {
    const mobStackerObjective = world.scoreboard.getObjective("mobstacker");
    if (!mobStackerObjective) return;

    updatedToggles.forEach((isOn, index) => {
        const mobName = allMobs[index];
        const newScore = isOn ? 1 : 0;
        mobStackerObjective.setScore(mobName, newScore);
    });
}

function ensureScoreboardExists() {
    if (!world.scoreboard.getObjective("mobstacker")) {
        world.getDimension("overworld").runCommandAsync(`scoreboard objectives add mobstacker dummy "Mob Stacker"`);
    }
}
