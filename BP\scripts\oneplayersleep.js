import { ModalFormData } from "@minecraft/server-ui";
import { world } from "@minecraft/server";

export function onePlayerSleep(player) {
    const form = new ModalFormData()
        .title("One Player Sleep Configuration")
        .slider("Set percentage of players needed for sleep\nThis fixes the realm glitch where it resets itself", 0, 100, 1, 50); // Slider from 0 to 100, default at 50%

    form.show(player).then((response) => {
        if (response.canceled) return;

        const percentage = response.formValues[0]; // Get the slider value directly

        const fakePlayerName = "onePlayerSleep";
        const scoreboard = "admin";

        try {
            // Update the scoreboard with the percentage
            world.getDimension("overworld").runCommand(
                `scoreboard players set ${fakePlayerName} ${scoreboard} ${percentage}`
            );

            // Update the playersSleepingPercentage gamerule
            world.getDimension("overworld").runCommand(
                `gamerule playersSleepingPercentage ${percentage}`
            );

            player.sendMessage(`§aOne-player sleep percentage set to ${percentage}%.`);
            player.sendMessage(`§aGamerule playersSleepingPercentage updated to ${percentage}%.`);
        } catch (error) {
            player.sendMessage("§cFailed to update the one-player sleep setting or gamerule.");
            console.error(error);
        }
    });
}
