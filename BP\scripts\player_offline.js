import { world } from "@minecraft/server";

// Function to reset the MoneyDisplay objective
// Function to reset the MoneyDisplay objective
async function resetMoneyDisplayObjective() {
    const overworld = world.getDimension("overworld");
    const adminScoreboard = world.scoreboard.getObjective("admin");

    if (!adminScoreboard) {
        console.warn("Admin scoreboard not found.");
        return;
    }

    try {
        const participant = adminScoreboard.getParticipants().find(p => p.displayName.startsWith("Money_"));
        if (!participant) {
            return;
        }

        const economyNameMatch = participant.displayName.match(/^Money_(.+)$/);
        if (!economyNameMatch || !economyNameMatch[1]) {
            return;
        }

        const rawEconomyName = economyNameMatch[1];
        const displayEconomyName = rawEconomyName
            .replace(/¤/g, "§")
            .replace(/_/g, " ");

        await overworld.runCommandAsync("scoreboard objectives remove MoneyDisplay");
        await overworld.runCommandAsync(`scoreboard objectives add MoneyDisplay dummy "${displayEconomyName}"`);

    } catch (error) {
        console.error("Error resetting MoneyDisplay objective:", error);
    }
}

// Main function for player offline logic
export async function immediatePlayerOfflineCommandLeave() {
    try {
        const overworld = world.getDimension("overworld");
        const adminScoreboard = world.scoreboard.getObjective("admin");

        if (!adminScoreboard) {
            console.warn("Admin scoreboard not found.");
            return;
        }

        const participant = adminScoreboard.getParticipants().find(p => p.displayName.startsWith("Money_"));
        if (!participant) {
            return;
        }

        const economyNameMatch = participant.displayName.match(/^Money_(.+)$/);
        if (!economyNameMatch || !economyNameMatch[1]) {
            return;
        }

        const rawEconomyName = economyNameMatch[1];

        // 1) Reset objective
        await resetMoneyDisplayObjective();

        // 2) Check the displayScore (stored in admin scoreboard).
        const displayScore = adminScoreboard.getScore(participant);

        switch (displayScore) {
            case 1:
                // Sidebar
                await overworld.runCommandAsync("scoreboard objectives setdisplay sidebar MoneyDisplay");
                break;
            case 2:
                // List ascending
                await overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay ascending");
                break;
            case 3:
                // List descending
                await overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay descending");
                break;
            case 4:
                // Both Sidebar & List
                await overworld.runCommandAsync("scoreboard objectives setdisplay sidebar MoneyDisplay");
                await overworld.runCommandAsync("scoreboard objectives setdisplay list MoneyDisplay");
                break;
            default:
                // Don't show or no recognized display score
                break;
        }

        // 3) Handle onePlayerSleep logic
        const onePlayerSleepParticipant = adminScoreboard
            .getParticipants()
            .find(p => p.displayName === "onePlayerSleep");
        if (onePlayerSleepParticipant) {
            const sleepPercentage = adminScoreboard.getScore(onePlayerSleepParticipant);
            if (sleepPercentage >= 0 && sleepPercentage <= 100) {
                await overworld.runCommandAsync(`gamerule playerssleepingpercentage ${sleepPercentage}`);
            }
        }

        // 4) Ensure economyStart scoreboard exists
        const economyStartScoreboard = world.scoreboard.getObjective("economyStart");
        if (!economyStartScoreboard) {
            await overworld.runCommandAsync("scoreboard objectives add economyStart dummy");
        }

        // 5) Give any uninitialized players their starting economy
        for (const player of world.getPlayers()) {
            const playerScore = economyStartScoreboard.getScore(player);
            if (playerScore === undefined || playerScore === null) {
                const startAmountParticipant = adminScoreboard
                    .getParticipants()
                    .find(p => p.displayName === "Economystart");
                if (startAmountParticipant) {
                    const startAmount = adminScoreboard.getScore(startAmountParticipant);
                    if (startAmount > 0) {
                        await overworld.runCommandAsync(`scoreboard players set "${player.name}" economyStart ${startAmount}`);
                        await overworld.runCommandAsync(`scoreboard players add "${player.name}" Money ${startAmount}`);
                        player.sendMessage(`§aYou have received your starting amount of ${startAmount}.`);
                    }
                }
            }
        }
    } catch (error) {
        console.error("Error in immediatePlayerOfflineCommandLeave:", error);
    }
}
