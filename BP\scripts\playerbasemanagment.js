import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { playerManagementMenu } from "./admin_menu"

export function playerBaseMenu(player) {
    const mainMenu = new ActionFormData()
        .title("§0Base Navigation Menu")
        .body("§bChoose an option to navigate bases:")
        .button("§0By Security Systems")
        .button("§0By Home Bases")
        .button("§cBack");

    mainMenu.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0: // By Security Systems
                playerBaseList(player);
                break;
            case 1: // By Home Bases
                homeBaseList(player);
                break;
            case 2: // Back
                playerManagementMenu(player);
                break;
        }
    }).catch((error) => {
        console.error("Error in playerBaseMenu:", error);
        player.sendMessage("§cAn error occurred while opening the Base Navigation Menu.");
    });
}

function playerBaseList(player) {
    const scoreboard = world.scoreboard.getObjective("basesecurity");

    if (!scoreboard) {
        player.sendMessage("§cNo bases found in the system.");
        return;
    }

    const baseOwners = new Set();

    // Extract unique player names from scoreboard participants
    scoreboard.getParticipants().forEach(participant => {
        const match = participant.displayName.match(/^(.+?)_\d+_-?\d+_-?\d+_-?\d+$/);
        if (match) {
            baseOwners.add(match[1]); // Store only player names
        }
    });

    if (baseOwners.size === 0) {
        player.sendMessage("§cNo valid bases found in the system.");
        return;
    }

    const playerListForm = new ActionFormData()
        .title("§0Base Owners")
        .body("Select a player to view their bases:")
        .button("§cBack");

    [...baseOwners].sort().forEach(playerName => {
        playerListForm.button(`§0${playerName}`);
    });

    playerListForm.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            playerBaseMenu(player); // Return to main menu
            return;
        }

        const selectedPlayer = [...baseOwners][response.selection - 1];
        showPlayerBases(player, selectedPlayer);
    }).catch((error) => {
        console.error("Error in playerBaseList:", error);
        player.sendMessage("§cAn error occurred while opening the Base Owners list.");
    });
}

function showPlayerBases(player, playerName) {
    const scoreboard = world.scoreboard.getObjective("basesecurity");

    if (!scoreboard) {
        player.sendMessage("§cNo bases found for this player.");
        return;
    }

    const playerBases = [];

    // Find all bases belonging to the selected player
    scoreboard.getParticipants().forEach(participant => {
        const match = participant.displayName.match(new RegExp(`^${playerName}_(\\d+)_(-?\\d+)_(-?\\d+)_(-?\\d+)$`));
        if (match) {
            const [_, baseNumber, x, y, z] = match;
            playerBases.push({ baseNumber: parseInt(baseNumber, 10), x: parseInt(x, 10), y: parseInt(y, 10), z: parseInt(z, 10) });
        }
    });

    if (playerBases.length === 0) {
        player.sendMessage(`§cNo bases found for ${playerName}.`);
        return;
    }

    // Sort bases numerically by base number
    playerBases.sort((a, b) => a.baseNumber - b.baseNumber);

    const baseListForm = new ActionFormData()
        .title(`§0${playerName}'s Bases`)
        .body("Select a base to manage:")
        .button("§cBack");

    playerBases.forEach(base => {
        baseListForm.button(`Base ${base.baseNumber}:\nX=${base.x}, Y=${base.y}, Z=${base.z}`);
    });

    baseListForm.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            playerBaseList(player); // Go back to player list
            return;
        }

        const selectedBase = playerBases[response.selection - 1];
        baseOptions(player, playerName, selectedBase);
    }).catch((error) => {
        console.error("Error in showPlayerBases:", error);
        player.sendMessage("§cAn error occurred while opening the player's bases list.");
    });
}

function baseOptions(player, playerName, base) {
    const form = new ActionFormData()
        .title(`Base ${base.baseNumber} Options`)
        .body(`Coordinates: X=${base.x}, Y=${base.y}, Z=${base.z}\nWhat do you want to do?`)
        .button("§0Teleport")
        .button("§cRemove Base")
        .button("§4Back");

    form.show(player).then((response) => {
        if (response.canceled) {
            showPlayerBases(player, playerName);
            return;
        }

        switch (response.selection) {
            case 0: // Teleport
                confirmTeleportSucurty(player, base, () => showPlayerBases(player, playerName));
                break;
            case 1: // Remove Base
                confirmRemoveBasesucurty(player, playerName, base, () => showPlayerBases(player, playerName));
                break;
            case 2: // Back
                showPlayerBases(player, playerName);
                break;
        }
    }).catch((error) => {
        console.error("Error in baseOptions:", error);
        player.sendMessage("§cAn error occurred while showing base options.");
    });
}

function confirmTeleportSucurty(player, base, goBack) {
    const form = new ActionFormData()
        .title("Teleport Confirmation")
        .body(`Are you sure you want to teleport to:\nX=${base.x}, Y=${base.y}, Z=${base.z}?`)
        .button("§aYes, Teleport")
        .button("§cNo, Cancel");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            if (goBack) goBack();
            return;
        }

        player.runCommandAsync(`tp @s ${base.x} ${base.y} ${base.z}`)
            .then(() => {
                player.sendMessage(`§aTeleported to X=${base.x}, Y=${base.y}, Z=${base.z}.`);
            })
            .catch((error) => {
                console.error("Teleport failed:", error);
                player.sendMessage("§cFailed to teleport.");
            });

    }).catch((error) => {
        console.error("Error in confirmTeleport:", error);
        player.sendMessage("§cAn error occurred while processing teleportation.");
    });
}

function confirmRemoveBasesucurty(player, playerName, base, goBack) {
    const form = new ActionFormData()
        .title("Confirm Removal")
        .body(`§4Are you sure you want to remove Base ${base.baseNumber} for player ${playerName}?\nCoordinates: X=${base.x}, Y=${base.y}, Z=${base.z}`)
        .button("§aYes, Remove")
        .button("§cNo, Cancel");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            if (goBack) goBack();
            return;
        }

        removeBaseS(player, playerName, base, goBack);
    }).catch((error) => {
        console.error("Error in confirmRemoveBase:", error);
        player.sendMessage("§cAn error occurred while confirming base removal.");
    });
}

function removeBaseS(player, playerName, base, goBack) {
    const scoreboard = world.scoreboard.getObjective("basesecurity");

    if (!scoreboard) {
        player.sendMessage("§cNo scoreboard found for bases.");
        return;
    }

    const basePrefix = `${playerName}_`;
    const basePattern = new RegExp(`^${playerName}_(\\d+)_(-?\\d+)_(-?\\d+)_(-?\\d+)$`);

    // Get all bases for this player
    const playerBases = scoreboard.getParticipants()
        .map(participant => {
            const match = participant.displayName.match(basePattern);
            if (match) {
                return {
                    displayName: participant.displayName,
                    baseNumber: parseInt(match[1], 10),
                    x: parseInt(match[2], 10),
                    y: parseInt(match[3], 10),
                    z: parseInt(match[4], 10)
                };
            }
            return null;
        })
        .filter(Boolean)
        .sort((a, b) => a.baseNumber - b.baseNumber); // Ensure bases are in order

    // Remove the selected base
    const baseToRemove = `${playerName}_${base.baseNumber}_${base.x}_${base.y}_${base.z}`;
    const participant = scoreboard.getParticipants().find(p => p.displayName === baseToRemove);

    if (participant) {
        try {
            scoreboard.removeParticipant(participant);
            player.sendMessage(`§aRemoved base ${base.baseNumber} at X=${base.x}, Y=${base.y}, Z=${base.z} for player ${playerName}.`);
        } catch (error) {
            console.error("Error removing participant:", error);
            player.sendMessage("§cFailed to remove the base.");
            return;
        }
    } else {
        player.sendMessage(`§cNo security base found for player ${playerName} at X=${base.x}, Y=${base.y}, Z=${base.z}.`);
        return;
    }

    // Rename remaining bases to keep numbers sequential
    let newBaseNumber = 1;
    for (const baseEntry of playerBases) {
        if (baseEntry.baseNumber === base.baseNumber) continue; // Skip the removed base

        const oldFakePlayerName = `${playerName}_${baseEntry.baseNumber}_${baseEntry.x}_${baseEntry.y}_${baseEntry.z}`;
        const newFakePlayerName = `${playerName}_${newBaseNumber}_${baseEntry.x}_${baseEntry.y}_${baseEntry.z}`;

        if (oldFakePlayerName !== newFakePlayerName) {
            try {
                // Remove old entry
                scoreboard.removeParticipant(scoreboard.getParticipants().find(p => p.displayName === oldFakePlayerName));
                // Add new entry with updated base number
                player.runCommandAsync(`scoreboard players set "${newFakePlayerName}" basesecurity 1`);
            } catch (error) {
                console.error("Error renaming base:", error);
                player.sendMessage("§cFailed to update base numbering.");
            }
        }
        newBaseNumber++;
    }

    if (goBack) goBack(); // Refresh the base list after removal
}




function homeBaseList(player) {
    const scoreboard = world.scoreboard.getObjective("PlayerTP");

    if (!scoreboard) {
        player.sendMessage("§cNo home bases found in the system.");
        return;
    }

    const participants = scoreboard.getParticipants();
    const playersWithHomes = new Set();

    // Extract only player names before "_1_x_y_z_d"
    for (const participant of participants) {
        const entry = participant.displayName;
        const match = entry.match(/^(.+?)_\d+_-?\d+_-?\d+_-?\d+_-?\d+$/);
        if (match) {
            const playerName = match[1]; // Extracts player name before "_1_x_y_z_d"
            playersWithHomes.add(playerName);
        }
    }

    // Convert the set to a sorted array
    const players = [...playersWithHomes].sort();

    if (players.length === 0) {
        player.sendMessage("§cNo valid home bases found in the system.");
        return;
    }

    const homeBaseForm = new ActionFormData()
        .title("§0Home Base List")
        .body("Select a player to view their home bases:")
        .button("§cBack");

    // Add player names as buttons
    players.forEach(playerName => {
        homeBaseForm.button(`§0${playerName}`);
    });

    homeBaseForm.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            playerBaseMenu(player);
            return;
        }

        const selectedPlayer = players[response.selection - 1];
        showPlayerHomeBases(player, selectedPlayer);
    }).catch((error) => {
        console.error("Error in homeBaseList:", error);
        player.sendMessage("§cAn error occurred while opening the Home Base List.");
    });
}

function showPlayerHomeBases(player, playerName) {
    const scoreboard = world.scoreboard.getObjective("PlayerTP");

    if (!scoreboard) {
        player.sendMessage(`§cNo home bases found for player ${playerName}.`);
        return;
    }

    const homeBases = [];

    // Find all home base entries for the selected player
    scoreboard.getParticipants().forEach(participant => {
        const match = participant.displayName.match(new RegExp(`^${playerName}_(\\d+)_(-?\\d+)_(-?\\d+)_(-?\\d+)_(-?\\d+)$`));
        if (match) {
            const [, homeNumber, x, y, z, dimId] = match;
            homeBases.push({
                homeNumber: parseInt(homeNumber, 10),
                x: parseInt(x, 10),
                y: parseInt(y, 10),
                z: parseInt(z, 10),
                dimId: parseInt(dimId, 10)
            });
        }
    });

    if (homeBases.length === 0) {
        player.sendMessage(`§cNo home bases found for ${playerName}.`);
        return;
    }

    // Sort bases by home number
    homeBases.sort((a, b) => a.homeNumber - b.homeNumber);

    const homeBaseForm = new ActionFormData()
        .title(`${playerName}'s Home Bases`)
        .body("Select a base to manage:")
        .button("§cBack");

    homeBases.forEach(base => {
        let dimensionMarker = "";
        if (base.dimId === 1) {
            dimensionMarker = " §cN"; // Nether
        } else if (base.dimId === 2) {
            dimensionMarker = " §0E"; // The End
        }

        homeBaseForm.button(`§0Home ${base.homeNumber}:\n§rX=${base.x}, Y=${base.y}, Z=${base.z}${dimensionMarker}`);
    });

    homeBaseForm.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            homeBaseList(player); // Go back to the list of players with homes
            return;
        }

        // Identify which home was selected
        const selectedBase = homeBases[response.selection - 1];
        homeBaseOptions(player, playerName, selectedBase);
    }).catch((error) => {
        console.error("Error in showPlayerHomeBases:", error);
        player.sendMessage("§cAn error occurred while opening the Home Bases.");
    });
}





function homeBaseOptions(player, playerName, base) {
    const dimensionMarker = base.dimId === 1 ? " §cN" : base.dimId === 2 ? " §0E" : "";
    const dimensionName = getDimensionPrettyName(base.dimId);

    const form = new ActionFormData()
        .title(`Home ${base.homeNumber} Options`)
        .body(
            `Coordinates: X=${base.x}, Y=${base.y}, Z=${base.z}\n` +
            `Dimension: ${dimensionName}${dimensionMarker}\n\n` +
            `What would you like to do?`
        )
        .button("§aTeleport")
        .button("§cRemove Home")
        .button("§4Back");

    form.show(player).then((response) => {
        if (response.canceled) {
            showPlayerHomeBases(player, playerName);
            return;
        }

        switch (response.selection) {
            case 0: // Teleport
                confirmTeleportToHome(player, base);
                break;
            case 1: // Remove Home
                confirmRemoveHomeBase(player, playerName, base);
                break;
            case 2: // Back
                showPlayerHomeBases(player, playerName);
                break;
        }
    }).catch((error) => {
        console.error("Error in homeBaseOptions:", error);
        player.sendMessage("§cAn error occurred while showing home base options.");
    });
}

/**
 * Confirmation menu before teleporting.
 */
function confirmTeleportToHome(player, base) {
    const dimensionName = getDimensionPrettyName(base.dimId);
    const bedrockDimension = getBedrockDimensionName(base.dimId);

    const form = new ActionFormData()
        .title("Confirm Teleport")
        .body(
            `Do you want to teleport to:\n\n` +
            `X=${base.x}, Y=${base.y}, Z=${base.z}\n` +
            `Dimension: ${dimensionName}\n\n`
        )
        .button("§aYes, Teleport")
        .button("§cNo, Cancel");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            homeBaseOptions(player, base.playerName, base);
            return;
        }

        if (response.selection === 0) {
            teleportToHomeBase(player, base);
        }
    }).catch((error) => {
        console.error("Error in confirmTeleportToHome:", error);
        player.sendMessage("§cAn error occurred while confirming teleportation.");
    });
}

/**
 * Teleports player to a home base.
 */
function teleportToHomeBase(player, base) {
    const bedrockDimension = getBedrockDimensionName(base.dimId);

    player.runCommandAsync(`execute in ${bedrockDimension} run tp @s ${base.x} ${base.y} ${base.z}`)
        .then(() => {
            player.sendMessage(`§aTeleported to Home ${base.homeNumber} at X=${base.x}, Y=${base.y}, Z=${base.z} in ${bedrockDimension}.`);
        })
        .catch((error) => {
            console.error("Teleport failed:", error);
            player.sendMessage("§cFailed to teleport.");
        });
}

/**
 * Confirmation menu before removing a home.
 */
function confirmRemoveHomeBase(player, playerName, base) {
    const dimensionName = getDimensionPrettyName(base.dimId);

    const form = new ActionFormData()
        .title("Confirm Home Removal")
        .body(
            `§4Are you sure you want to remove Home ${base.homeNumber}?\n\n` +
            `Coordinates: X=${base.x}, Y=${base.y}, Z=${base.z}\n` +
            `Dimension: ${dimensionName}\n\n` +
            `§cThis action is irreversible.`
        )
        .button("§aYes, Remove")
        .button("§cNo, Cancel");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 1) {
            homeBaseOptions(player, playerName, base);
            return;
        }

        if (response.selection === 0) {
            removePlayerHomeBase(player, playerName, base);
        }
    }).catch((error) => {
        console.error("Error in confirmRemoveHomeBase:", error);
        player.sendMessage("§cAn error occurred while confirming home removal.");
    });
}

/**
 * Parses a home entry into an object with { playerName, homeNumber, x, y, z, dimId }.
 * Returns null if the format is invalid.
 */
function parseHomeEntry(entryName) {
    const parts = entryName.split("_");
    if (parts.length < 6) return null;

    // Extract values
    const dimId = parseInt(parts.pop());  // Last part is dimension ID
    const z = parseInt(parts.pop());
    const y = parseInt(parts.pop());
    const x = parseInt(parts.pop());
    const homeNumber = parseInt(parts.pop());

    // Remaining parts form the player name
    const playerName = parts.join("_");

    if (isNaN(homeNumber) || isNaN(x) || isNaN(y) || isNaN(z) || isNaN(dimId)) {
        return null; // Invalid format
    }

    return { playerName, homeNumber, x, y, z, dimId };
}

/**
 * Removes a home and renames all higher-numbered homes down to keep order.
 */
function removePlayerHomeBase(player, playerName, base) {
    const scoreboard = world.scoreboard.getObjective("PlayerTP");

    if (!scoreboard) {
        player.sendMessage("§cPlayerTP scoreboard does not exist.");
        return;
    }

    const homeEntry = `${playerName}_${base.homeNumber}_${base.x}_${base.y}_${base.z}_${base.dimId}`;

    // Remove the selected home
    player.runCommandAsync(`scoreboard players reset "${homeEntry}" PlayerTP`)
        .then(() => {
            player.sendMessage(`§aHome ${base.homeNumber} removed successfully.`);

            // Now rename remaining homes to shift down
            shiftHomeNumbers(player, playerName, base.homeNumber);

        }).catch(error => {
            console.error("Failed to remove home:", error);
            player.sendMessage("§cFailed to remove the home.");
        });
}

/**
 * Shifts all home numbers down to fill gaps when a home is deleted.
 */
function shiftHomeNumbers(player, playerName, removedHomeNumber) {
    const scoreboard = world.scoreboard.getObjective("PlayerTP");

    if (!scoreboard) {
        player.sendMessage("§cPlayerTP scoreboard does not exist.");
        return;
    }

    const homeEntries = scoreboard.getParticipants()
        .map(p => parseHomeEntry(p.displayName))
        .filter(data => data && data.playerName === playerName)
        .sort((a, b) => a.homeNumber - b.homeNumber); // Sort numerically

    let updatedEntries = [];

    homeEntries.forEach(home => {
        if (home.homeNumber > removedHomeNumber) {
            // Calculate new home number (shift down)
            const newHomeNumber = home.homeNumber - 1;
            const oldEntry = `${home.playerName}_${home.homeNumber}_${home.x}_${home.y}_${home.z}_${home.dimId}`;
            const newEntry = `${home.playerName}_${newHomeNumber}_${home.x}_${home.y}_${home.z}_${home.dimId}`;

            updatedEntries.push({ oldEntry, newEntry });
        }
    });

    // Process renaming
    Promise.all(updatedEntries.map(({ oldEntry, newEntry }) => {
        return player.runCommandAsync(`scoreboard players reset "${oldEntry}" PlayerTP`)
            .then(() => player.runCommandAsync(`scoreboard players set "${newEntry}" PlayerTP 1`));
    })).then(() => {
        player.sendMessage("§aHome numbers updated successfully.");
        showPlayerHomeBases(player, playerName);
    }).catch(error => {
        console.error("Error updating home numbers:", error);
        player.sendMessage("§cAn error occurred while updating home numbers.");
    });
}


/**
 * Returns a **player-friendly** dimension name.
 */
function getDimensionPrettyName(dimId) {
    switch (dimId) {
        case 1: return "Nether";
        case 2: return "The End";
        default: return "Overworld";
    }
}

/**
 * Returns Bedrock's execute dimension name.
 */
function getBedrockDimensionName(dimId) {
    switch (dimId) {
        case 1: return "nether";
        case 2: return "the_end";
        default: return "overworld";
    }
}
