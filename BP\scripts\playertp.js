import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData, MessageFormData } from "@minecraft/server-ui";
import { tpHome } from "./tp_home";
import { mainMenu } from "./mainmenu";

const teleportRequests = {};

/**
 * Main Player TP Menu
 */
export function playerTpmenu(player) {
    // Get the "admin" scoreboard (where you store 'hometps', 'playertpsplayer', 'teleportspawn', etc.)
    const adminScoreboard = world.scoreboard.getObjective("admin");
    if (!adminScoreboard) {
        player.sendMessage("§cAdmin scoreboard not found. Have you set it up?");
        return;
    }

    const tpMenuForm = new ActionFormData()
        .title("Player TP Menu")
        .body("Choose an option:");

    // Check scoreboard toggles
    const homeTpsValue = getScore(adminScoreboard, "hometps");
    const p2pValue = getScore(adminScoreboard, "playertpsplayer");
    const spawnValue = getScore(adminScoreboard, "teleportspawn");

    // Show Player-to-Player TP button if 'playertpsplayer' == 1
    if (p2pValue === 1) {
        tpMenuForm.button("Player-to-Player TP", "textures/ui/FriendsIcon");
    }

    // Show Home TP button if 'hometps' == 1
    if (homeTpsValue === 1) {
        tpMenuForm.button("Home TP \n Home settings", "textures/ui/icon_fall");
    }

    // Show TP Spawn button if 'teleportspawn' == 1
    if (spawnValue === 1) {
        tpMenuForm.button("TP Spawn", "textures/ui/icon_agent");
    }

    // Always show back button
    tpMenuForm.button("§l§cBack", "textures/ui/book_arrowleft_hover");

    tpMenuForm.show(player).then((response) => {
        if (response.canceled) {
            player.sendMessage("Player TP Menu closed.");
            return;
        }

        let index = 0;

        // 1) Player-to-Player TP
        if (p2pValue === 1) {
            if (response.selection === index++) {
                playerToPlayerMenu(player);
                return;
            }
        }

        // 2) Home TP
        if (homeTpsValue === 1) {
            if (response.selection === index++) {
                tpHome(player);
                return;
            }
        }

        // 3) TP Spawn
        if (spawnValue === 1) {
            if (response.selection === index++) {
                confirmTpSpawn(player);
                return;
            }
        }

        // 4) Back
        if (response.selection === index) {
            mainMenu(player);
        }
    });
}

/**
 * Helper: Get a fake player's score (by displayName) from a scoreboard.
 * @param {Objective} objective 
 * @param {string} fakePlayerName 
 * @returns {number}
 */
function getScore(objective, fakePlayerName) {
    const participant = objective
        .getParticipants()
        .find((p) => p.displayName === fakePlayerName);
    return participant ? objective.getScore(participant) : 0;
}

/**
 * Player-to-Player TP Menu
 */
function playerToPlayerMenu(player) {
    const p2pForm = new ActionFormData()
        .title("Player-to-Player TP")
        .body("Choose an option:")
        .button("Request TP to Player")
        .button("Accept Request from Player")
        .button("Back");

    p2pForm.show(player).then((response) => {
        if (response.canceled) {
            // Return to Player TP Menu
            playerTpmenu(player);
            return;
        }

        switch (response.selection) {
            case 0:
                requestTpToPlayer(player);
                break;
            case 1:
                acceptTpRequest(player);
                break;
            case 2:
                playerTpmenu(player);
                break;
        }
    });
}

/**
 * Confirm Teleport to Spawn
 */
function confirmTpSpawn(player) {
    const confirmationForm = new MessageFormData()
        .title("Confirm Teleport")
        .body("Are you sure you want to teleport to spawn?")
        .button1("No")
        .button2("Yes");

    confirmationForm.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            // Return to Player TP Menu
            playerTpmenu(player);
        } else if (response.selection === 1) {
            tpSpawn(player);
        }
    });
}

/**
 * Teleport to Spawn
 */
async function tpSpawn(player) {
    try {
        // Get cost from 'teleporthomecost' in the "admin" scoreboard
        const adminScoreboard = world.scoreboard.getObjective("admin");
        const tpCost = adminScoreboard ? getScore(adminScoreboard, "teleporthomecost") : 0;

        // Check Money scoreboard
        const moneyObjective = world.scoreboard.getObjective("Money");
        if (!moneyObjective) {
            player.sendMessage("§cMoney scoreboard is not set up.");
            return;
        }

        const playerMoney = moneyObjective.getScore(player);

        // Check if player can pay
        if (tpCost > 0 && playerMoney < tpCost) {
            player.sendMessage(`§cInsufficient funds. You need $${tpCost}.`);
            return;
        }

        // Deduct cost if needed
        if (tpCost > 0) {
            await player.runCommandAsync(`scoreboard players remove "${player.name}" Money ${tpCost}`);
            player.sendMessage(`§a$${tpCost} deducted for teleportation.`);
        } else {
            player.sendMessage("§aTeleportation is free.");
        }

        // Retrieve the spawn location from scoreboard "setspawn"
        const scoreboard = world.scoreboard.getObjective("setspawn");
        if (!scoreboard) {
            player.sendMessage("§cNo global spawn point found in the scoreboard.");
            return;
        }

        let spawnEntry = null;
        scoreboard.getParticipants().forEach((participant) => {
            if (participant.displayName.startsWith("global_spawn")) {
                spawnEntry = participant.displayName;
            }
        });

        if (!spawnEntry) {
            player.sendMessage(
                "§cNo valid global spawn point found! Use the Set Spawn function to define one."
            );
            return;
        }

        const match = spawnEntry.match(/^global_spawn_(\w+)_(-?\d+)_(-?\d+)_(-?\d+)$/);
        if (!match) {
            player.sendMessage(
                "§cGlobal spawn point format is invalid. Please reset the spawn."
            );
            return;
        }

        const [, dimension, x, y, z] = match;

        // Teleport the player
        await player.runCommandAsync(
            `execute as @s in ${dimension} run tp @s ${x} ${y} ${z}`
        );
        player.sendMessage(
            `§aTeleported to the global spawn point: X=${x}, Y=${y}, Z=${z} in ${dimension}.`
        );
    } catch (error) {
        console.error("Error teleporting to global spawn point:", error);
        player.sendMessage("§cAn error occurred while teleporting to the global spawn.");
    }
}

/**
 * Request TP to Another Player (Now always free to request)
 */
function requestTpToPlayer(player) {
    // We do NOT check or deduct money here anymore
    // The cost is only applied upon acceptance

    // We'll still fetch the cost from the scoreboard
    const adminScoreboard = world.scoreboard.getObjective("admin");
    const tpCost = adminScoreboard ? getScore(adminScoreboard, "teleporthomecost") : 0;

    const tpPlayerForm = new ActionFormData().title("Request TP to Player");

    const players = Array.from(world.getPlayers()).map((p) => p.name);
    players.sort();

    players.forEach((playerName) => {
        tpPlayerForm.button(playerName);
    });

    tpPlayerForm.show(player).then((response) => {
        if (response.canceled) return;

        const selectedPlayerName = players[response.selection];
        const targetPlayer = world
            .getPlayers()
            .find((p) => p.name === selectedPlayerName);

        if (!targetPlayer) {
            player.sendMessage("The selected player is no longer online.");
            return;
        }

        // Record the request (cost is stored but not deducted)
        if (!teleportRequests[selectedPlayerName]) {
            teleportRequests[selectedPlayerName] = [];
        }
        teleportRequests[selectedPlayerName].push({
            requester: player.name,
            cost: tpCost
        });

        // Notify
        targetPlayer.sendMessage(
            `${player.name} has sent you a teleport request! Open the menu to review.`
        );
        player.sendMessage(`Teleport request sent to ${selectedPlayerName} (free to request).`);
    });
}

/**
 * Accept a Pending TP Request
 */
function acceptTpRequest(player) {
    const requests = teleportRequests[player.name];
    if (!requests || requests.length === 0) {
        player.sendMessage("You have no pending teleport requests.");
        return;
    }

    const requestForm = new ActionFormData()
        .title("Pending Teleport Requests")
        .body("Choose a player to accept or decline the request.");

    requests.forEach((req) => {
        requestForm.button(req.requester);
    });

    requestForm.show(player).then((response) => {
        if (response.canceled) return;

        const selectedRequest = requests[response.selection];
        if (!selectedRequest) return;

        const requestingPlayerName = selectedRequest.requester;
        const requestingPlayer = world
            .getPlayers()
            .find((p) => p.name === requestingPlayerName);

        // If the requester is offline, remove their request
        if (!requestingPlayer) {
            player.sendMessage("That player is no longer online.");
            teleportRequests[player.name] = requests.filter(
                (req) => req.requester !== requestingPlayerName
            );
            return;
        }

        // Confirm acceptance
        const confirmTpForm = new MessageFormData()
            .title("Teleport Request")
            .body(`Accept ${requestingPlayerName}'s teleport request?\n\n` +
                `They will be charged $${selectedRequest.cost} if accepted.`)
            .button1("No")
            .button2("Yes");

        confirmTpForm.show(player).then((confirmResponse) => {
            if (confirmResponse.selection === 1) {
                // The cost is enforced on acceptance
                const tpCost = selectedRequest.cost;

                const moneyObjective = world.scoreboard.getObjective("Money");
                if (!moneyObjective) {
                    player.sendMessage("§cMoney scoreboard does not exist.");
                    return;
                }

                const requesterMoney = moneyObjective.getScore(requestingPlayer);

                // Check if the requester can pay
                if (tpCost > 0 && requesterMoney < tpCost) {
                    // Not enough money
                    requestingPlayer.sendMessage(
                        `§cInsufficient funds. You need $${tpCost} to teleport.`
                    );
                    player.sendMessage(
                        `${requestingPlayer.name} tried to teleport but lacked sufficient funds.`
                    );
                } else {
                    // Deduct if cost > 0
                    if (tpCost > 0) {
                        requestingPlayer.runCommandAsync(
                            `scoreboard players remove "${requestingPlayer.name}" Money ${tpCost}`
                        );
                    }
                    // Perform the teleport
                    requestingPlayer.runCommandAsync(`tp @s "${player.name}"`);

                    // Notify both players
                    requestingPlayer.sendMessage(
                        tpCost > 0
                            ? `You have been teleported to ${player.name}. ($${tpCost} deducted)`
                            : `You have been teleported to ${player.name}. (Free)`
                    );
                    player.sendMessage(`${requestingPlayer.name} has teleported to you.`);
                }
            } else {
                requestingPlayer.sendMessage(`${player.name} declined your teleport request.`);
            }

            // Remove this request from the list after processing
            teleportRequests[player.name] = requests.filter(
                (req) => req.requester !== requestingPlayerName
            );
        });
    });
}
