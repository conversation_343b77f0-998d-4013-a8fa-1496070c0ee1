import {
    world,
    system,
    Player
} from "@minecraft/server";

const checkIntervalTicks = 100;

// Run the proximity checks every 100 ticks
system.runInterval(() => {
    checkPlayerProximityAndBanList();
}, checkIntervalTicks);

export function checkPlayerProximityAndBanList() {
    const baseSecurityObj = world.scoreboard.getObjective("basesecurity");
    if (!baseSecurityObj) return;

    const adminObj = world.scoreboard.getObjective("admin"); // Admin scoreboard
    const banListObj = world.scoreboard.getObjective("banlist");
    const friendsListObj = world.scoreboard.getObjective("friendslist");

    // e.g. scoreboard players set "basesecurityrange" admin 300
    const generalAdmin = world.scoreboard.getObjective("admin");
    if (!generalAdmin) return;

    const rangeParticipant = generalAdmin.getParticipants().find(
        (p) => p.displayName === "basesecurityrange"
    );
    if (!rangeParticipant) return;

    const baseRange = generalAdmin.getScore(rangeParticipant);
    if (!baseRange) return;

    // All base entries in "basesecurity"
    const baseParticipants = baseSecurityObj.getParticipants();

    for (const player of world.getPlayers()) {
        // 1) Only check if the player is in the Overworld
        if (player.dimension.id !== "minecraft:overworld") {
            // If not in the Overworld, skip checks entirely
            continue;
        }

        const playerName = player.name;

        // 2) If admin, skip ban & base checks
        if (isAdminPlayer(playerName, adminObj)) {
            continue;
        }

        // 3) If they’re on the ban list => Kick
        if (isBannedPlayer(playerName, banListObj)) {
            kickPlayer(player, "You are banned from this server.");
            continue;
        }

        // 4) Otherwise do normal base checks
        const playerPos = player.location;
        for (const basePart of baseParticipants) {
            const baseName = basePart.displayName;
            const parsed = parseBaseParticipantName(baseName);
            if (!parsed) continue;

            const { ownerName, x, y, z } = parsed;

            // Check if the base is "on" (score=1) or "off" (score=0)
            const baseScore = baseSecurityObj.getScore(basePart);
            if (baseScore === 0) {
                // Security off => let them in
                continue;
            }
            // baseScore === 1 => do distance/friend checks
            const dx = playerPos.x - x;
            const dy = playerPos.y - y;
            const dz = playerPos.z - z;
            const distSq = dx * dx + dy * dy + dz * dz;

            if (distSq <= baseRange * baseRange) {
                const isAllowed = isPlayerOwnerOrFriend(
                    playerName,
                    ownerName,
                    friendsListObj,
                    player
                );
                if (!isAllowed) {
                    player.sendMessage(
                        `§cYou are trying to enter ${ownerName}'s base at [${x}, ${y}, ${z}] without permission!`
                    );
                    pushPlayerOutsideRadius(player, { x, z }, baseRange + 1);
                }
            }
        }
    }
}

// ------------------- Helper Functions -------------------

// 1) Check Admin Scoreboard
function isAdminPlayer(playerName, adminScoreObj) {
    if (!adminScoreObj) return false;

    // Participants might be "admin_PlayerName_owner" or "admin_PlayerName"
    const participants = adminScoreObj.getParticipants();
    const pattern1 = `admin_${playerName}_owner`;
    const pattern2 = `admin_${playerName}`;

    return participants.some(
        (p) => p.displayName === pattern1 || p.displayName === pattern2
    );
}

// 2) Ban Check
function isBannedPlayer(playerName, banListObj) {
    if (!banListObj) return false;
    const banFakePlayer = `ban_${playerName}`;
    // If found in scoreboard at all => banned
    return banListObj.getParticipants().some(
        (p) => p.displayName === banFakePlayer
    );
}

// 3) Kick Command
function kickPlayer(player, reason = "Banned") {
    try {
        const dim = player.dimension;
        const quotedName = JSON.stringify(player.name);
        dim.runCommandAsync(`kick ${quotedName} ${reason}`);
    } catch (err) {
        console.warn(`Failed to kick ${player.name}:`, err);
    }
}

// 4) Parse base scoreboard name
// Format: "ownerName_baseIndex_x_y_z"
function parseBaseParticipantName(fullName) {
    const parts = fullName.split("_");
    if (parts.length < 4) return null;

    const z = parseInt(parts.pop(), 10);
    const y = parseInt(parts.pop(), 10);
    const x = parseInt(parts.pop(), 10);

    let baseIndex = null;
    while (parts.length > 0) {
        const last = parts[parts.length - 1];
        // If the last part is an integer, assume it's the base index (like "1")
        if (/^-?\d+$/.test(last)) {
            baseIndex = parseInt(last, 10);
            parts.pop();
        } else {
            break;
        }
    }

    // The rest is the owner's name (with spaces or underscores replaced as you prefer)
    const ownerName = parts.join(" ");
    return { ownerName, x, y, z, baseIndex };
}

// 5) Friend Check
function isPlayerOwnerOrFriend(playerName, ownerName, friendsListObj, playerForDebug) {
    // If the base belongs to them
    if (playerName === ownerName) return true;
    if (!friendsListObj) return false;

    const friendParticipants = friendsListObj.getParticipants();
    for (const part of friendParticipants) {
        const listString = part.displayName.trim();
        // Some servers use "¦", you could unify them with replaceAll if needed
        const normalized = listString.replaceAll("¦", "|");
        const names = normalized.split("|").map(s => s.trim()).filter(Boolean);

        if (names.length === 0) continue;
        const listOwner = names[0];
        if (listOwner === ownerName && names.includes(playerName)) {
            return true;
        }
    }
    return false;
}

// 6) Push Player Outside Radius
function pushPlayerOutsideRadius(player, baseCenter, outDistance) {
    const dimension = player.dimension;
    // If we’re not in Overworld, skip, though we already checked this
    if (!dimension || dimension.id !== "minecraft:overworld") return;

    const playerPos = player.location;
    const dx = playerPos.x - baseCenter.x;
    const dz = playerPos.z - baseCenter.z;
    let dist2D = Math.sqrt(dx * dx + dz * dz);

    if (dist2D < 0.0001) {
        dist2D = 1;
    }
    const scale = outDistance / dist2D;
    const newX = baseCenter.x + dx * scale;
    const newZ = baseCenter.z + dz * scale;

    const minY = Math.max(1, playerPos.y - 5);
    const maxY = Math.min(319, playerPos.y + 5);
    let teleported = false;

    for (let testY = minY; testY <= maxY; testY++) {
        const groundBlock = dimension.getBlock({
            x: Math.floor(newX),
            y: testY,
            z: Math.floor(newZ)
        });
        if (!groundBlock) continue;

        if (groundBlock.typeId !== "minecraft:air") {
            const blockAbove1 = dimension.getBlock({
                x: Math.floor(newX),
                y: testY + 1,
                z: Math.floor(newZ)
            });
            const blockAbove2 = dimension.getBlock({
                x: Math.floor(newX),
                y: testY + 2,
                z: Math.floor(newZ)
            });
            if (
                blockAbove1 && blockAbove2 &&
                blockAbove1.typeId === "minecraft:air" &&
                blockAbove2.typeId === "minecraft:air"
            ) {
                player.teleport(
                    {
                        x: Math.floor(newX) + 0.5,
                        y: testY + 1,
                        z: Math.floor(newZ) + 0.5
                    },
                    { dimension, keepVelocity: false }
                );
                teleported = true;
                break;
            }
        }
    }

    if (!teleported) {
        // If we never found a suitable Y, just put them at the same Y
        player.teleport(
            {
                x: Math.floor(newX) + 0.5,
                y: playerPos.y + 1,
                z: Math.floor(newZ) + 0.5
            },
            { dimension, keepVelocity: false }
        );
    }
}
