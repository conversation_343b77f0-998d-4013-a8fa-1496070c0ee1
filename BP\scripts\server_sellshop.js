import { world, system, ItemStack } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { mainMenu } from "./mainmenu";

export function serverSellShop(player) {
    const shops = getSellShops();

    if (shops.length === 0) {
        player.sendMessage("You haven't created any sell shops yet.");
        return;
    }

    const form = new ActionFormData()
        .title("Your Sell Shops")
        .body("Select a shop to view its items.")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    // Add a button for each shop
    shops.forEach(shop => {
        form.button(shop, "textures/ui/MCoin");
    });

    form.show(player).then(response => {
        if (response.canceled) return; // Exit the menu

        if (response.selection === 0) {
            mainMenu(player); // Back button
            return;
        }

        const selectedShop = shops[response.selection - 1];
        if (selectedShop) {
            viewShopItems(player, selectedShop);
        } else {
            player.sendMessage("Failed to load the selected shop. Please try again.");
        }
    }).catch(error => {
        console.error("Failed to display sell shops: ", error);
        player.sendMessage("An error occurred. Please try again later.");
    });
}

function viewShopItems(player, shopName) {
    const items = getShopItems(shopName);

    if (!items || items.length === 0) {
        player.sendMessage(`The shop '${shopName}' has no items to display.`);
        return;
    }

    const form = new ActionFormData()
        .title(`Items in ${shopName}`)
        .body("Select an item to sell:")
        .button("Back", "textures/ui/book_arrowleft_hover");

    items.forEach(item => {
        // For display, remove any namespace from the baseIdentifier.
        let friendlyName = item.baseIdentifier;
        if (friendlyName.includes(":")) {
            friendlyName = friendlyName.split(":")[1];
        }
        friendlyName = friendlyName.replace(/_/g, " ");
        form.button(`${friendlyName} x${item.setAmount}\nPrice: ${item.price}`, "textures/ui/MCoin");
    });

    form.show(player).then(response => {
        if (response.canceled) return; // Exit the menu

        if (response.selection === 0) {
            serverSellShop(player); // Back button
            return;
        }

        const selectedItem = items[response.selection - 1];
        if (selectedItem) {
            sellItem(player, selectedItem, shopName);
        } else {
            player.sendMessage("Failed to process the selected item. Please try again.");
        }
    }).catch(error => {
        console.error(`Failed to display items for shop ${shopName}: `, error);
        player.sendMessage("An error occurred. Please try again later.");
    });
}

/**
 * Attempts to sell an item in sets.
 * The shop item is stored as "baseIdentifier_setAmount" (e.g. "minecraft:stone_3").
 * The function checks the player's inventory for the baseIdentifier,
 * calculates the number of complete sets available,
 * prompts the player for the number of sets to sell,
 * and, if confirmed, clears the items and awards Money.
 */
async function sellItem(player, item, shopName) {
    const inventory = player.getComponent("minecraft:inventory")?.container;
    if (!inventory) {
        player.sendMessage("Unable to access your inventory.");
        return;
    }

    // Use the baseIdentifier (without the set amount) when checking the inventory.
    const exactIdentifier = item.baseIdentifier;

    let playerItemCount = 0;
    // Check the player's inventory for matching items.
    for (let slot = 0; slot < inventory.size; slot++) {
        const slotItem = inventory.getItem(slot);
        if (slotItem) {
            // Debug log (optional)
            console.log(`Checking slot ${slot}: ${slotItem.typeId} against ${exactIdentifier}`);
            if (slotItem.typeId === exactIdentifier) {
                playerItemCount += slotItem.amount;
            }
        }
    }

    // Determine how many complete sets the player can sell.
    const availableSets = Math.floor(playerItemCount / item.setAmount);
    if (availableSets < 1) {
        player.sendMessage(`You don't have enough '${item.baseIdentifier}' in your inventory to sell a set (each set requires ${item.setAmount}).`);
        return;
    }

    // Prompt the player for the number of sets to sell.
    const form = new ModalFormData()
        .title(`Sell ${item.baseIdentifier}`)
        .textField(`Enter the number of sets to sell (Max: ${availableSets}):`, "e.g., 2", "1");

    form.show(player).then(async (response) => {
        if (response.canceled) return;

        const setsToSell = parseInt(response.formValues[0], 10);
        if (isNaN(setsToSell) || setsToSell < 1 || setsToSell > availableSets) {
            player.sendMessage("Invalid quantity entered. Please try again.");
            return;
        }

        // Calculate total items to remove and total earnings.
        const totalItemsToRemove = setsToSell * item.setAmount;
        const totalEarnings = setsToSell * item.price;

        // (Optional) Confirm the transaction with the player.
        const confirmForm = new ActionFormData()
            .title("Confirm Sale")
            .body(`You will sell ${totalItemsToRemove} items (i.e. ${setsToSell} set(s)) for ${totalEarnings} Money.\nDo you want to proceed?`)
            .button("Yes")
            .button("No");
        confirmForm.show(player).then(async (confirmResponse) => {
            if (confirmResponse.canceled || confirmResponse.selection === 1) {
                player.sendMessage("Sale cancelled.");
                return;
            }

            try {
                // Remove the items from the player's inventory.
                await player.runCommandAsync(`clear @s ${exactIdentifier} 0 ${totalItemsToRemove}`);
                // Add the money earned.
                await player.runCommandAsync(`scoreboard players add @s Money ${totalEarnings}`);
                player.sendMessage(`You sold ${totalItemsToRemove}x ${item.baseIdentifier} (${setsToSell} set(s)) for ${totalEarnings} Money.`);
            } catch (error) {
                player.sendMessage("An error occurred while processing your transaction.");
                console.error("Failed to update inventory or scoreboard: ", error);
            }

            viewShopItems(player, shopName); // Refresh the shop menu
        }).catch((error) => {
            player.sendMessage("An error occurred. Please try again later.");
            console.error("Failed to process confirmation form: ", error);
        });
    }).catch((error) => {
        player.sendMessage("An error occurred. Please try again later.");
        console.error("Failed to process sell form: ", error);
    });
}

/*************************************************
 * Retrieve Sell Shop Names
 *************************************************/
function getSellShops() {
    const objectives = world.scoreboard.getObjectives();
    const shopNames = [];

    objectives.forEach(obj => {
        if (obj.id.startsWith("Sellshop_")) {
            let shopName = obj.id.replace("Sellshop_", "");
            // Convert stored characters back to their intended form.
            shopName = shopName.replace(/¤/g, "§").replace(/_/g, " ").replace(/¦/g, "&");
            shopNames.push(shopName);
        }
    });

    return shopNames;
}

/*************************************************
 * Retrieve Shop Items and Parse the Identifier
 * Items are stored as "baseIdentifier_setAmount" (e.g. "minecraft:stone_3")
 *************************************************/
function getShopItems(shopName) {
    const shopScoreboardName = `Sellshop_${shopName
        .replace(/§/g, "¤")
        .replace(/ /g, "_")
        .replace(/&/g, "¦")}`;
    const items = [];

    try {
        const objective = world.scoreboard.getObjective(shopScoreboardName);
        if (!objective) {
            console.error(`Scoreboard for shop '${shopName}' not found.`);
            return items;
        }

        const participants = objective.getParticipants();
        participants.forEach(participant => {
            const fullIdentifier = participant.displayName; // e.g. "minecraft:stone_3" or "zombie:dirt_5"
            const cost = objective.getScore(participant);
            const lastUnderscore = fullIdentifier.lastIndexOf("_");
            if (lastUnderscore !== -1) {
                const baseIdentifier = fullIdentifier.substring(0, lastUnderscore);
                const setAmount = parseInt(fullIdentifier.substring(lastUnderscore + 1), 10);
                items.push({
                    fullIdentifier,  // stored string e.g. "minecraft:stone_3"
                    baseIdentifier,  // e.g. "minecraft:stone"
                    setAmount,       // e.g. 3
                    price: cost
                });
            } else {
                // Fallback if no underscore is found
                items.push({
                    fullIdentifier,
                    baseIdentifier: fullIdentifier,
                    setAmount: 1,
                    price: cost
                });
            }
        });
    } catch (error) {
        console.error(`Failed to retrieve items for shop '${shopName}':`, error);
    }

    return items;
}

/*************************************************
 * (Optional) Other shop management functions
 *************************************************/
function sellShop(player) {
    const shops = getSellShops();

    const form = new ActionFormData()
        .title("Sell Shop")
        .body(shops.length === 0 ? "No shops available. Add a shop to get started!" : "Select a shop to edit or view.")
        .button("Add Shop", "textures/ui/mining_fatigue_effect")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover");

    shops.forEach(shop => {
        form.button(shop, "textures/ui/MCoin");
    });

    form.show(player).then(response => {
        if (response.canceled) return;

        if (response.selection === 0) {
            createSellShop(player, () => sellShop(player));
        } else if (response.selection === 1) {
            // Assuming you have a playerMenuSettings function imported.
            playerMenuSettings(player);
        } else {
            const selectedShop = shops[response.selection - 2];
            if (selectedShop) {
                manageShop(player, selectedShop);
            }
        }
    });
}

function manageShop(player, shopName) {
    const items = getShopItems(shopName);

    const form = new ActionFormData()
        .title(`Manage Shop: ${shopName}`)
        .body(items.length === 0 ? "No items added yet. Add items to get started!" : "Manage your shop items.")
        .button("Add Item", "textures/ui/mashup_world")
        .button("Back to Shop List", "textures/ui/book_arrowleft_hover");

    items.forEach(item => {
        // For display, remove any namespace from the baseIdentifier.
        let friendlyName = item.baseIdentifier;
        if (friendlyName.includes(":")) {
            friendlyName = friendlyName.split(":")[1];
        }
        friendlyName = friendlyName.replace(/_/g, " ");
        form.button(`${friendlyName} x${item.setAmount}\nPrice: ${item.price}`, "textures/ui/MCoin");
    });

    form.show(player).then(response => {
        if (response.canceled) return;

        if (response.selection === 0) {
            addItemToShop(player, shopName);
        } else if (response.selection === 1) {
            sellShop(player);
        } else {
            const selectedItem = items[response.selection - 2]; // Adjust for "Add Item" and "Back" buttons
            if (selectedItem) {
                // For the sell shop, you might simply let the player view the items again,
                // or you could add an edit function.
                viewShopItems(player, shopName);
            }
        }
    });
}

// Note: addItemToShop and createSellShop functions are assumed to be defined elsewhere.
