import { world, system, ItemStack } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { mainMenu } from "./mainmenu"

export function serverStoresMenu(player) {
    const serverStoresForm = new ActionFormData()
        .title("§6Server Stores")
        .body("Browse all available server stores.")
        .button("§l§cBack", "textures/ui/book_arrowleft_hover"); // Back button is always added

    const scoreboards = world.scoreboard.getObjectives();
    const stores = scoreboards.filter((obj) => obj.id.startsWith("store_"));

    // Add a button for each store
    stores.forEach((store) => {
        const storeName = decodeString(store.id.replace("store_", "")); // Decode store name
        serverStoresForm.button(storeName);
    });

    serverStoresForm.show(player).then((response) => {
        if (response.canceled) return; // Simply close the menu if Escape is pressed

        if (response.selection === 0) {
            // Back button
            mainMenu(player);
            return;
        }

        const selectedStore = stores[response.selection - 1].id; // Adjust index to match store list
        openStore(player, selectedStore); // Open the selected store
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err}`);
    });
}

/**
 * Opens a store for buying. This version assumes that items are stored
 * in the new format: <identifier>_<setAmount> with a stored price.
 */
function openStore(player, storeId) {
    const decodedStoreName = decodeString(storeId.replace("store_", ""));
    const storeForm = new ActionFormData()
        .title(`§6Store: ${decodedStoreName}`)
        .body("Available items in this store:");
    const storeObjective = world.scoreboard.getObjective(storeId);
    const participants = storeObjective ? storeObjective.getParticipants() : [];
    const items = [];
    // Parse each stored item into an object with baseIdentifier, setAmount, and price.
    participants.forEach((participant) => {
        const fullName = participant.displayName; // e.g. "minecraft:stone_10" or "zombie:dirt_5"
        const lastUnderscore = fullName.lastIndexOf("_");
        if (lastUnderscore === -1) return; // Skip if format is invalid.
        const baseIdentifier = fullName.substring(0, lastUnderscore);
        const setAmount = parseInt(fullName.substring(lastUnderscore + 1), 10);
        const price = storeObjective.getScore(participant);
        items.push({ fullName, baseIdentifier, setAmount, price });
    });
    // Add a button for each item.
    items.forEach((item) => {
        let friendlyName = item.baseIdentifier;
        if (friendlyName.includes(":")) {
            friendlyName = friendlyName.split(":")[1];
        }
        friendlyName = friendlyName.replace(/_/g, " ");
        storeForm.button(`${decodeString(friendlyName)} x${item.setAmount} - $${item.price}`, "textures/ui/MCoin");
    });
    storeForm.button("§l§cBack", "textures/ui/book_arrowleft_hover"); // Back button

    storeForm.show(player).then((response) => {
        if (response.canceled) return; // Simply close the menu if Escape is pressed

        // If the last button (Back) was selected:
        if (response.selection === items.length) {
            serverStoresMenu(player);
            return;
        }
        const selectedItem = items[response.selection];
        // Call the buy function with the selected item.
        buyItem(player, storeId, selectedItem);
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err}`);
    });
}

/**
 * Prompts the player to buy a given item. The item object contains:
 * - baseIdentifier (e.g. "minecraft:stone")
 * - setAmount (e.g. 10)
 * - price (money cost per set)
 *
 * The form asks how many sets (each set consists of setAmount items) to buy.
 * The total cost is calculated as (sets * price) and the player is given (sets * setAmount)
 * of the item.
 */
function buyItem(player, storeId, item) {
    const buyItemForm = new ModalFormData()
        .title(`§6Buy Item: ${decodeString(item.baseIdentifier)}`)
        .textField(
            `How many sets of ${item.setAmount} would you like to buy? (Price per set: $${item.price})\n§4Make sure you have enough inventory space!`,
            "Sets",
            "1"
        );

    buyItemForm.show(player).then((response) => {
        if (response.canceled) {
            openStore(player, storeId);
            return;
        }
        const setsToBuy = parseInt(response.formValues[0], 10);
        if (isNaN(setsToBuy) || setsToBuy < 1) {
            player.sendMessage("§cInvalid number of sets entered.");
            openStore(player, storeId);
            return;
        }
        // Calculate total cost and total items.
        const totalCost = setsToBuy * item.price;
        const totalItems = setsToBuy * item.setAmount;

        // Check player's money.
        const moneyObjective = world.scoreboard.getObjective("Money");
        const moneyParticipant = moneyObjective.getParticipants().find((p) => p.displayName === player.name);
        if (!moneyParticipant) {
            player.sendMessage("§cYou do not have any money.");
            openStore(player, storeId);
            return;
        }
        const currentMoney = moneyObjective.getScore(moneyParticipant);
        if (currentMoney < totalCost) {
            player.sendMessage(`§cYou do not have enough money. Required: $${totalCost}, You have: $${currentMoney}.`);
            openStore(player, storeId);
            return;
        }

        // Give the items in stacks (max 64 per stack).
        const maxStackSize = 64;
        let remaining = totalItems;
        let success = true;
        while (remaining > 0) {
            const stackSize = Math.min(remaining, maxStackSize);
            try {
                player.runCommandAsync(`give @s ${item.baseIdentifier} ${stackSize}`);
            } catch (error) {
                console.error(`Failed to give items '${item.baseIdentifier}':`, error);
                success = false;
                break;
            }
            remaining -= stackSize;
        }
        if (!success) {
            player.sendMessage("§cCould not deliver the items. Please ensure you have enough inventory space.");
        } else {
            // Deduct money.
            player.runCommandAsync(`scoreboard players remove "${player.name}" Money ${totalCost}`).then(() => {
                player.sendMessage(`§aYou bought ${totalItems}x ${decodeString(item.baseIdentifier)} (in ${setsToBuy} set(s)) for $${totalCost}.`);
            }).catch((err) => {
                player.sendMessage(`§cFailed to update your money: ${err}`);
            });
        }
        openStore(player, storeId);
    }).catch((err) => {
        player.sendMessage(`§cAn error occurred: ${err}`);
        openStore(player, storeId);
    });
}



function decodeString(input) {
    return input.replace(/¦/g, "&").replace(/¤/g, "§").replace(/_/g, " ");
}
