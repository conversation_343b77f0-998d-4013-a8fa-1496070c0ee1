import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData, MessageFormData } from "@minecraft/server-ui";
import { playerTpmenu } from "./playertp";

/**
 * Converts a dimension identifier (Bedrock-based) to a numeric ID.
 * 0 = Overworld, 1 = Nether, 2 = The End
 * 
 * If your server returns "minecraft:the_nether" or "minecraft:nether", 
 * both are covered here for Nether.
 */
function getDimensionId(dimensionName) {
    // For debug, you can temporarily do:
    // console.warn(`DEBUG dimensionName: ${dimensionName}`);

    if (dimensionName === "minecraft:nether" || dimensionName === "minecraft:the_nether") {
        return 1;
    }
    // <-- CHANGED: Support The End
    if (dimensionName === "minecraft:the_end") {
        return 2;
    }
    // Default to Overworld for anything else
    return 0;
}

/**
 * Converts a numeric dimension ID to Bedrock's /execute short name.
 * 0 -> "overworld"
 * 1 -> "nether"
 * 2 -> "the_end"
 */
function getBedrockDimensionName(dimId) {
    switch (dimId) {
        case 1:
            return "nether";
        case 2: // <-- CHANGED: Added The End
            return "the_end";
        default:
            return "overworld";
    }
}

/**
 * Returns a **player-friendly** dimension name for display.
 */
function getDimensionPrettyName(dimId) {
    switch (dimId) {
        case 1:
            return "Nether";
        case 2: // <-- CHANGED: The End
            return "The End";
        default:
            return "Overworld";
    }
}

/**
 * Builds the single scoreboard entry for a home:
 * "<playerName>_<homeNumber>_<x>_<y>_<z>_<dimensionId>"
 */
function buildHomeEntry(playerName, homeNumber, x, y, z, dimensionId) {
    return `${playerName}_${homeNumber}_${x}_${y}_${z}_${dimensionId}`;
}

/**
 * Parses a scoreboard entry into { playerName, homeNumber, x, y, z, dimId }.
 * Returns null if format is invalid.
 */
function parseHomeEntry(entryName) {
    const parts = entryName.split("_");
    if (parts.length < 6) return null;

    const dimId = parseInt(parts.pop());
    const z = parseInt(parts.pop());
    const y = parseInt(parts.pop());
    const x = parseInt(parts.pop());
    const homeNumber = parseInt(parts.pop());
    const playerName = parts.join("_");

    if (isNaN(homeNumber) || isNaN(x) || isNaN(y) || isNaN(z) || isNaN(dimId)) {
        return null;
    }

    return { playerName, homeNumber, x, y, z, dimId };
}

/**
 * Finds the scoreboard participant for a given player's nth home.
 */
function findHomeEntry(tpObjective, playerName, homeNumber) {
    return tpObjective.getParticipants().find((p) => {
        const data = parseHomeEntry(p.displayName);
        return data && data.playerName === playerName && data.homeNumber === homeNumber;
    });
}

/**
 * Finds the nickname scoreboard entry for a given home entry name.
 * Nickname entries look like: "<homeEntry>_nickname_<nickname>"
 */
function findNicknameEntry(tpObjective, homeEntry) {
    return tpObjective
        .getParticipants()
        .find((p) => p.displayName.startsWith(`${homeEntry}_nickname_`));
}

/**
 * Extracts the nickname from something like "<homeEntry>_nickname_<nickname>".
 */
function parseNicknameEntryName(entryName) {
    const idx = entryName.indexOf("_nickname_");
    if (idx === -1) return null;
    return entryName.substring(idx + "_nickname_".length);
}

/**
 * Main TP Home Menu (entry point)
 */
export function tpHome(player) {
    let tpObjective = world.scoreboard.getObjective("PlayerTP");
    let adminObjective = world.scoreboard.getObjective("admin");

    // Ensure the scoreboard exists before using it
    if (!tpObjective) {
        world.scoreboard.addObjective("PlayerTP", "Player Teleports");
        tpObjective = world.scoreboard.getObjective("PlayerTP");
    }
    if (!adminObjective) {
        world.scoreboard.addObjective("admin", "Admin Settings");
        adminObjective = world.scoreboard.getObjective("admin");
    }

    const playerName = player.name;

    // Count how many homes the player actually has by searching for scoreboard entries
    let playerHomeCount = 0;
    const homeEntries = tpObjective.getParticipants().filter(p => p.displayName.startsWith(`${playerName}_`));

    homeEntries.forEach(entry => {
        const data = parseHomeEntry(entry.displayName);
        if (data && data.playerName === playerName) {
            playerHomeCount = Math.max(playerHomeCount, data.homeNumber);
        }
    });

    // Retrieve max homes allowed (from admin scoreboard "homecount")
    const maxHomesParticipant = adminObjective
        .getParticipants()
        .find(p => p.displayName === "homecount");
    const maxHomes = maxHomesParticipant
        ? adminObjective.getScore(maxHomesParticipant)
        : 0;

    const form = new ActionFormData().title("TP Home Menu");

    // Always add the Back button first
    form.button("Back");

    if (playerHomeCount === 0) {
        form
            .body("You have no saved homes. Add a new home to get started.")
            .button("Add Home");
    } else {
        // Loop through player's homes and display buttons for each
        for (let i = 1; i <= playerHomeCount; i++) {
            const homeEntry = tpObjective
                .getParticipants()
                .find(p => p.displayName.startsWith(`${playerName}_${i}_`));

            if (!homeEntry) {
                form.button(`Home ${i} (Missing)`);
                continue;
            }

            const data = parseHomeEntry(homeEntry.displayName);
            if (!data) {
                form.button(`Home ${i} (Invalid Data)`);
                continue;
            }

            // Find nickname if it exists
            const nicknameEntry = tpObjective
                .getParticipants()
                .find(p => p.displayName.startsWith(`${homeEntry.displayName}_nickname_`));
            const nickname = nicknameEntry
                ? parseNicknameEntryName(nicknameEntry.displayName)
                : null;

            // Build the button label
            const coords = `${data.x}, ${data.y}, ${data.z}`;
            let buttonLabel = nickname
                ? `${nickname} (${coords})`
                : `Home ${i} (${coords})`;

            // If Nether, append red "N"
            // <-- CHANGED: If End, append "§0E" (black E)
            if (data.dimId === 1) {
                buttonLabel += " §cN"; // Nether
            } else if (data.dimId === 2) {
                buttonLabel += " §0E"; // The End (black E)
            }

            form.button(buttonLabel);
        }

        // Show "Add Home" if current homes < maxHomes
        if (playerHomeCount < maxHomes) {
            form.button("Add Home");
        }
    }

    form.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0: // Back button
                playerTpmenu(player);
                break;
            case playerHomeCount + 1: // Add Home
                showAddHomeMenu(player);
                break;
            default:
                if (response.selection > 0 && response.selection <= playerHomeCount) {
                    showHomeOptions(player, response.selection);
                } else {
                    player.sendMessage("Invalid selection.");
                }
                break;
        }
    });
}

/**
 * Menu to confirm buying (or adding) a new home
 */
function showAddHomeMenu(player) {
    const tpObjective = world.scoreboard.getObjective("PlayerTP");
    if (!tpObjective) {
        player.sendMessage("§cPlayerTP scoreboard is missing.");
        return;
    }

    const costParticipant = tpObjective
        .getParticipants()
        .find((p) => p.displayName === "cost");
    const cost = costParticipant ? tpObjective.getScore(costParticipant) : 0;

    // This might be unused if you rely on "homecount" in "admin"
    const maxHomesParticipant = tpObjective
        .getParticipants()
        .find((p) => p.displayName === "playerhomes");
    const maxHomes = maxHomesParticipant ? tpObjective.getScore(maxHomesParticipant) : 0;

    const form = new MessageFormData()
        .title("Add Home")
        .body(`This home will cost $${cost}. You can set up to ${maxHomes} homes. Proceed?`)
        .button1("Cancel")
        .button2(cost === 0 ? "Add Home (Free)" : "Buy");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            tpHome(player);
            return;
        }

        if (response.selection === 1) {
            const moneyObjective = world.scoreboard.getObjective("Money");
            if (!moneyObjective) {
                player.sendMessage("§cThe Money scoreboard does not exist.");
                return;
            }

            const playerMoney = moneyObjective.getScore(player);

            if (cost > 0 && playerMoney < cost) {
                player.sendMessage(
                    `§cYou don't have enough money to purchase a home slot. You need $${cost}.`
                );
                showAddHomeMenu(player);
                return;
            }

            if (cost > 0) {
                player.runCommandAsync(`scoreboard players remove "${player.name}" Money ${cost}`)
                    .then(() => addHomeSlot(player, tpObjective))
                    .catch((error) => {
                        player.sendMessage("§cAn error occurred while deducting money.");
                        console.error(error);
                    });
            } else {
                addHomeSlot(player, tpObjective);
            }
        }
    });
}

/**
 * Actually adds the home slot to the scoreboard and increments the player's home count
 */
function addHomeSlot(player, tpObjective) {
    const { x, y, z } = player.location;
    const dimensionId = getDimensionId(player.dimension.id);

    player.runCommandAsync(`scoreboard players add "${player.name}" PlayerTP 1`)
        .then(() => {
            const newHomeNumber = tpObjective.getScore(player);

            const homeEntry = buildHomeEntry(
                player.name,
                newHomeNumber,
                Math.floor(x),
                Math.floor(y),
                Math.floor(z),
                dimensionId
            );

            return player.runCommandAsync(`scoreboard players set "${homeEntry}" PlayerTP 1`);
        })
        .then(() => {
            player.sendMessage(`§aHome added at your current location!`);
            tpHome(player);
        })
        .catch((error) => {
            player.sendMessage("§cAn error occurred while adding the home slot.");
            console.error(error);
        });
}

/**
 * Shows the menu of actions for an existing home (TP, set coords, set nickname, remove, back).
 */
function showHomeOptions(player, homeNumber) {
    const form = new ActionFormData()
        .title(`Home ${homeNumber} Options`)
        .body("Manage your home:")
        .button("TP to Home")
        .button("Set Current Coordinates")
        .button("Set Nickname")
        .button("Remove Home")
        .button("Back");

    form.show(player).then((response) => {
        if (response.canceled) return;

        switch (response.selection) {
            case 0:
                teleportToHome(player, homeNumber);
                break;
            case 1:
                confirmSetCoordinates(player, homeNumber);
                break;
            case 2:
                showSetNicknameForm(player, homeNumber);
                break;
            case 3:
                confirmRemoveHome(player, homeNumber);
                break;
            case 4:
                tpHome(player);
                break;
        }
    });
}

/**
 * Teleports the player to the chosen home, deducting "Tppercost" if necessary.
 */
function teleportToHome(player, homeNumber) {
    const tpObjective = world.scoreboard.getObjective("PlayerTP");
    if (!tpObjective) {
        player.sendMessage("§cPlayerTP scoreboard does not exist.");
        return;
    }

    const costParticipant = tpObjective
        .getParticipants()
        .find((p) => p.displayName === "Tppercost");
    const cost = costParticipant ? tpObjective.getScore(costParticipant) : 0;

    const moneyObjective = world.scoreboard.getObjective("Money");
    if (!moneyObjective) {
        player.sendMessage("§cMoney scoreboard does not exist.");
        return;
    }

    const playerMoney = moneyObjective.getScore(player);
    if (cost > 0 && playerMoney < cost) {
        player.sendMessage(`§cYou don't have enough money to teleport home. You need $${cost}.`);
        return;
    }

    const participant = findHomeEntry(tpObjective, player.name, homeNumber);
    if (!participant) {
        player.sendMessage(`§cNo data found for Home #${homeNumber}.`);
        return;
    }

    const data = parseHomeEntry(participant.displayName);
    if (!data) {
        player.sendMessage("§cInvalid home data (could not parse).");
        return;
    }

    // Convert dimension ID to short name for /execute in <dimension> ...
    const bedrockDimension = getBedrockDimensionName(data.dimId);

    if (cost > 0) {
        player.runCommandAsync(`scoreboard players remove "${player.name}" Money ${cost}`)
            .then(() => {
                player.runCommandAsync(
                    `execute in ${bedrockDimension} run tp @s ${data.x} ${data.y} ${data.z}`
                )
                    .then(() => {
                        player.sendMessage(
                            `Teleported to Home #${homeNumber} at [${data.x}, ${data.y}, ${data.z}] in ${bedrockDimension}. ($${cost} deducted)`
                        );
                    })
                    .catch((error) => {
                        player.sendMessage("Failed to teleport to home.");
                        console.error(error);
                    });
            })
            .catch((error) => {
                player.sendMessage("Failed to deduct money for teleportation.");
                console.error(error);
            });
    } else {
        player.runCommandAsync(
            `execute in ${bedrockDimension} run tp @s ${data.x} ${data.y} ${data.z}`
        )
            .then(() => {
                player.sendMessage(
                    `Teleported to Home #${homeNumber} at [${data.x}, ${data.y}, ${data.z}] in ${bedrockDimension}. (Free)`
                );
            })
            .catch((error) => {
                player.sendMessage("Failed to teleport to home.");
                console.error(error);
            });
    }
}

/**
 * Prompts the player to confirm setting a home at their current location,
 * including dimension info (Overworld, Nether, The End).
 */
function confirmSetCoordinates(player, homeNumber) {
    const coords = player.location;
    const dimensionId = getDimensionId(player.dimension.id);
    const dimensionName = getDimensionPrettyName(dimensionId);

    const form = new MessageFormData()
        .title(`Set Home #${homeNumber} Coordinates`)
        .body(
            `Your current location:\n` +
            `X: ${coords.x.toFixed(1)}, Y: ${coords.y.toFixed(1)}, Z: ${coords.z.toFixed(1)}\n` +
            `Dimension: §e${dimensionName}§r\n\n` +
            `Are you sure you want to set Home #${homeNumber} here?`
        )
        .button1("No")
        .button2("Yes");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 0) return;

        if (response.selection === 1) {
            saveHomeCoordinates(player, homeNumber, coords, dimensionId, () => {
                player.sendMessage(`§aHome #${homeNumber} set successfully in §e${dimensionName}§a!`);
                showHomeOptions(player, homeNumber);
            });
        }
    });
}

/**
 * Saves new home coordinates and updates the dimension if needed.
 */
async function saveHomeCoordinates(player, homeNumber, coords, dimensionId, callback) {
    const tpObjective = world.scoreboard.getObjective("PlayerTP");
    if (!tpObjective) {
        player.sendMessage("§cPlayerTP scoreboard does not exist.");
        return;
    }

    const spawnPoint = await getSpawnPoint();
    if (!spawnPoint) {
        player.sendMessage("§cNo spawn point set. Please use the Set Spawn function first.");
        return;
    }

    const minSpawnDistance = 1000;
    const minZeroAxisDistance = 250;

    // Only check spawn distance if the player's dimension matches the spawn dimension
    if (dimensionId === getDimensionId(spawnPoint.dimension)) {
        const distanceFromSpawn = Math.sqrt(
            (coords.x - spawnPoint.x) ** 2 + (coords.z - spawnPoint.z) ** 2
        );

        if (distanceFromSpawn < minSpawnDistance) {
            player.sendMessage(
                `§cYour home is too close to spawn.\n` +
                `You must be at least ${minSpawnDistance} blocks away.\n` +
                `Current distance: ${Math.round(distanceFromSpawn)} blocks.`
            );
            return;
        }
    }

    // Check distance from (0,0) in all dimensions
    if (Math.abs(coords.x) < minZeroAxisDistance || Math.abs(coords.z) < minZeroAxisDistance) {
        player.sendMessage(
            `§cYour home is too close to the origin (0,0).\n` +
            `Minimum distance: ${minZeroAxisDistance} blocks.`
        );
        return;
    }

    const oldParticipant = findHomeEntry(tpObjective, player.name, homeNumber);
    if (!oldParticipant) {
        player.sendMessage(`§cNo scoreboard entry for Home #${homeNumber}.`);
        return;
    }

    const oldData = parseHomeEntry(oldParticipant.displayName);
    if (!oldData) {
        player.sendMessage("§cInvalid existing home data.");
        return;
    }

    // Always update the dimension to match the player's current one
    const newDimId = dimensionId;

    const newEntry = buildHomeEntry(
        oldData.playerName,
        oldData.homeNumber,
        Math.floor(coords.x),
        Math.floor(coords.y),
        Math.floor(coords.z),
        newDimId
    );

    try {
        // Remove old scoreboard line
        await player.runCommandAsync(`scoreboard players reset "${oldParticipant.displayName}" PlayerTP`);

        // Create new line with updated coords + dimension
        await player.runCommandAsync(`scoreboard players set "${newEntry}" PlayerTP 1`);

        // If there's a nickname, update it
        const oldNicknameParticipant = findNicknameEntry(tpObjective, oldParticipant.displayName);
        if (oldNicknameParticipant) {
            const nickname = parseNicknameEntryName(oldNicknameParticipant.displayName);
            await player.runCommandAsync(`scoreboard players reset "${oldNicknameParticipant.displayName}" PlayerTP`);
            const newNicknameEntry = `${newEntry}_nickname_${nickname}`;
            await player.runCommandAsync(`scoreboard players set "${newNicknameEntry}" PlayerTP ${oldData.homeNumber}`);
        }

        if (callback) callback();
    } catch (error) {
        console.error(`Failed to save home coordinates: ${error}`);
        player.sendMessage("§cFailed to save home coordinates. Please try again.");
    }
}

/**
 * Confirm removing a home
 */
function confirmRemoveHome(player, homeNumber) {
    const form = new MessageFormData()
        .title(`Remove Home #${homeNumber}`)
        .body(
            "Are you sure you want to remove this home?\n" +
            "If homes cost money, you will need to buy this slot again."
        )
        .button1("Cancel")
        .button2("Remove");

    form.show(player).then((response) => {
        if (response.canceled || response.selection === 0) {
            return;
        }
        if (response.selection === 1) {
            removeHome(player, homeNumber);
        }
    });
}

/**
 * Remove the home scoreboard entry (and nickname), then decrement home count
 */
function removeHome(player, homeNumber) {
    const tpObjective = world.scoreboard.getObjective("PlayerTP");
    if (!tpObjective) {
        player.sendMessage("§cPlayerTP scoreboard does not exist.");
        return;
    }

    const homeParticipant = findHomeEntry(tpObjective, player.name, homeNumber);
    if (!homeParticipant) {
        player.sendMessage(`§cNo data found for Home #${homeNumber}.`);
        return;
    }

    player.runCommandAsync(`scoreboard players reset "${homeParticipant.displayName}" PlayerTP`)
        .then(async () => {
            // Remove nickname if any
            const nicknameParticipant = findNicknameEntry(tpObjective, homeParticipant.displayName);
            if (nicknameParticipant) {
                await player.runCommandAsync(
                    `scoreboard players reset "${nicknameParticipant.displayName}" PlayerTP`
                );
            }

            // Decrease player's home count
            await player.runCommandAsync(
                `scoreboard players remove "${player.name}" PlayerTP 1`
            );

            player.sendMessage(`§aHome #${homeNumber} removed successfully.`);
            tpHome(player);
        })
        .catch((error) => {
            console.error(`Failed to remove home: ${error}`);
            player.sendMessage("§cAn error occurred while removing the home.");
        });
}

/**
 * Nickname creation for a home entry
 */
function showSetNicknameForm(player, homeNumber) {
    const tpObjective = world.scoreboard.getObjective("PlayerTP");
    if (!tpObjective) {
        player.sendMessage("§cPlayerTP scoreboard does not exist.");
        return;
    }

    const homeParticipant = findHomeEntry(tpObjective, player.name, homeNumber);
    if (!homeParticipant) {
        player.sendMessage(`§cNo data found for Home #${homeNumber}.`);
        return;
    }

    const form = new ModalFormData()
        .title(`Set Nickname for Home #${homeNumber}`)
        .textField("Enter a new nickname:", "Nickname");

    form.show(player).then((response) => {
        if (response.canceled) return;

        const newNickname = response.formValues[0].trim();
        if (!newNickname) {
            player.sendMessage("Nickname cannot be empty. Please try again.");
            showSetNicknameForm(player, homeNumber);
            return;
        }

        try {
            // Remove any existing nickname
            const oldNicknameParticipant = findNicknameEntry(tpObjective, homeParticipant.displayName);
            if (oldNicknameParticipant) {
                player.runCommandAsync(
                    `scoreboard players reset "${oldNicknameParticipant.displayName}" PlayerTP`
                );
            }

            const newNicknameEntry = `${homeParticipant.displayName}_nickname_${newNickname}`;
            player.runCommandAsync(
                `scoreboard players set "${newNicknameEntry}" PlayerTP ${homeNumber}`
            );
            player.sendMessage(`Nickname for Home #${homeNumber} set to: ${newNickname}`);
        } catch (error) {
            console.error(`Failed to set nickname: ${error}`);
        }

        showHomeOptions(player, homeNumber);
    });
}

/**
 * Retrieve the spawn point from scoreboard "setspawn"
 */
async function getSpawnPoint() {
    try {
        const scoreboard = world.scoreboard.getObjective("setspawn");
        if (!scoreboard) {
            console.log("No 'setspawn' scoreboard found.");
            return null;
        }

        const spawnEntry = scoreboard
            .getParticipants()
            .find((p) => p.displayName.startsWith("global_spawn"));
        if (!spawnEntry) {
            console.log("No valid global spawn point found.");
            return null;
        }

        const match = spawnEntry.displayName.match(/^global_spawn_(\w+)_(-?\d+)_(-?\d+)_(-?\d+)$/);
        if (!match) {
            console.log("Invalid global spawn point format.");
            return null;
        }

        const [, dimension, x, y, z] = match;
        return {
            dimension,
            x: parseInt(x, 10),
            y: parseInt(y, 10),
            z: parseInt(z, 10),
        };
    } catch (error) {
        console.error("Error retrieving spawn point:", error);
        return null;
    }
}
