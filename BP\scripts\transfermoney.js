import { world } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";

export function transferMoney(player) {
    const onlinePlayers = Array.from(world.getPlayers());

    const actionForm = new ActionFormData()
        .title("Transfer Money")
        .body("Select a player to transfer money to:")
        .button("Back");

    onlinePlayers.forEach((onlinePlayer) => {
        actionForm.button(onlinePlayer.name);
    });

    actionForm.show(player).then((actionResponse) => {
        if (!actionResponse.canceled) {
            const selectedIndex = actionResponse.selection;

            if (selectedIndex === 0) {
                // Back button was clicked
                return;
            }

            const selectedPlayer = onlinePlayers[selectedIndex - 1];

            const modalForm = new ModalFormData()
                .title(`Transfer to "${selectedPlayer.name}"`)
                .textField("Enter amount to transfer:", "Amount", "0");

            modalForm.show(player).then((modalResponse) => {
                if (!modalResponse.canceled) {
                    const amount = parseInt(modalResponse.formValues[0]);

                    if (isNaN(amount) || amount <= 0) {
                        player.sendMessage("Invalid amount. Please enter a valid number.");
                        return;
                    }

                    // Access the scoreboard and handle the transaction
                    const moneyObjective = world.scoreboard.getObjective("Money");
                    if (!moneyObjective) {
                        player.sendMessage("Money scoreboard objective not found.");
                        return;
                    }

                    // Get sender's current score
                    const playerScore = moneyObjective.getScore(player);

                    if (playerScore >= amount) {
                        // Deduct from sender
                        moneyObjective.setScore(player, playerScore - amount);

                        // Get recipient's current score
                        const recipientScore = moneyObjective.getScore(selectedPlayer) || 0;
                        moneyObjective.setScore(selectedPlayer, recipientScore + amount);

                        player.sendMessage(`Successfully transferred ${amount} Money to "${selectedPlayer.name}".`);
                        selectedPlayer.sendMessage(`${player.name} has transferred ${amount} Money to you.`);
                    } else {
                        player.sendMessage("You do not have enough Money to complete this transfer.");
                    }
                }
            }).catch((error) => {
                console.error("Error displaying modal form:", error);
            });
        }
    }).catch((error) => {
        console.error("Error displaying action form:", error);
    });
}
