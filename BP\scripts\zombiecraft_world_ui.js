import { world, system } from "@minecraft/server";
import { MainMenu } from "./menu_index";
import { immediatePlayerOfflineCommandLeave } from "./player_offline";
import { checkPlayerProximityAndBanList } from "./proximityBanCheck";

world.beforeEvents.worldInitialize.subscribe((initEvent) => {
    initEvent.itemComponentRegistry.registerCustomComponent("zombie:menu", new MainMenu());
});

const intervalTicks = 30; // Interval in ticks (10 ticks = 0.5 seconds)

// Synchronize `manas` with `mana` for all players on a regular interval
system.runInterval(async () => {
    try {
        await world.getDimension("overworld").runCommandAsync(
            "execute as @a run scoreboard players operation @s MoneyDisplay = @s Money"
        );
    } catch (error) {
        console.error("Error syncing scoreboard:", error);
    }
}, intervalTicks);

world.beforeEvents.playerLeave.subscribe(() => {
    immediatePlayerOfflineCommandLeave();
})

world.afterEvents.playerJoin.subscribe(() => {
    // Run the immediate command when the player joins
    immediatePlayerOfflineCommandLeave();

    // Set up a delay of 300 ticks (15 seconds) before running the command again
    system.runTimeout(() => {
        immediatePlayerOfflineCommandLeave();
    }, 300);
});

const checkIntervalTicks = 100;
system.runInterval(() => {
    checkPlayerProximityAndBanList();
}, checkIntervalTicks);
