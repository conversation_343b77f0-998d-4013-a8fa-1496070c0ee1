{"format_version": "1.12.0", "minecraft:entity": {"description": {"identifier": "minecraft:minecart", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"canopy:ticking_timer": {"minecraft:timer": {"looping": false, "time": 10, "time_down_event": {"event": "canopy:disable_ticking"}}}, "canopy:ticking": {"minecraft:tick_world": {"never_despawn": true, "radius": 2}}}, "components": {"minecraft:is_stackable": {}, "minecraft:type_family": {"family": ["minecart", "inanimate"]}, "minecraft:collision_box": {"width": 0.98, "height": 0.7}, "minecraft:rail_movement": {}, "minecraft:rideable": {"seat_count": 1, "interact_text": "action.interact.ride.minecart", "pull_in_entities": true, "seats": {"position": [0.0, -0.2, 0.0]}}, "minecraft:rail_sensor": {"eject_on_activate": true}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 60.0, "max_dropped_ticks": 20, "use_motion_prediction_hints": true}, "conditional_values": [{"max_optimized_distance": 0.0, "max_dropped_ticks": 0, "conditional_values": [{"test": "is_moving", "subject": "self", "operator": "==", "value": true}]}]}}, "events": {"canopy:tick_tenSeconds": {"add": {"component_groups": ["canopy:ticking", "canopy:ticking_timer"]}}, "canopy:disable_ticking": {"remove": {"component_groups": ["canopy:ticking", "canopy:ticking_timer"]}}}}}