{
  "format_version": "1.12.0",
  "minecraft:entity": {
    "description": {
      "identifier": "canopy:rideable",
      "is_summonable": true,
      "is_spawnable": false,
      "is_experimental": false
    },

    "components": {
      "minecraft:physics": {
        "has_collision": false,
        "has_gravity": false
      },
      "minecraft:custom_hit_test": {
        "hitboxes": [
          {
            "pivot": [0, 100, 0],
            "width": 0,
            "height": 0
          }
        ]
      },
      "minecraft:damage_sensor": {
        "triggers": {
          "deals_damage": false
        }
      },
      "minecraft:collision_box": {
        "width": 0.001,
        "height": 0.001
      },
      "minecraft:health": {
        "value": 1,
        "max": 1,
        "min": 1
      },
      "minecraft:pushable": {
        "is_pushable": false,
        "is_pushable_by_piston": false
      },
      "minecraft:nameable": {
        "allow_name_tag_renaming": false
      },
      "minecraft:breathable": {
        "breathes_air": true,
        "breathes_water": true
      },
      "minecraft:rideable": {
        "seat_count": 1,
        "family_types": ["player"],
        "pull_in_entities": false,
        "seats": {
          "position": [ 0.0, 0.0, 0.0 ]
        }
        // "dismount_mode": "on_top_center"
      }
    }
  }
}
  