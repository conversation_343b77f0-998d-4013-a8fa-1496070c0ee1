{"format_version": "1.12.0", "minecraft:entity": {"description": {"identifier": "minecraft:tnt", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"from_explosion": {"minecraft:explode": {"fuse_length": {"range_min": 0.5, "range_max": 2.0}, "fuse_lit": true, "power": 4, "causes_fire": false}}, "canopy:fuse": {"minecraft:explode": {"fuse_length": 3600, "fuse_lit": true, "power": 4, "causes_fire": false}}, "canopy:explode": {"minecraft:explode": {"fuse_length": 0, "fuse_lit": true, "power": 4, "causes_fire": false}}}, "components": {"minecraft:type_family": {"family": ["tnt", "inanimate"]}, "minecraft:collision_box": {"width": 0.98, "height": 0.98}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80.0, "max_dropped_ticks": 5, "use_motion_prediction_hints": true}}}, "events": {"from_explosion": {"add": {"component_groups": ["from_explosion"]}}, "canopy:fuse": {"add": {"component_groups": ["canopy:fuse"]}}, "canopy:explode": {"add": {"component_groups": ["canopy:explode"]}}}}}