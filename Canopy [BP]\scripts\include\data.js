export const categoryToMobMap = {
    'animal' : [
        'armadillo',
        'bee',
        'camel',
        'chicken',
        'cow',
        'dolphin',
        'donkey',
        'fox',
        'frog',
        'glow_squid',
        'goat',
        'hoglin',
        'horse',
        'llama',
        'mooshroom',
        'ocelot',
        'panda',
        'parrot',
        'pig',
        'polar_bear',
        'rabbit',
        'sheep',
        'squid',
        'strider',
        'turtle',
        'wolf'
    ],
    'ambient' : [ 'bat' ],
    'cat' : [ 'cat' ],
    'monster' : [
        'blaze',
        'bogged',
        'creeper',
        'drowned',
        'enderman',
        'ghast',
        'guardian',
        'husk',
        'magma_cube',
        'phantom',
        'piglin',
        'skeleton',
        'slime',
        'spider',
        'stray',
        'witch',
        'wither_skeleton',
        'zombie_pigman',
        'zombie'
    ],
    'pillager' : [
        'pillager'
    ],
    'water_animal' : [
        'axolotl',
        'pufferfish',
        'salmon',
        'tropicalfish'
    ],
    'none' : [
        'allay',
        'breeze',
        'cave_spider',
        'creaking',
        'elder_guardian',
        'ender_dragon',
        'endermite',
        'evocation_illager',
        'fish',
        'iron_golem',
        'lightning_bolt',
        'llama_spit',
        'mule',
        'npc',
        'piglin_brute',
        'player',
        'ravager',
        'shulker',
        'silverfish',
        'skeleton_horse',
        'sniffer',
        'snow_golem',
        'tadpole',
        'trader_llama',
        'vex',
        'villager',
        'villager_v2',
        'vindicator',
        'wandering_trader',
        'warden',
        'wither',
        'zoglin',
        'zombie_horse',
        'zombie_villager',
        'zombie_villager_v2'
    ]
}

export const intToBiomeMap = {
    0: 'Bamboo Jungle',
    1: 'Bamboo Jungle Hills',
    2: 'Basalt Deltas',
    3: 'Beach',
    4: 'Birch Forest',
    5: 'Birch Forest Hills',
    6: 'Birch Forest Hills M',
    7: 'Birch Forest M',
    8: 'Cherry Grove',
    9: 'Cold Beach',
    10: 'Cold Ocean',
    11: 'Cold Taiga',
    12: 'Cold Taiga Hills',
    13: 'Cold Taiga M',
    14: 'Crimson Forest',
    15: 'Deep Cold Ocean',
    16: 'Deep Dark',
    17: 'Deep Frozen Ocean',
    18: 'Deep Lukewarm Ocean',
    19: 'Deep Ocean',
    20: 'Deep Warm Ocean',
    21: 'Desert',
    22: 'Desert Hills',
    23: 'Desert M',
    24: 'Dripstone Caves',
    25: 'Extreme Hills',
    26: 'Extreme Hills Edge',
    27: 'Extreme Hills M',
    28: 'Extreme Hills Plus Trees',
    29: 'Extreme Hills Plus Trees M',
    30: 'Flower Forest',
    31: 'Forest',
    32: 'Forest Hills',
    33: 'Frozen Ocean',
    34: 'Frozen Peaks',
    35: 'Frozen River',
    36: 'Grove',
    37: 'Hell',
    38: 'Ice Mountains',
    39: 'Ice Plains',
    40: 'Ice Plains Spikes',
    41: 'Jagged Peaks',
    42: 'Jungle',
    43: 'Jungle Edge',
    44: 'Jungle Edge M',
    45: 'Jungle Hills',
    46: 'Jungle M',
    47: 'Legacy Frozen Ocean',
    48: 'Lukewarm Ocean',
    49: 'Lush Caves',
    50: 'Mangrove Swamp',
    51: 'Meadow',
    52: 'Mega Taiga',
    53: 'Mega Taiga Hills',
    54: 'Mesa',
    55: 'Mesa Bryce',
    56: 'Mesa Plateau',
    57: 'Mesa Plateau M',
    58: 'Mesa Plateau Stone',
    59: 'Mesa Plateau Stone M',
    60: 'Mushroom Island',
    61: 'Mushroom Island Shore',
    62: 'Ocean',
    63: 'Plains',
    64: 'Redwood Taiga Hills M',
    65: 'Redwood Taiga M',
    66: 'River',
    67: 'Roofed Forest',
    68: 'Roofed Forest M',
    69: 'Savanna',
    70: 'Savanna M',
    71: 'Savanna Plateau',
    72: 'Savanna Plateau M',
    73: 'Snowy Slopes',
    74: 'Soulsand Valley',
    75: 'Stone Beach',
    76: 'Stony Peaks',
    77: 'Sunflower Plains',
    78: 'Swampland',
    79: 'Swampland M',
    80: 'Taiga',
    81: 'Taiga Hills',
    82: 'Taiga M',
    83: 'The End',
    84: 'Warm Ocean',
    85: 'Warped Forest',
    86: 'Pale Garden'
};