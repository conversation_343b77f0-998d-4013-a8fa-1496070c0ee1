// Commands
import './src/commands/info'
import './src/commands/help'
import './src/commands/peek'
import './src/commands/jump'
import './src/commands/warp'
import './src/commands/gamemode'
import './src/commands/camera'
import './src/commands/canopy'
import './src/commands/distance'
import './src/commands/log'
import './src/commands/entitydensity'
import './src/commands/health'
import './src/commands/counter'
import './src/commands/resetall'
import './src/commands/data'
import './src/commands/tick'
import './src/commands/changedimension'
import './src/commands/spawn'
import './src/commands/claimprojectiles'
import './src/commands/trackevent'
import './src/commands/tntfuse'
import './src/commands/removeentity'
import './src/commands/pos'
import './src/commands/cleanup'
import './src/commands/sit'
import './src/commands/generator'
import './src/commands/resettest'
import './src/commands/simmap'
import './src/commands/loop'

// Script Events
import './src/commands/scriptevents/counter'
import './src/commands/scriptevents/spawn'
import './src/commands/scriptevents/tick'
import './src/commands/scriptevents/generator'
import './src/commands/scriptevents/resettest'
import './src/commands/scriptevents/loop'

// Rules
import './src/rules/infodisplay/InfoDisplay'
import './src/rules/explosionNoBlockDamage'
import './src/rules/autoItemPickup'
import './src/rules/universalChunkLoading'
import './src/rules/creativeNoTileDrops'
import './src/rules/flippinArrows'
import './src/rules/tntPrimeNoMomentum'
import './src/rules/tntPrimeMaxMomentum'
import './src/rules/dupeTnt'
import './src/rules/pistonBedrockBreaking'
import './src/rules/hotbarSwitching'
import './src/rules/renewableSponge'
import './src/rules/armorStandRespawning'
import './src/rules/explosionOff'
import './src/rules/explosionChainReactionOnly'
import './src/rules/creativeInstantTame'
import './src/rules/entityInstantDeath'
import './src/rules/renewableElytra'
import './src/rules/instaminableDeepslate'
import './src/rules/instaminableEndstone'
import './src/rules/quickFillContainer'
import './src/rules/durabilityNotifier'
import './src/rules/allowBubbleColumnPlacement'
import './src/rules/cauldronConcreteConversion'
import './src/rules/creativeOneHitKill'
import './src/rules/playerSit'
import './src/rules/refillHand'
import './src/rules/durabilitySwap'
import './src/rules/allowPeekInventory'
import './src/rules/commandTntFuse'

// Load Time Processes
import './src/onStart'
import './src/onReload'
