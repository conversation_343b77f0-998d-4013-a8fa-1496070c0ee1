import { Command } from '../../lib/canopy/Canopy';

const gamemodeMap = {
    's': 'Survival',
    'a': 'Adventure',
    'c': 'Creative',
    'sp': 'Spectator'
};

for (const key in gamemodeMap) {
    new Command({
        name: key,
        description: { translate: `commands.gamemode.${key}` },
        usage: key,
        callback: (sender) => sender.runCommand(`gamemode ${gamemodeMap[key]}`),
        opOnly: true
    });
}
