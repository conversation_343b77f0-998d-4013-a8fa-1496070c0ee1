{"format_version": "1.21.50", "minecraft:block": {"description": {"identifier": "ninjos:coin_wood", "menu_category": {"category": "items"}, "traits": {"minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}}}, "components": {"minecraft:collision_box": {"origin": [-4, 0, -4], "size": [8, 16, 8]}, "minecraft:selection_box": {"origin": [-4, 0, -4], "size": [8, 16, 8]}, "minecraft:destructible_by_mining": {"seconds_to_destroy": 0.4}, "minecraft:destructible_by_explosion": {"explosion_resistance": 200}, "minecraft:geometry": "geometry.coin_netherite", "minecraft:material_instances": {"*": {"texture": "coin_wood", "render_method": "alpha_test", "face_dimming": false, "ambient_occlusion": false}}, "minecraft:light_emission": 15, "minecraft:map_color": "#493a00"}, "permutations": [{"condition": "q.block_state('minecraft:cardinal_direction') == 'north' ", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'south' ", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'east' ", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}}}, {"condition": "q.block_state('minecraft:cardinal_direction') == 'west' ", "components": {"minecraft:transformation": {"rotation": [0, 270, 0]}}}]}}