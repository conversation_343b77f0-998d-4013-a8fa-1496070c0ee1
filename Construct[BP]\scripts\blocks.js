export const blocks = {
   "acacia_button" : {
      "sound" : "wood",
      "textures" : "acacia_planks"
   },
   "acacia_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "acacia_double_slab" : {
      "sound" : "wood",
      "textures" : "acacia_planks"
   },
   "acacia_fence" : {
      "sound" : "wood",
      "textures" : "acacia_planks"
   },
   "acacia_fence_gate" : {
      "sound" : "wood",
      "textures" : "wood_acacia"
   },
   "acacia_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "acacia_sign"
   },
   "acacia_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "acacia_leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "acacia_leaves"
   },
   "acacia_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "acacia_log_top",
         "side" : "acacia_log_side",
         "up" : "acacia_log_top"
      }
   },
   "acacia_pressure_plate" : {
      "sound" : "wood",
      "textures" : "acacia_planks"
   },
   "acacia_sapling" : {
      "sound" : "grass",
      "textures" : "acacia_sapling"
   },
   "acacia_slab" : {
      "sound" : "wood",
      "textures" : "acacia_planks"
   },
   "acacia_stairs" : {
      "sound" : "wood",
      "textures" : "wood_acacia"
   },
   "acacia_standing_sign" : {
      "sound" : "wood",
      "textures" : "acacia_sign"
   },
   "acacia_trapdoor" : {
      "sound" : "wood",
      "textures" : "acacia_trapdoor"
   },
   "acacia_wall_sign" : {
      "sound" : "wood",
      "textures" : "acacia_sign"
   },
   "acacia_wood" : {
      "carried_textures" : "acacia_wood",
      "sound" : "wood",
      "textures" : "acacia_wood"
   },
   "activator_rail" : {
      "sound" : "metal",
      "textures" : {
         "down" : "rail_activator",
         "side" : "rail_activator",
         "up" : "rail_activator_powered"
      }
   },
   "air" : {},
   "allium" : {
      "sound" : "grass",
      "textures" : "allium"
   },
   "allow" : {
      "sound" : "stone",
      "textures" : "build_allow"
   },
   "amethyst_block" : {
      "sound" : "amethyst_block",
      "textures" : "amethyst_block"
   },
   "amethyst_cluster" : {
      "sound" : "amethyst_cluster",
      "textures" : "amethyst_cluster"
   },
   "ancient_debris" : {
      "sound" : "ancient_debris",
      "textures" : {
         "down" : "ancient_debris_top",
         "east" : "ancient_debris_side",
         "north" : "ancient_debris_side",
         "south" : "ancient_debris_side",
         "up" : "ancient_debris_top",
         "west" : "ancient_debris_side"
      }
   },
   "andesite" : {
      "sound" : "stone",
      "textures" : "andesite"
   },
   "andesite_double_slab" : {
      "sound" : "stone",
      "textures" : "andesite_slab"
   },
   "andesite_slab" : {
      "sound" : "stone",
      "textures" : "andesite_slab"
   },
   "andesite_stairs" : {
      "sound" : "stone",
      "textures" : "andesite"
   },
   "andesite_wall" : {
      "sound" : "stone",
      "textures" : "andesite_wall"
   },
   "anvil" : {
      "sound" : "anvil",
      "textures" : {
         "down" : "flattened_anvil_base",
         "side" : "flattened_anvil_base",
         "up" : "flattened_anvil_top"
      }
   },
   "azalea" : {
      "sound" : "azalea",
      "textures" : {
         "down" : "potted_azalea_bush_top",
         "east" : "azalea_plant",
         "north" : "azalea_side",
         "side" : "azalea_side",
         "south" : "potted_azalea_bush_side",
         "up" : "azalea_top",
         "west" : "potted_azalea_bush_plant"
      }
   },
   "azalea_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "azalea_leaves",
      "textures" : "azalea_leaves"
   },
   "azalea_leaves_flowered" : {
      "ambient_occlusion_exponent" : 0.80,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "azalea_leaves",
      "textures" : "azalea_leaves_flowered"
   },
   "azure_bluet" : {
      "sound" : "grass",
      "textures" : "azure_bluet"
   },
   "bamboo" : {
      "carried_textures" : "bamboo_carried",
      "sound" : "bamboo",
      "textures" : {
         "down" : "bamboo_sapling",
         "east" : "bamboo_singleleaf",
         "north" : "bamboo_stem",
         "south" : "bamboo_small_leaf",
         "up" : "bamboo_leaf",
         "west" : "bamboo_stem"
      }
   },
   "bamboo_block" : {
      "sound" : "bamboo_wood",
      "textures" : {
         "down" : "bamboo_block_top",
         "side" : "bamboo_block",
         "up" : "bamboo_block_top"
      }
   },
   "bamboo_button" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_planks"
   },
   "bamboo_door" : {
      "sound" : "bamboo_wood",
      "textures" : {
         "down" : "bamboo_door_bottom",
         "side" : "bamboo_door_top",
         "up" : "bamboo_door_bottom"
      }
   },
   "bamboo_double_slab" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_planks"
   },
   "bamboo_fence" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_fence"
   },
   "bamboo_fence_gate" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_fence_gate"
   },
   "bamboo_hanging_sign" : {
      "sound" : "bamboo_wood_hanging_sign",
      "textures" : "bamboo_sign"
   },
   "bamboo_mosaic" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_mosaic"
   },
   "bamboo_mosaic_double_slab" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_mosaic"
   },
   "bamboo_mosaic_slab" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_mosaic"
   },
   "bamboo_mosaic_stairs" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_mosaic"
   },
   "bamboo_planks" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_planks"
   },
   "bamboo_pressure_plate" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_planks"
   },
   "bamboo_sapling" : {
      "carried_textures" : "bamboo_carried",
      "sound" : "bamboo_sapling",
      "textures" : "bamboo_sapling"
   },
   "bamboo_slab" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_planks"
   },
   "bamboo_stairs" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_planks"
   },
   "bamboo_standing_sign" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_sign"
   },
   "bamboo_trapdoor" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_trapdoor"
   },
   "bamboo_wall_sign" : {
      "sound" : "bamboo_wood",
      "textures" : "bamboo_sign"
   },
   "barrel" : {
      "sound" : "wood",
      "textures" : {
         "down" : "barrel_side",
         "east" : "barrel_side",
         "north" : "barrel_top",
         "south" : "barrel_bottom",
         "up" : "barrel_side",
         "west" : "barrel_side"
      }
   },
   "barrier" : {
      "textures" : "barrier"
   },
   "basalt" : {
      "sound" : "basalt",
      "textures" : {
         "down" : "basalt_top",
         "side" : "basalt_side",
         "up" : "basalt_top"
      }
   },
   "beacon" : {
      "sound" : "glass",
      "textures" : {
         "down" : "beacon_base",
         "side" : "beacon_shell",
         "up" : "beacon_core"
      }
   },
   "bed" : {
      "sound" : "wood",
      "textures" : "bed_bottom"
   },
   "bedrock" : {
      "sound" : "stone",
      "textures" : "bedrock"
   },
   "bee_nest" : {
      "textures" : {
         "down" : "bee_nest_bottom",
         "east" : "bee_nest_side",
         "north" : "bee_nest_side",
         "south" : "bee_nest_front",
         "up" : "bee_nest_top",
         "west" : "bee_nest_side"
      }
   },
   "beehive" : {
      "textures" : {
         "down" : "beehive_top",
         "east" : "beehive_side",
         "north" : "beehive_side",
         "south" : "beehive_front",
         "up" : "beehive_top",
         "west" : "beehive_side"
      }
   },
   "beetroot" : {
      "sound" : "wood",
      "textures" : "beetroot"
   },
   "bell" : {
      "carried_textures" : "bell_carried",
      "sound" : "metal",
      "textures" : {
         "down" : "bell_bottom",
         "east" : "dark_oak_planks",
         "north" : "bell_side",
         "south" : "bell_side",
         "up" : "bell_top",
         "west" : "bell_stone"
      }
   },
   "big_dripleaf" : {
      "sound" : "big_dripleaf",
      "textures" : {
         "down" : "big_dripleaf_side1",
         "east" : "big_dripleaf_top",
         "north" : "big_dripleaf_stem",
         "south" : "big_dripleaf_top",
         "up" : "big_dripleaf_side2",
         "west" : "big_dripleaf_top"
      }
   },
   "birch_button" : {
      "sound" : "wood",
      "textures" : "birch_planks"
   },
   "birch_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "birch_double_slab" : {
      "sound" : "wood",
      "textures" : "birch_planks"
   },
   "birch_fence" : {
      "sound" : "wood",
      "textures" : "birch_planks"
   },
   "birch_fence_gate" : {
      "sound" : "wood",
      "textures" : "wood_birch"
   },
   "birch_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "birch_sign"
   },
   "birch_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "birch_leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "birch_leaves"
   },
   "birch_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "birch_log_top",
         "side" : "birch_log_side",
         "up" : "birch_log_top"
      }
   },
   "birch_pressure_plate" : {
      "sound" : "wood",
      "textures" : "birch_planks"
   },
   "birch_sapling" : {
      "sound" : "grass",
      "textures" : "birch_sapling"
   },
   "birch_slab" : {
      "sound" : "wood",
      "textures" : "birch_planks"
   },
   "birch_stairs" : {
      "sound" : "wood",
      "textures" : "wood_birch"
   },
   "birch_standing_sign" : {
      "sound" : "wood",
      "textures" : "birch_sign"
   },
   "birch_trapdoor" : {
      "sound" : "wood",
      "textures" : "birch_trapdoor"
   },
   "birch_wall_sign" : {
      "sound" : "wood",
      "textures" : "birch_sign"
   },
   "birch_wood" : {
      "carried_textures" : "birch_wood",
      "sound" : "wood",
      "textures" : "birch_wood"
   },
   "black_candle" : {
      "carried_textures" : "black_candle_carried",
      "sound" : "candle",
      "textures" : "black_candle"
   },
   "black_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "black_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_black"
   },
   "black_concrete" : {
      "sound" : "stone",
      "textures" : "black_concrete"
   },
   "black_concrete_powder" : {
      "sound" : "sand",
      "textures" : "black_concrete_powder"
   },
   "black_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "black_glazed_terracotta"
   },
   "black_shulker_box" : {
      "sound" : "stone",
      "textures" : "black_shulker_box"
   },
   "black_stained_glass" : {
      "sound" : "glass",
      "textures" : "black_stained_glass"
   },
   "black_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "black_stained_glass",
         "east" : "black_stained_glass_pane_top",
         "north" : "black_stained_glass",
         "south" : "black_stained_glass",
         "up" : "black_stained_glass",
         "west" : "black_stained_glass"
      }
   },
   "black_terracotta" : {
      "sound" : "terracotta",
      "textures" : "black_terracotta"
   },
   "black_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_black"
   },
   "blackstone" : {
      "sound" : "stone",
      "textures" : {
         "down" : "blackstone_top",
         "side" : "blackstone",
         "up" : "blackstone_top"
      }
   },
   "blackstone_double_slab" : {
      "sound" : "stone",
      "textures" : "blackstone"
   },
   "blackstone_slab" : {
      "sound" : "stone",
      "textures" : "blackstone"
   },
   "blackstone_stairs" : {
      "sound" : "stone",
      "textures" : "blackstone"
   },
   "blackstone_wall" : {
      "sound" : "stone",
      "textures" : "blackstone"
   },
   "blast_furnace" : {
      "sound" : "stone",
      "textures" : {
         "down" : "blast_furnace_top",
         "east" : "blast_furnace_side",
         "north" : "blast_furnace_side",
         "south" : "blast_furnace_front_off",
         "up" : "blast_furnace_top",
         "west" : "blast_furnace_side"
      }
   },
   "blue_candle" : {
      "carried_textures" : "blue_candle_carried",
      "sound" : "candle",
      "textures" : "blue_candle"
   },
   "blue_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "blue_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_blue"
   },
   "blue_concrete" : {
      "sound" : "stone",
      "textures" : "blue_concrete"
   },
   "blue_concrete_powder" : {
      "sound" : "sand",
      "textures" : "blue_concrete_powder"
   },
   "blue_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "blue_glazed_terracotta"
   },
   "blue_ice" : {
      "sound" : "glass",
      "textures" : "blue_ice"
   },
   "blue_orchid" : {
      "sound" : "grass",
      "textures" : "blue_orchid"
   },
   "blue_shulker_box" : {
      "sound" : "stone",
      "textures" : "blue_shulker_box"
   },
   "blue_stained_glass" : {
      "sound" : "glass",
      "textures" : "blue_stained_glass"
   },
   "blue_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "blue_stained_glass",
         "east" : "blue_stained_glass_pane_top",
         "north" : "blue_stained_glass",
         "south" : "blue_stained_glass",
         "up" : "blue_stained_glass",
         "west" : "blue_stained_glass"
      }
   },
   "blue_terracotta" : {
      "sound" : "terracotta",
      "textures" : "blue_terracotta"
   },
   "blue_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_blue"
   },
   "bone_block" : {
      "sound" : "bone_block",
      "textures" : {
         "down" : "bone_block_top",
         "side" : "bone_block_side",
         "up" : "bone_block_top"
      }
   },
   "bookshelf" : {
      "sound" : "wood",
      "textures" : {
         "down" : "bookshelf_top",
         "side" : "bookshelf",
         "up" : "bookshelf_top"
      }
   },
   "border_block" : {
      "sound" : "stone",
      "textures" : "border_block"
   },
   "brain_coral" : {
      "carried_textures" : "brain_coral",
      "sound" : "stone",
      "textures" : "brain_coral"
   },
   "brain_coral_block" : {
      "sound" : "stone",
      "textures" : "brain_coral_block"
   },
   "brain_coral_fan" : {
      "carried_textures" : "brain_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "brain_coral_fan",
         "side" : "brain_coral_fan",
         "up" : "brain_coral_fan"
      }
   },
   "brain_coral_wall_fan" : {
      "carried_textures" : "brain_coral_wall_fan",
      "sound" : "stone",
      "textures" : "brain_coral_wall_fan"
   },
   "brewing_stand" : {
      "sound" : "stone",
      "textures" : {
         "down" : "brewing_stand_base",
         "side" : "brewing_stand",
         "up" : "brewing_stand"
      }
   },
   "brick_block" : {
      "textures" : "brick"
   },
   "brick_double_slab" : {
      "sound" : "stone",
      "textures" : "brick_slab"
   },
   "brick_slab" : {
      "sound" : "stone",
      "textures" : "brick_slab"
   },
   "brick_stairs" : {
      "textures" : "brick"
   },
   "brick_wall" : {
      "sound" : "stone",
      "textures" : "brick_wall"
   },
   "brown_candle" : {
      "carried_textures" : "brown_candle_carried",
      "sound" : "candle",
      "textures" : "brown_candle"
   },
   "brown_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "brown_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_brown"
   },
   "brown_concrete" : {
      "sound" : "stone",
      "textures" : "brown_concrete"
   },
   "brown_concrete_powder" : {
      "sound" : "sand",
      "textures" : "brown_concrete_powder"
   },
   "brown_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "brown_glazed_terracotta"
   },
   "brown_mushroom" : {
      "sound" : "grass",
      "textures" : "mushroom_brown"
   },
   "brown_mushroom_block" : {
      "sound" : "wood",
      "textures" : {
         "down" : "mushroom_brown_bottom",
         "east" : "mushroom_brown_east",
         "north" : "mushroom_brown_north",
         "south" : "mushroom_brown_south",
         "up" : "mushroom_brown_top",
         "west" : "mushroom_brown_west"
      }
   },
   "brown_shulker_box" : {
      "sound" : "stone",
      "textures" : "brown_shulker_box"
   },
   "brown_stained_glass" : {
      "sound" : "glass",
      "textures" : "brown_stained_glass"
   },
   "brown_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "brown_stained_glass",
         "east" : "brown_stained_glass_pane_top",
         "north" : "brown_stained_glass",
         "south" : "brown_stained_glass",
         "up" : "brown_stained_glass",
         "west" : "brown_stained_glass"
      }
   },
   "brown_terracotta" : {
      "sound" : "terracotta",
      "textures" : "brown_terracotta"
   },
   "brown_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_brown"
   },
   "bubble_column" : {
      "isotropic" : false,
      "textures" : {
         "down" : "bubble_column_down_top",
         "east" : "pumpkin_side",
         "north" : "bubble_column_outer",
         "south" : "bubble_column_mid",
         "up" : "bubble_column_up_top",
         "west" : "pumpkin_side"
      }
   },
   "bubble_coral" : {
      "carried_textures" : "bubble_coral",
      "sound" : "stone",
      "textures" : "bubble_coral"
   },
   "bubble_coral_block" : {
      "sound" : "stone",
      "textures" : "bubble_coral_block"
   },
   "bubble_coral_fan" : {
      "carried_textures" : "bubble_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "bubble_coral_fan",
         "side" : "bubble_coral_fan",
         "up" : "bubble_coral_fan"
      }
   },
   "bubble_coral_wall_fan" : {
      "carried_textures" : "bubble_coral_wall_fan",
      "sound" : "stone",
      "textures" : "bubble_coral_wall_fan"
   },
   "budding_amethyst" : {
      "sound" : "amethyst_block",
      "textures" : "budding_amethyst"
   },
   "bush" : {
      "carried_textures" : "bush_carried",
      "sound" : "grass",
      "textures" : "bush"
   },
   "cactus" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cactus_bottom",
         "side" : "cactus_side",
         "up" : "cactus_top"
      }
   },
   "cactus_flower" : {
      "sound" : "cactus_flower",
      "textures" : "cactus_flower"
   },
   "cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "calcite" : {
      "sound" : "calcite",
      "textures" : "calcite"
   },
   "calibrated_sculk_sensor" : {
      "carried_textures" : {
         "down" : "sculk_sensor_tendril_inactive",
         "east" : "sculk_sensor_tendril_active",
         "north" : "sculk_sensor_tendril_active",
         "south" : "calibrated_sculk_sensor_amethyst",
         "up" : "sculk_sensor_tendril_inactive",
         "west" : "sculk_sensor_tendril_active"
      },
      "sound" : "sculk_sensor",
      "textures" : {
         "down" : "sculk_sensor_bottom",
         "east" : "sculk_sensor_side",
         "north" : "sculk_sensor_side",
         "south" : "calibrated_sculk_sensor_input_side",
         "up" : "calibrated_sculk_sensor_top",
         "west" : "sculk_sensor_side"
      }
   },
   "camera" : {
      "isotropic" : true,
      "textures" : {
         "down" : "camera_top",
         "east" : "camera_side",
         "north" : "camera_back",
         "south" : "camera_front",
         "up" : "camera_top",
         "west" : "camera_side"
      }
   },
   "campfire" : {
      "sound" : "wood",
      "textures" : {
         "down" : "campfire_log",
         "side" : "campfire_log_lit",
         "up" : "campfire_fire"
      }
   },
   "candle" : {
      "carried_textures" : "candle_carried",
      "sound" : "candle",
      "textures" : "candle"
   },
   "candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "carpet" : {
      "sound" : "cloth",
      "textures" : "wool"
   },
   "carrots" : {
      "sound" : "grass",
      "textures" : "carrots"
   },
   "cartography_table" : {
      "sound" : "wood",
      "textures" : {
         "down" : "cartography_table_bottom",
         "east" : "cartography_table_side3",
         "north" : "cartography_table_side3",
         "south" : "cartography_table_side1",
         "up" : "cartography_table_top",
         "west" : "cartography_table_side2"
      }
   },
   "carved_pumpkin" : {
      "sound" : "wood",
      "textures" : {
         "down" : "pumpkin_top",
         "east" : "pumpkin_side",
         "north" : "pumpkin_side",
         "south" : "pumpkin_face",
         "up" : "pumpkin_top",
         "west" : "pumpkin_side"
      }
   },
   "cauldron" : {
      "textures" : {
         "down" : "cauldron_bottom",
         "east" : "still_water_grey",
         "north" : "cauldron_side",
         "south" : "cauldron_inner",
         "up" : "cauldron_top",
         "west" : "cauldron_water"
      }
   },
   "cave_vines" : {
      "sound" : "cave_vines",
      "textures" : {
         "down" : "cave_vines_head",
         "side" : "cave_vines_body",
         "up" : "cave_vines_body"
      }
   },
   "cave_vines_body_with_berries" : {
      "sound" : "cave_vines",
      "textures" : {
         "down" : "cave_vines_body",
         "side" : "cave_vines_body",
         "up" : "cave_vines_body"
      }
   },
   "cave_vines_head_with_berries" : {
      "sound" : "cave_vines",
      "textures" : {
         "down" : "cave_vines_head",
         "side" : "cave_vines_head",
         "up" : "cave_vines_head"
      }
   },
   "chain" : {
      "sound" : "chain",
      "textures" : {
         "down" : "chain1",
         "side" : "chain2",
         "up" : "chain1"
      }
   },
   "chain_command_block" : {
      "sound" : "metal",
      "textures" : {
         "down" : "command_block_chain_conditional_side",
         "east" : "command_block_chain_side",
         "north" : "command_block_chain_front",
         "south" : "command_block_chain_back",
         "up" : "command_block_chain_conditional_side",
         "west" : "command_block_chain_side"
      }
   },
   "cherry_button" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_door" : {
      "sound" : "cherry_wood",
      "textures" : {
         "down" : "cherry_door_bottom",
         "side" : "cherry_door_top",
         "up" : "cherry_door_bottom"
      }
   },
   "cherry_double_slab" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_fence" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_fence_gate" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_hanging_sign" : {
      "sound" : "cherry_wood_hanging_sign",
      "textures" : "cherry_planks"
   },
   "cherry_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "sound" : "cherry_leaves",
      "textures" : "cherry_leaves"
   },
   "cherry_log" : {
      "sound" : "cherry_wood",
      "textures" : {
         "down" : "cherry_log_top",
         "side" : "cherry_log_side",
         "up" : "cherry_log_top"
      }
   },
   "cherry_planks" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_pressure_plate" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_sapling" : {
      "sound" : "bamboo_sapling",
      "textures" : "cherry_sapling"
   },
   "cherry_slab" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_stairs" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_standing_sign" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_trapdoor" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_trapdoor"
   },
   "cherry_wall_sign" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_planks"
   },
   "cherry_wood" : {
      "sound" : "cherry_wood",
      "textures" : "cherry_log_side"
   },
   "chest" : {
      "sound" : "wood",
      "textures" : {
         "down" : "chest_inventory_top",
         "east" : "chest_inventory_side",
         "north" : "chest_inventory_side",
         "south" : "chest_inventory_front",
         "up" : "chest_inventory_top",
         "west" : "chest_inventory_side"
      }
   },
   "chipped_anvil" : {
      "sound" : "anvil",
      "textures" : {
         "down" : "flattened_anvil_base",
         "side" : "flattened_anvil_base",
         "up" : "chipped_anvil_top"
      }
   },
   "chiseled_bookshelf" : {
      "sound" : "chiseled_bookshelf",
      "textures" : {
         "down" : "chiseled_bookshelf_top",
         "east" : "chiseled_bookshelf_side",
         "north" : "chiseled_bookshelf_front",
         "south" : "chiseled_bookshelf_side",
         "up" : "chiseled_bookshelf_top",
         "west" : "chiseled_bookshelf_side"
      }
   },
   "chiseled_copper" : {
      "sound" : "copper",
      "textures" : "chiseled_copper"
   },
   "chiseled_deepslate" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "chiseled_deepslate"
   },
   "chiseled_nether_bricks" : {
      "sound" : "nether_brick",
      "textures" : "chiseled_nether_bricks"
   },
   "chiseled_polished_blackstone" : {
      "sound" : "stone",
      "textures" : "chiseled_polished_blackstone"
   },
   "chiseled_quartz_block" : {
      "sound" : "stone",
      "textures" : {
         "down" : "chiseled_quartz_block_top",
         "side" : "chiseled_quartz_block_side",
         "up" : "chiseled_quartz_block_top"
      }
   },
   "chiseled_red_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_redsandstone_top",
         "side" : "chiseled_red_sandstone",
         "up" : "flattened_redsandstone_top"
      }
   },
   "chiseled_resin_bricks" : {
      "sound" : "resin_brick",
      "textures" : "chiseled_resin_bricks"
   },
   "chiseled_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_sandstone_top",
         "side" : "chiseled_sandstone",
         "up" : "flattened_sandstone_top"
      }
   },
   "chiseled_stone_bricks" : {
      "sound" : "stone",
      "textures" : "chiseled_stone_bricks"
   },
   "chiseled_tuff" : {
      "sound" : "tuff",
      "textures" : {
         "down" : "chiseled_tuff_top",
         "side" : "chiseled_tuff",
         "up" : "chiseled_tuff_top"
      }
   },
   "chiseled_tuff_bricks" : {
      "sound" : "tuff_bricks",
      "textures" : {
         "down" : "chiseled_tuff_bricks_top",
         "side" : "chiseled_tuff_bricks",
         "up" : "chiseled_tuff_bricks_top"
      }
   },
   "chorus_flower" : {
      "sound" : "stone",
      "textures" : "chorus_flower"
   },
   "chorus_plant" : {
      "sound" : "stone",
      "textures" : "chorus_plant"
   },
   "clay" : {
      "isotropic" : true,
      "sound" : "gravel",
      "textures" : "clay"
   },
   "closed_eyeblossom" : {
      "sound" : "eyeblossom",
      "textures" : "closed_eyeblossom"
   },
   "coal_block" : {
      "sound" : "stone",
      "textures" : "coal_block"
   },
   "coal_ore" : {
      "sound" : "stone",
      "textures" : "coal_ore"
   },
   "coarse_dirt" : {
      "isotropic" : true,
      "sound" : "gravel",
      "textures" : "coarse_dirt"
   },
   "cobbled_deepslate" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "cobbled_deepslate"
   },
   "cobbled_deepslate_double_slab" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "cobbled_deepslate"
   },
   "cobbled_deepslate_slab" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "cobbled_deepslate"
   },
   "cobbled_deepslate_stairs" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "cobbled_deepslate"
   },
   "cobbled_deepslate_wall" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "cobbled_deepslate"
   },
   "cobblestone" : {
      "sound" : "stone",
      "textures" : "cobblestone"
   },
   "cobblestone_double_slab" : {
      "sound" : "stone",
      "textures" : "cobblestone_slab"
   },
   "cobblestone_slab" : {
      "sound" : "stone",
      "textures" : "cobblestone_slab"
   },
   "cobblestone_wall" : {
      "sound" : "stone",
      "textures" : "flattened_cobblestone_wall"
   },
   "cocoa" : {
      "sound" : "wood",
      "textures" : "cocoa"
   },
   "command_block" : {
      "sound" : "metal",
      "textures" : {
         "down" : "command_block_conditional_side",
         "east" : "command_block_side",
         "north" : "command_block_front",
         "south" : "command_block_back",
         "up" : "command_block_conditional_side",
         "west" : "command_block_side"
      }
   },
   "composter" : {
      "sound" : "wood",
      "textures" : {
         "down" : "composter_bottom",
         "side" : "composter_side",
         "up" : "composter_top"
      }
   },
   "concrete" : {
      "sound" : "stone",
      "textures" : "concrete"
   },
   "concretePowder" : {
      "sound" : "sand",
      "textures" : "concretePowder"
   },
   "conduit" : {
      "sound" : "stone",
      "textures" : "conduit"
   },
   "copper_block" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "copper_block"
   },
   "copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "copper_bulb"
   },
   "copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "copper_door_bottom",
         "side" : "copper_door_top",
         "up" : "copper_door_bottom"
      }
   },
   "copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "copper_grate"
   },
   "copper_ore" : {
      "isotropic" : false,
      "sound" : "stone",
      "textures" : "copper_ore"
   },
   "copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "copper_trapdoor"
   },
   "coral" : {
      "carried_textures" : "coral",
      "sound" : "stone",
      "textures" : "coral"
   },
   "coral_block" : {
      "sounds" : "stone",
      "textures" : "coral_block"
   },
   "coral_fan" : {
      "carried_textures" : "coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "coral_fan",
         "side" : "coral_fan",
         "up" : "coral_fan"
      }
   },
   "coral_fan_dead" : {
      "carried_textures" : "coral_fan_dead",
      "sound" : "stone",
      "textures" : {
         "down" : "coral_fan_dead",
         "side" : "coral_fan_dead",
         "up" : "coral_fan_dead"
      }
   },
   "coral_fan_hang" : {
      "carried_textures" : "coral_fan_hang_a",
      "sound" : "stone",
      "textures" : {
         "down" : "coral_fan_hang_a",
         "side" : "coral_fan_hang_a",
         "up" : "coral_fan_hang_a"
      }
   },
   "coral_fan_hang2" : {
      "carried_textures" : "coral_fan_hang_b",
      "sound" : "stone",
      "textures" : {
         "down" : "coral_fan_hang_b",
         "side" : "coral_fan_hang_b",
         "up" : "coral_fan_hang_b"
      }
   },
   "coral_fan_hang3" : {
      "carried_textures" : "coral_fan_hang_c",
      "sound" : "stone",
      "textures" : {
         "down" : "coral_fan_hang_c",
         "side" : "coral_fan_hang_c",
         "up" : "coral_fan_hang_c"
      }
   },
   "cornflower" : {
      "sound" : "grass",
      "textures" : "cornflower"
   },
   "cracked_deepslate_bricks" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "cracked_deepslate_bricks"
   },
   "cracked_deepslate_tiles" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "cracked_deepslate_tiles"
   },
   "cracked_nether_bricks" : {
      "sound" : "nether_brick",
      "textures" : "cracked_nether_bricks"
   },
   "cracked_polished_blackstone_bricks" : {
      "sound" : "stone",
      "textures" : "cracked_polished_blackstone_bricks"
   },
   "cracked_stone_bricks" : {
      "sound" : "stone",
      "textures" : "cracked_stone_bricks"
   },
   "crafter" : {
      "sound" : "stone",
      "textures" : {
         "down" : "crafter_bottom",
         "east" : "crafter_east",
         "north" : "crafter_north",
         "south" : "crafter_south",
         "up" : "crafter_top",
         "west" : "crafter_west"
      }
   },
   "crafting_table" : {
      "sound" : "wood",
      "textures" : {
         "down" : "crafting_table_bottom",
         "east" : "crafting_table_side",
         "north" : "crafting_table_front",
         "south" : "crafting_table_front",
         "up" : "crafting_table_top",
         "west" : "crafting_table_side"
      }
   },
   "creaking_heart" : {
      "sound" : "creaking_heart",
      "textures" : {
         "down" : "creaking_heart_top",
         "side" : "creaking_heart_side",
         "up" : "creaking_heart_top"
      }
   },
   "creeper_head" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "crimson_button" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_door" : {
      "sound" : "nether_wood",
      "textures" : {
         "down" : "crimson_door_lower",
         "side" : "crimson_door_top",
         "up" : "crimson_door_lower"
      }
   },
   "crimson_double_slab" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_fence" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_fence_gate" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_fungus" : {
      "sound" : "fungus",
      "textures" : "nether_shroom_red"
   },
   "crimson_hanging_sign" : {
      "sound" : "nether_wood_hanging_sign",
      "textures" : "crimson_sign"
   },
   "crimson_hyphae" : {
      "sound" : "stem",
      "textures" : {
         "down" : "crimson_log_side",
         "east" : "crimson_log_side",
         "north" : "crimson_log_side",
         "south" : "crimson_log_side",
         "up" : "crimson_log_side",
         "west" : "crimson_log_side"
      }
   },
   "crimson_nylium" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "nylium",
      "textures" : {
         "down" : "netherrack",
         "east" : "crimson_nylium_side",
         "north" : "crimson_nylium_side",
         "south" : "crimson_nylium_side",
         "up" : "crimson_nylium_top",
         "west" : "crimson_nylium_side"
      }
   },
   "crimson_planks" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_pressure_plate" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_roots" : {
      "sound" : "roots",
      "textures" : {
         "down" : "crimson_roots",
         "east" : "crimson_roots",
         "north" : "crimson_roots",
         "south" : "crimson_roots_pot",
         "up" : "crimson_roots",
         "west" : "crimson_roots"
      }
   },
   "crimson_slab" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_stairs" : {
      "sound" : "nether_wood",
      "textures" : "crimson_planks"
   },
   "crimson_standing_sign" : {
      "sound" : "nether_wood",
      "textures" : "crimson_sign"
   },
   "crimson_stem" : {
      "sound" : "stem",
      "textures" : {
         "down" : "crimson_log_top",
         "east" : "crimson_log_side",
         "north" : "crimson_log_side",
         "south" : "crimson_log_side",
         "up" : "crimson_log_top",
         "west" : "crimson_log_side"
      }
   },
   "crimson_trapdoor" : {
      "sound" : "nether_wood",
      "textures" : "crimson_trapdoor"
   },
   "crimson_wall_sign" : {
      "sound" : "nether_wood",
      "textures" : "crimson_sign"
   },
   "crying_obsidian" : {
      "ambient_occlusion_exponent" : 2.0,
      "isotropic" : true,
      "sound" : "stone",
      "textures" : "crying_obsidian"
   },
   "cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "cut_red_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_redsandstone_top",
         "side" : "cut_red_sandstone",
         "up" : "flattened_redsandstone_top"
      }
   },
   "cut_red_sandstone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "cut_red_sandstone_slab_top",
         "side" : "cut_red_sandstone_slab_side",
         "up" : "cut_red_sandstone_slab_top"
      }
   },
   "cut_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_sandstone_top",
         "side" : "cut_sandstone",
         "up" : "flattened_sandstone_top"
      }
   },
   "cut_sandstone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "cut_sandstone_slab_top",
         "side" : "cut_sandstone_slab_side",
         "up" : "cut_sandstone_slab_top"
      }
   },
   "cyan_candle" : {
      "carried_textures" : "cyan_candle_carried",
      "sound" : "candle",
      "textures" : "cyan_candle"
   },
   "cyan_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "cyan_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_cyan"
   },
   "cyan_concrete" : {
      "sound" : "stone",
      "textures" : "cyan_concrete"
   },
   "cyan_concrete_powder" : {
      "sound" : "sand",
      "textures" : "cyan_concrete_powder"
   },
   "cyan_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "cyan_glazed_terracotta"
   },
   "cyan_shulker_box" : {
      "sound" : "stone",
      "textures" : "cyan_shulker_box"
   },
   "cyan_stained_glass" : {
      "sound" : "glass",
      "textures" : "cyan_stained_glass"
   },
   "cyan_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "cyan_stained_glass",
         "east" : "cyan_stained_glass_pane_top",
         "north" : "cyan_stained_glass",
         "south" : "cyan_stained_glass",
         "up" : "cyan_stained_glass",
         "west" : "cyan_stained_glass"
      }
   },
   "cyan_terracotta" : {
      "sound" : "terracotta",
      "textures" : "cyan_terracotta"
   },
   "cyan_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_cyan"
   },
   "damaged_anvil" : {
      "sound" : "anvil",
      "textures" : {
         "down" : "flattened_anvil_base",
         "side" : "flattened_anvil_base",
         "up" : "damaged_anvil_top"
      }
   },
   "dandelion" : {
      "sound" : "grass",
      "textures" : "dandelion"
   },
   "dark_oak_button" : {
      "sound" : "wood",
      "textures" : "dark_oak_planks"
   },
   "dark_oak_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "dark_oak_double_slab" : {
      "sound" : "wood",
      "textures" : "dark_oak_planks"
   },
   "dark_oak_fence" : {
      "sound" : "wood",
      "textures" : "dark_oak_planks"
   },
   "dark_oak_fence_gate" : {
      "sound" : "wood",
      "textures" : "wood_big_oak"
   },
   "dark_oak_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "darkoak_sign"
   },
   "dark_oak_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "big_oak_leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "big_oak_leaves"
   },
   "dark_oak_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "dark_oak_log_top",
         "side" : "dark_oak_log_side",
         "up" : "dark_oak_log_top"
      }
   },
   "dark_oak_pressure_plate" : {
      "sound" : "wood",
      "textures" : "dark_oak_planks"
   },
   "dark_oak_sapling" : {
      "sound" : "grass",
      "textures" : "dark_oak_sapling"
   },
   "dark_oak_slab" : {
      "sound" : "wood",
      "textures" : "dark_oak_planks"
   },
   "dark_oak_stairs" : {
      "sound" : "wood",
      "textures" : "wood_big_oak"
   },
   "dark_oak_trapdoor" : {
      "sound" : "wood",
      "textures" : "dark_oak_trapdoor"
   },
   "dark_oak_wood" : {
      "carried_textures" : "dark_oak_wood",
      "sound" : "wood",
      "textures" : "dark_oak_wood"
   },
   "dark_prismarine" : {
      "sound" : "stone",
      "textures" : "dark_prismarine"
   },
   "dark_prismarine_double_slab" : {
      "sound" : "stone",
      "textures" : "dark_prismarine_slab"
   },
   "dark_prismarine_slab" : {
      "sound" : "stone",
      "textures" : "dark_prismarine_slab"
   },
   "dark_prismarine_stairs" : {
      "sound" : "stone",
      "textures" : "dark_prismarine"
   },
   "darkoak_standing_sign" : {
      "sound" : "wood",
      "textures" : "darkoak_sign"
   },
   "darkoak_wall_sign" : {
      "sound" : "wood",
      "textures" : "darkoak_sign"
   },
   "daylight_detector" : {
      "sound" : "wood",
      "textures" : {
         "down" : "daylight_detector_side",
         "side" : "daylight_detector_side",
         "up" : "daylight_detector_top"
      }
   },
   "daylight_detector_inverted" : {
      "sound" : "wood",
      "textures" : {
         "down" : "daylight_detector_side",
         "side" : "daylight_detector_side",
         "up" : "daylight_detector_top"
      }
   },
   "dead_brain_coral" : {
      "carried_textures" : "dead_brain_coral",
      "sound" : "stone",
      "textures" : "dead_brain_coral"
   },
   "dead_brain_coral_block" : {
      "sound" : "stone",
      "textures" : "dead_brain_coral_block"
   },
   "dead_brain_coral_fan" : {
      "carried_textures" : "dead_brain_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "dead_brain_coral_fan",
         "side" : "dead_brain_coral_fan",
         "up" : "dead_brain_coral_fan"
      }
   },
   "dead_brain_coral_wall_fan" : {
      "carried_textures" : "dead_brain_coral_wall_fan",
      "sound" : "stone",
      "textures" : "dead_brain_coral_wall_fan"
   },
   "dead_bubble_coral" : {
      "carried_textures" : "dead_bubble_coral",
      "sound" : "stone",
      "textures" : "dead_bubble_coral"
   },
   "dead_bubble_coral_block" : {
      "sound" : "stone",
      "textures" : "dead_bubble_coral_block"
   },
   "dead_bubble_coral_fan" : {
      "carried_textures" : "dead_bubble_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "dead_bubble_coral_fan",
         "side" : "dead_bubble_coral_fan",
         "up" : "dead_bubble_coral_fan"
      }
   },
   "dead_bubble_coral_wall_fan" : {
      "carried_textures" : "dead_bubble_coral_wall_fan",
      "sound" : "stone",
      "textures" : "dead_bubble_coral_wall_fan"
   },
   "dead_fire_coral" : {
      "carried_textures" : "dead_fire_coral",
      "sound" : "stone",
      "textures" : "dead_fire_coral"
   },
   "dead_fire_coral_block" : {
      "sound" : "stone",
      "textures" : "dead_fire_coral_block"
   },
   "dead_fire_coral_fan" : {
      "carried_textures" : "dead_fire_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "dead_fire_coral_fan",
         "side" : "dead_fire_coral_fan",
         "up" : "dead_fire_coral_fan"
      }
   },
   "dead_fire_coral_wall_fan" : {
      "carried_textures" : "dead_fire_coral_wall_fan",
      "sound" : "stone",
      "textures" : "dead_fire_coral_wall_fan"
   },
   "dead_horn_coral" : {
      "carried_textures" : "dead_horn_coral",
      "sound" : "stone",
      "textures" : "dead_horn_coral"
   },
   "dead_horn_coral_block" : {
      "sound" : "stone",
      "textures" : "dead_horn_coral_block"
   },
   "dead_horn_coral_fan" : {
      "carried_textures" : "dead_horn_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "dead_horn_coral_fan",
         "side" : "dead_horn_coral_fan",
         "up" : "dead_horn_coral_fan"
      }
   },
   "dead_horn_coral_wall_fan" : {
      "carried_textures" : "dead_horn_coral_wall_fan",
      "sound" : "stone",
      "textures" : "dead_horn_coral_wall_fan"
   },
   "dead_tube_coral" : {
      "carried_textures" : "dead_tube_coral",
      "sound" : "stone",
      "textures" : "dead_tube_coral"
   },
   "dead_tube_coral_block" : {
      "sound" : "stone",
      "textures" : "dead_tube_coral_block"
   },
   "dead_tube_coral_fan" : {
      "carried_textures" : "dead_tube_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "dead_tube_coral_fan",
         "side" : "dead_tube_coral_fan",
         "up" : "dead_tube_coral_fan"
      }
   },
   "dead_tube_coral_wall_fan" : {
      "carried_textures" : "dead_tube_coral_wall_fan",
      "sound" : "stone",
      "textures" : "dead_tube_coral_wall_fan"
   },
   "deadbush" : {
      "sound" : "deadbush",
      "textures" : "deadbush"
   },
   "decorated_pot" : {
      "sound" : "decorated_pot",
      "textures" : "decorated_pot"
   },
   "deepslate" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "deepslate",
      "textures" : {
         "down" : "deepslate_top",
         "side" : "deepslate",
         "up" : "deepslate_top"
      }
   },
   "deepslate_brick_double_slab" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_bricks"
   },
   "deepslate_brick_slab" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_bricks"
   },
   "deepslate_brick_stairs" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_bricks"
   },
   "deepslate_brick_wall" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_bricks"
   },
   "deepslate_bricks" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_bricks"
   },
   "deepslate_coal_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_coal_ore"
   },
   "deepslate_copper_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_copper_ore"
   },
   "deepslate_diamond_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_diamond_ore"
   },
   "deepslate_emerald_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_emerald_ore"
   },
   "deepslate_gold_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_gold_ore"
   },
   "deepslate_iron_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_iron_ore"
   },
   "deepslate_lapis_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_lapis_ore"
   },
   "deepslate_redstone_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_redstone_ore"
   },
   "deepslate_tile_double_slab" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_tiles"
   },
   "deepslate_tile_slab" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_tiles"
   },
   "deepslate_tile_stairs" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_tiles"
   },
   "deepslate_tile_wall" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_tiles"
   },
   "deepslate_tiles" : {
      "isotropic" : false,
      "sound" : "deepslate_bricks",
      "textures" : "deepslate_tiles"
   },
   "deny" : {
      "sound" : "stone",
      "textures" : "build_deny"
   },
   "deprecated_anvil" : {
      "sound" : "anvil",
      "textures" : "flattened_anvil_base"
   },
   "deprecated_purpur_block_1" : {
      "sound" : "stone",
      "textures" : "flattened_purpur_block"
   },
   "deprecated_purpur_block_2" : {
      "sound" : "stone",
      "textures" : "flattened_purpur_block"
   },
   "detector_rail" : {
      "sound" : "metal",
      "textures" : {
         "down" : "rail_detector",
         "side" : "rail_detector",
         "up" : "rail_detector_powered"
      }
   },
   "diamond_block" : {
      "sound" : "metal",
      "textures" : "diamond_block"
   },
   "diamond_ore" : {
      "sound" : "stone",
      "textures" : "diamond_ore"
   },
   "diorite" : {
      "sound" : "stone",
      "textures" : "diorite"
   },
   "diorite_double_slab" : {
      "sound" : "stone",
      "textures" : "diorite_slab"
   },
   "diorite_slab" : {
      "sound" : "stone",
      "textures" : "diorite_slab"
   },
   "diorite_stairs" : {
      "sound" : "stone",
      "textures" : "diorite"
   },
   "diorite_wall" : {
      "sound" : "stone",
      "textures" : "diorite_wall"
   },
   "dirt" : {
      "isotropic" : true,
      "sound" : "gravel",
      "textures" : "flattened_dirt"
   },
   "dirt_with_roots" : {
      "isotropic" : true,
      "sound" : "dirt_with_roots",
      "textures" : "dirt_with_roots"
   },
   "dispenser" : {
      "carried_textures" : {
         "down" : "dispenser_top",
         "east" : "dispenser_side",
         "north" : "dispenser_side",
         "south" : "dispenser_front_horizontal",
         "up" : "dispenser_top",
         "west" : "dispenser_side"
      },
      "sound" : "stone",
      "textures" : {
         "down" : "dispenser_top",
         "east" : "dispenser_front_vertical",
         "north" : "dispenser_side",
         "south" : "dispenser_front_horizontal",
         "up" : "dispenser_top",
         "west" : "dispenser_side"
      }
   },
   "double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "double_plant" : {
      "carried_textures" : "double_plant_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "double_plant_bottom",
         "side" : "sunflower_additional",
         "up" : "double_plant_top"
      }
   },
   "double_stone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom",
         "side" : "stone_slab_side",
         "up" : "stone_slab_top"
      }
   },
   "double_stone_slab2" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom_2",
         "side" : "stone_slab_side_2",
         "up" : "stone_slab_top_2"
      }
   },
   "double_stone_slab3" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom_3",
         "side" : "stone_slab_side_3",
         "up" : "stone_slab_top_3"
      }
   },
   "double_stone_slab4" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom_4",
         "side" : "stone_slab_side_4",
         "up" : "stone_slab_top_4"
      }
   },
   "double_wooden_slab" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "dragon_egg" : {
      "sound" : "stone",
      "textures" : "dragon_egg"
   },
   "dragon_head" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "dried_kelp_block" : {
      "carried_textures" : {
         "down" : "dried_kelp_block_top",
         "east" : "dried_kelp_block_side_a",
         "north" : "dried_kelp_block_side_b",
         "south" : "dried_kelp_block_side_a",
         "up" : "dried_kelp_block_top",
         "west" : "dried_kelp_block_side_b"
      },
      "sound" : "grass",
      "textures" : {
         "down" : "dried_kelp_block_top",
         "east" : "dried_kelp_block_side_b",
         "north" : "dried_kelp_block_side_a",
         "south" : "dried_kelp_block_side_b",
         "up" : "dried_kelp_block_top",
         "west" : "dried_kelp_block_side_a"
      }
   },
   "dripstone_block" : {
      "sound" : "dripstone_block",
      "textures" : "dripstone_block"
   },
   "dropper" : {
      "carried_textures" : {
         "down" : "dropper_top",
         "east" : "dropper_side",
         "north" : "dropper_side",
         "south" : "dropper_front_horizontal",
         "up" : "dropper_top",
         "west" : "dropper_side"
      },
      "sound" : "stone",
      "textures" : {
         "down" : "dropper_top",
         "east" : "dropper_front_vertical",
         "north" : "dropper_side",
         "south" : "dropper_front_horizontal",
         "up" : "dropper_top",
         "west" : "dropper_side"
      }
   },
   "emerald_block" : {
      "sound" : "metal",
      "textures" : "emerald_block"
   },
   "emerald_ore" : {
      "sound" : "stone",
      "textures" : "emerald_ore"
   },
   "enchanting_table" : {
      "textures" : {
         "down" : "enchanting_table_bottom",
         "side" : "enchanting_table_side",
         "up" : "enchanting_table_top"
      }
   },
   "end_brick_stairs" : {
      "sound" : "stone",
      "textures" : "end_bricks"
   },
   "end_bricks" : {
      "sound" : "stone",
      "textures" : "end_bricks"
   },
   "end_gateway" : {
      "textures" : "end_gateway"
   },
   "end_portal" : {
      "textures" : "end_portal"
   },
   "end_portal_frame" : {
      "carried_textures" : "endframe_eye",
      "sound" : "glass",
      "textures" : {
         "down" : "endframe_bottom",
         "east" : "endframe_side",
         "north" : "endframe_side",
         "south" : "endframe_side",
         "up" : "endframe_top",
         "west" : "endframe_side"
      }
   },
   "end_rod" : {
      "sound" : "wood",
      "textures" : "end_rod"
   },
   "end_stone" : {
      "sound" : "stone",
      "textures" : "end_stone"
   },
   "end_stone_brick_double_slab" : {
      "sound" : "stone",
      "textures" : "end_stone_brick_slab"
   },
   "end_stone_brick_slab" : {
      "sound" : "stone",
      "textures" : "end_stone_brick_slab"
   },
   "end_stone_brick_wall" : {
      "sound" : "stone",
      "textures" : "end_stone_brick_wall"
   },
   "ender_chest" : {
      "textures" : {
         "down" : "ender_chest_inventory_top",
         "east" : "ender_chest_inventory_side",
         "north" : "ender_chest_inventory_side",
         "south" : "ender_chest_inventory_front",
         "up" : "ender_chest_inventory_top",
         "west" : "ender_chest_inventory_side"
      }
   },
   "exposed_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "exposed_chiseled_copper"
   },
   "exposed_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_copper"
   },
   "exposed_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "exposed_copper_bulb"
   },
   "exposed_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "exposed_copper_door_bottom",
         "side" : "exposed_copper_door_top",
         "up" : "exposed_copper_door_bottom"
      }
   },
   "exposed_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "exposed_copper_grate"
   },
   "exposed_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "exposed_copper_trapdoor"
   },
   "exposed_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "exposed_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "exposed_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "exposed_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "farmland" : {
      "sound" : "gravel",
      "textures" : {
         "down" : "farmland_side",
         "side" : "farmland_side",
         "up" : "farmland"
      }
   },
   "fence" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "fence_gate" : {
      "sound" : "wood",
      "textures" : "wood_oak"
   },
   "fern" : {
      "carried_textures" : "fern_carried",
      "sound" : "grass",
      "textures" : "fern"
   },
   "fire" : {
      "sound" : "wood",
      "textures" : {
         "down" : "fire_1",
         "side" : "fire_0",
         "up" : "fire_0"
      }
   },
   "fire_coral" : {
      "carried_textures" : "fire_coral",
      "sound" : "stone",
      "textures" : "fire_coral"
   },
   "fire_coral_block" : {
      "sound" : "stone",
      "textures" : "fire_coral_block"
   },
   "fire_coral_fan" : {
      "carried_textures" : "fire_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "fire_coral_fan",
         "side" : "fire_coral_fan",
         "up" : "fire_coral_fan"
      }
   },
   "fire_coral_wall_fan" : {
      "carried_textures" : "fire_coral_wall_fan",
      "sound" : "stone",
      "textures" : "fire_coral_wall_fan"
   },
   "firefly_bush" : {
      "carried_textures" : "firefly_bush_carried",
      "sound" : "firefly_bush",
      "textures" : "firefly_bush"
   },
   "fletching_table" : {
      "sound" : "wood",
      "textures" : {
         "down" : "birch_planks",
         "east" : "fletching_table_side2",
         "north" : "fletching_table_side1",
         "south" : "fletching_table_side1",
         "up" : "fletching_table_top",
         "west" : "fletching_table_side2"
      }
   },
   "flower_pot" : {
      "textures" : "flower_pot"
   },
   "flowering_azalea" : {
      "sound" : "azalea",
      "textures" : {
         "down" : "potted_flowering_azalea_bush_top",
         "east" : "azalea_plant",
         "north" : "flowering_azalea_side",
         "side" : "flowering_azalea_side",
         "south" : "potted_flowering_azalea_bush_side",
         "up" : "flowering_azalea_top",
         "west" : "potted_flowering_azalea_bush_plant"
      }
   },
   "flowing_lava" : {
      "textures" : {
         "down" : "still_lava",
         "side" : "flowing_lava",
         "up" : "still_lava"
      }
   },
   "flowing_water" : {
      "textures" : {
         "down" : "still_water_grey",
         "side" : "flowing_water_grey",
         "up" : "still_water_grey"
      }
   },
   "format_version" : "1.21.40",
   "frame" : {
      "sound" : "itemframe",
      "textures" : "itemframe_background"
   },
   "frog_spawn" : {
      "sound" : "frog_spawn",
      "textures" : "frog_spawn"
   },
   "frosted_ice" : {
      "sound" : "glass",
      "textures" : "frosted_ice"
   },
   "furnace" : {
      "sound" : "stone",
      "textures" : {
         "down" : "furnace_top",
         "east" : "furnace_side",
         "north" : "furnace_side",
         "south" : "furnace_front_off",
         "up" : "furnace_top",
         "west" : "furnace_side"
      }
   },
   "gilded_blackstone" : {
      "sound" : "stone",
      "textures" : "gilded_blackstone"
   },
   "glass" : {
      "sound" : "glass",
      "textures" : "glass"
   },
   "glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "glass",
         "east" : "glass_pane_top",
         "north" : "glass",
         "south" : "glass",
         "up" : "glass",
         "west" : "glass"
      }
   },
   "glow_frame" : {
      "sound" : "itemframe",
      "textures" : "glow_item_frame"
   },
   "glow_lichen" : {
      "sound" : "glow_lichen",
      "textures" : "glow_lichen"
   },
   "glowingobsidian" : {
      "ambient_occlusion_exponent" : 1.0,
      "sound" : "stone",
      "textures" : "glowing_obsidian"
   },
   "glowstone" : {
      "isotropic" : true,
      "sound" : "glass",
      "textures" : "glowstone"
   },
   "gold_block" : {
      "sound" : "metal",
      "textures" : "gold_block"
   },
   "gold_ore" : {
      "sound" : "stone",
      "textures" : "gold_ore"
   },
   "golden_rail" : {
      "sound" : "metal",
      "textures" : {
         "down" : "rail_golden",
         "side" : "rail_golden",
         "up" : "rail_golden_powered"
      }
   },
   "granite" : {
      "sound" : "stone",
      "textures" : "granite"
   },
   "granite_double_slab" : {
      "sound" : "stone",
      "textures" : "granite_slab"
   },
   "granite_slab" : {
      "sound" : "stone",
      "textures" : "granite_slab"
   },
   "granite_stairs" : {
      "sound" : "stone",
      "textures" : "granite"
   },
   "granite_wall" : {
      "sound" : "stone",
      "textures" : "granite_wall"
   },
   "grass" : {
      "carried_textures" : {
         "down" : "grass_carried_bottom",
         "side" : "grass_carried",
         "up" : "grass_carried_top"
      },
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : {
         "down" : "grass_bottom",
         "side" : "grass_side",
         "up" : "grass_top"
      }
   },
   "grass_path" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : {
         "down" : "dirt",
         "side" : "grass_path_side",
         "up" : "grass_path_top"
      }
   },
   "gravel" : {
      "sound" : "gravel",
      "textures" : "gravel"
   },
   "gray_candle" : {
      "carried_textures" : "gray_candle_carried",
      "sound" : "candle",
      "textures" : "gray_candle"
   },
   "gray_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "gray_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_gray"
   },
   "gray_concrete" : {
      "sound" : "stone",
      "textures" : "gray_concrete"
   },
   "gray_concrete_powder" : {
      "sound" : "sand",
      "textures" : "gray_concrete_powder"
   },
   "gray_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "gray_glazed_terracotta"
   },
   "gray_shulker_box" : {
      "sound" : "stone",
      "textures" : "gray_shulker_box"
   },
   "gray_stained_glass" : {
      "sound" : "glass",
      "textures" : "gray_stained_glass"
   },
   "gray_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "gray_stained_glass",
         "east" : "gray_stained_glass_pane_top",
         "north" : "gray_stained_glass",
         "south" : "gray_stained_glass",
         "up" : "gray_stained_glass",
         "west" : "gray_stained_glass"
      }
   },
   "gray_terracotta" : {
      "sound" : "terracotta",
      "textures" : "gray_terracotta"
   },
   "gray_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_gray"
   },
   "green_candle" : {
      "carried_textures" : "green_candle_carried",
      "sound" : "candle",
      "textures" : "green_candle"
   },
   "green_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "green_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_green"
   },
   "green_concrete" : {
      "sound" : "stone",
      "textures" : "green_concrete"
   },
   "green_concrete_powder" : {
      "sound" : "sand",
      "textures" : "green_concrete_powder"
   },
   "green_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "green_glazed_terracotta"
   },
   "green_shulker_box" : {
      "sound" : "stone",
      "textures" : "green_shulker_box"
   },
   "green_stained_glass" : {
      "sound" : "glass",
      "textures" : "green_stained_glass"
   },
   "green_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "green_stained_glass",
         "east" : "green_stained_glass_pane_top",
         "north" : "green_stained_glass",
         "south" : "green_stained_glass",
         "up" : "green_stained_glass",
         "west" : "green_stained_glass"
      }
   },
   "green_terracotta" : {
      "sound" : "terracotta",
      "textures" : "green_terracotta"
   },
   "green_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_green"
   },
   "grindstone" : {
      "sound" : "stone",
      "textures" : {
         "down" : "grindstone_leg",
         "east" : "grindstone_side",
         "north" : "grindstone_pivot",
         "south" : "grindstone_side",
         "up" : "grindstone_round",
         "west" : "grindstone_side"
      }
   },
   "hanging_roots" : {
      "sound" : "hanging_roots",
      "textures" : "hanging_roots"
   },
   "hardened_clay" : {
      "isotropic" : true,
      "sound" : "terracotta",
      "textures" : "hardened_clay"
   },
   "hay_block" : {
      "sound" : "grass",
      "textures" : {
         "down" : "hayblock_top",
         "side" : "hayblock_side",
         "up" : "hayblock_top"
      }
   },
   "heavy_core" : {
      "sound" : "heavy_core",
      "textures" : "heavy_core"
   },
   "heavy_weighted_pressure_plate" : {
      "sound" : "iron",
      "textures" : "iron_block"
   },
   "honey_block" : {
      "sound" : "honey_block",
      "textures" : {
         "down" : "honey_bottom",
         "east" : "honey_side",
         "north" : "honey_side",
         "south" : "honey_side",
         "up" : "honey_top",
         "west" : "honey_side"
      }
   },
   "honeycomb_block" : {
      "sound" : "coral",
      "textures" : "honeycomb_block"
   },
   "hopper" : {
      "sound" : "metal",
      "textures" : {
         "down" : "hopper_inside",
         "east" : "hopper_outside",
         "north" : "hopper_outside",
         "south" : "hopper_outside",
         "up" : "hopper_top",
         "west" : "hopper_outside"
      }
   },
   "horn_coral" : {
      "carried_textures" : "horn_coral",
      "sound" : "stone",
      "textures" : "horn_coral"
   },
   "horn_coral_block" : {
      "sound" : "stone",
      "textures" : "horn_coral_block"
   },
   "horn_coral_fan" : {
      "carried_textures" : "horn_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "horn_coral_fan",
         "side" : "horn_coral_fan",
         "up" : "horn_coral_fan"
      }
   },
   "horn_coral_wall_fan" : {
      "carried_textures" : "horn_coral_wall_fan",
      "sound" : "stone",
      "textures" : "horn_coral_wall_fan"
   },
   "ice" : {
      "sound" : "glass",
      "textures" : "ice"
   },
   "infested_chiseled_stone_bricks" : {
      "sound" : "stone",
      "textures" : "infested_chiseled_stone_bricks"
   },
   "infested_cobblestone" : {
      "sound" : "stone",
      "textures" : "infested_cobblestone"
   },
   "infested_cracked_stone_bricks" : {
      "sound" : "stone",
      "textures" : "infested_cracked_stone_bricks"
   },
   "infested_deepslate" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "deepslate",
      "textures" : {
         "down" : "deepslate_top",
         "side" : "deepslate",
         "up" : "deepslate_top"
      }
   },
   "infested_mossy_stone_bricks" : {
      "sound" : "stone",
      "textures" : "infested_mossy_stone_bricks"
   },
   "infested_stone" : {
      "sound" : "stone",
      "textures" : "infested_stone"
   },
   "infested_stone_bricks" : {
      "sound" : "stone",
      "textures" : "infested_stone_bricks"
   },
   "info_update" : {
      "sound" : "gravel",
      "textures" : "missing_tile"
   },
   "info_update2" : {
      "sound" : "gravel",
      "textures" : "missing_tile"
   },
   "invisibleBedrock" : {
      "textures" : "stone"
   },
   "iron_bars" : {
      "sound" : "iron",
      "textures" : {
         "down" : "iron_bars",
         "east" : "iron_bars_edge",
         "north" : "iron_bars",
         "south" : "iron_bars",
         "up" : "iron_bars",
         "west" : "iron_bars"
      }
   },
   "iron_block" : {
      "sound" : "iron",
      "textures" : "iron_block"
   },
   "iron_door" : {
      "sound" : "iron",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "iron_ore" : {
      "sound" : "stone",
      "textures" : "iron_ore"
   },
   "iron_trapdoor" : {
      "sound" : "iron",
      "textures" : "iron_trapdoor"
   },
   "jigsaw" : {
      "textures" : {
         "down" : "jigsaw_side",
         "east" : "jigsaw_side",
         "north" : "jigsaw_front",
         "south" : "jigsaw_back",
         "up" : "jigsaw_lock",
         "west" : "jigsaw_side"
      }
   },
   "jukebox" : {
      "sound" : "wood",
      "textures" : {
         "down" : "jukebox_side",
         "side" : "jukebox_side",
         "up" : "jukebox_top"
      }
   },
   "jungle_button" : {
      "sound" : "wood",
      "textures" : "jungle_planks"
   },
   "jungle_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "jungle_double_slab" : {
      "sound" : "wood",
      "textures" : "jungle_planks"
   },
   "jungle_fence" : {
      "sound" : "wood",
      "textures" : "jungle_planks"
   },
   "jungle_fence_gate" : {
      "sound" : "wood",
      "textures" : "wood_jungle"
   },
   "jungle_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "jungle_sign"
   },
   "jungle_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "jungle_leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "jungle_leaves"
   },
   "jungle_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "jungle_log_top",
         "side" : "jungle_log_side",
         "up" : "jungle_log_top"
      }
   },
   "jungle_pressure_plate" : {
      "sound" : "wood",
      "textures" : "jungle_planks"
   },
   "jungle_sapling" : {
      "sound" : "grass",
      "textures" : "jungle_sapling"
   },
   "jungle_slab" : {
      "sound" : "wood",
      "textures" : "jungle_planks"
   },
   "jungle_stairs" : {
      "sound" : "wood",
      "textures" : "wood_jungle"
   },
   "jungle_standing_sign" : {
      "sound" : "wood",
      "textures" : "jungle_sign"
   },
   "jungle_trapdoor" : {
      "sound" : "wood",
      "textures" : "jungle_trapdoor"
   },
   "jungle_wall_sign" : {
      "sound" : "wood",
      "textures" : "jungle_sign"
   },
   "jungle_wood" : {
      "carried_textures" : "jungle_wood",
      "sound" : "wood",
      "textures" : "jungle_wood"
   },
   "kelp" : {
      "sound" : "grass",
      "textures" : {
         "down" : "kelp_d",
         "east" : "kelp_top",
         "north" : "kelp_a",
         "south" : "kelp_b",
         "up" : "kelp_c",
         "west" : "kelp_top_bulb"
      }
   },
   "ladder" : {
      "sound" : "ladder",
      "textures" : "ladder"
   },
   "lantern" : {
      "carried_textures" : "lantern_carried",
      "sound" : "lantern",
      "textures" : "lantern"
   },
   "lapis_block" : {
      "sound" : "stone",
      "textures" : "lapis_block"
   },
   "lapis_ore" : {
      "sound" : "stone",
      "textures" : "lapis_ore"
   },
   "large_amethyst_bud" : {
      "sound" : "large_amethyst_bud",
      "textures" : "large_amethyst_bud"
   },
   "large_fern" : {
      "carried_textures" : "large_fern_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "large_fern_bottom",
         "side" : "sunflower_additional",
         "up" : "large_fern_top"
      }
   },
   "lava" : {
      "isotropic" : true,
      "textures" : {
         "down" : "still_lava",
         "side" : "flowing_lava",
         "up" : "still_lava"
      }
   },
   "lava_cauldron" : {
      "textures" : {
         "down" : "cauldron_bottom",
         "east" : "still_lava",
         "north" : "cauldron_side",
         "south" : "cauldron_inner",
         "up" : "cauldron_top",
         "west" : "cauldron_water"
      }
   },
   "leaf_litter" : {
      "carried_textures" : "leaf_litter_carried",
      "sound" : "leaf_litter",
      "textures" : "leaf_litter"
   },
   "leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "leaves"
   },
   "leaves2" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "leaves_carried2",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "leaves2"
   },
   "lectern" : {
      "sound" : "wood",
      "textures" : {
         "down" : "lectern_bottom",
         "east" : "lectern_base",
         "north" : "lectern_front",
         "south" : "lectern_sides",
         "up" : "lectern_top",
         "west" : "lectern_sides"
      }
   },
   "lever" : {
      "sound" : "lever",
      "textures" : {
         "down" : "lever",
         "east" : "lever_particle",
         "north" : "lever",
         "south" : "lever",
         "up" : "lever",
         "west" : "lever"
      }
   },
   "light_block" : {
      "carried_textures" : "light_block_carried"
   },
   "light_block_0" : {
      "carried_textures" : "light_block_0",
      "sound" : "stone"
   },
   "light_block_1" : {
      "carried_textures" : "light_block_1",
      "sound" : "stone"
   },
   "light_block_10" : {
      "carried_textures" : "light_block_10",
      "sound" : "stone"
   },
   "light_block_11" : {
      "carried_textures" : "light_block_11",
      "sound" : "stone"
   },
   "light_block_12" : {
      "carried_textures" : "light_block_12",
      "sound" : "stone"
   },
   "light_block_13" : {
      "carried_textures" : "light_block_13",
      "sound" : "stone"
   },
   "light_block_14" : {
      "carried_textures" : "light_block_14",
      "sound" : "stone"
   },
   "light_block_15" : {
      "carried_textures" : "light_block_15",
      "sound" : "stone"
   },
   "light_block_2" : {
      "carried_textures" : "light_block_2",
      "sound" : "stone"
   },
   "light_block_3" : {
      "carried_textures" : "light_block_3",
      "sound" : "stone"
   },
   "light_block_4" : {
      "carried_textures" : "light_block_4",
      "sound" : "stone"
   },
   "light_block_5" : {
      "carried_textures" : "light_block_5",
      "sound" : "stone"
   },
   "light_block_6" : {
      "carried_textures" : "light_block_6",
      "sound" : "stone"
   },
   "light_block_7" : {
      "carried_textures" : "light_block_7",
      "sound" : "stone"
   },
   "light_block_8" : {
      "carried_textures" : "light_block_8",
      "sound" : "stone"
   },
   "light_block_9" : {
      "carried_textures" : "light_block_9",
      "sound" : "stone"
   },
   "light_blue_candle" : {
      "carried_textures" : "light_blue_candle_carried",
      "sound" : "candle",
      "textures" : "light_blue_candle"
   },
   "light_blue_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "light_blue_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_light_blue"
   },
   "light_blue_concrete" : {
      "sound" : "stone",
      "textures" : "light_blue_concrete"
   },
   "light_blue_concrete_powder" : {
      "sound" : "sand",
      "textures" : "light_blue_concrete_powder"
   },
   "light_blue_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "light_blue_glazed_terracotta"
   },
   "light_blue_shulker_box" : {
      "sound" : "stone",
      "textures" : "light_blue_shulker_box"
   },
   "light_blue_stained_glass" : {
      "sound" : "glass",
      "textures" : "light_blue_stained_glass"
   },
   "light_blue_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "light_blue_stained_glass",
         "east" : "light_blue_stained_glass_pane_top",
         "north" : "light_blue_stained_glass",
         "south" : "light_blue_stained_glass",
         "up" : "light_blue_stained_glass",
         "west" : "light_blue_stained_glass"
      }
   },
   "light_blue_terracotta" : {
      "sound" : "terracotta",
      "textures" : "light_blue_terracotta"
   },
   "light_blue_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_light_blue"
   },
   "light_gray_candle" : {
      "carried_textures" : "light_gray_candle_carried",
      "sound" : "candle",
      "textures" : "light_gray_candle"
   },
   "light_gray_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "light_gray_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_silver"
   },
   "light_gray_concrete" : {
      "sound" : "stone",
      "textures" : "light_gray_concrete"
   },
   "light_gray_concrete_powder" : {
      "sound" : "sand",
      "textures" : "light_gray_concrete_powder"
   },
   "light_gray_shulker_box" : {
      "sound" : "stone",
      "textures" : "light_gray_shulker_box"
   },
   "light_gray_stained_glass" : {
      "sound" : "glass",
      "textures" : "light_gray_stained_glass"
   },
   "light_gray_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "light_gray_stained_glass",
         "east" : "light_gray_stained_glass_pane_top",
         "north" : "light_gray_stained_glass",
         "south" : "light_gray_stained_glass",
         "up" : "light_gray_stained_glass",
         "west" : "light_gray_stained_glass"
      }
   },
   "light_gray_terracotta" : {
      "sound" : "terracotta",
      "textures" : "light_gray_terracotta"
   },
   "light_gray_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_silver"
   },
   "light_weighted_pressure_plate" : {
      "sound" : "metal",
      "textures" : "gold_block"
   },
   "lightning_rod" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "lightning_rod"
   },
   "lilac" : {
      "carried_textures" : "lilac_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "lilac_bottom",
         "side" : "lilac_additional",
         "up" : "lilac_top"
      }
   },
   "lily_of_the_valley" : {
      "sound" : "grass",
      "textures" : "lily_of_the_valley"
   },
   "lime_candle" : {
      "carried_textures" : "lime_candle_carried",
      "sound" : "candle",
      "textures" : "lime_candle"
   },
   "lime_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "lime_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_lime"
   },
   "lime_concrete" : {
      "sound" : "stone",
      "textures" : "lime_concrete"
   },
   "lime_concrete_powder" : {
      "sound" : "sand",
      "textures" : "lime_concrete_powder"
   },
   "lime_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "lime_glazed_terracotta"
   },
   "lime_shulker_box" : {
      "sound" : "stone",
      "textures" : "lime_shulker_box"
   },
   "lime_stained_glass" : {
      "sound" : "glass",
      "textures" : "lime_stained_glass"
   },
   "lime_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "lime_stained_glass",
         "east" : "lime_stained_glass_pane_top",
         "north" : "lime_stained_glass",
         "south" : "lime_stained_glass",
         "up" : "lime_stained_glass",
         "west" : "lime_stained_glass"
      }
   },
   "lime_terracotta" : {
      "sound" : "terracotta",
      "textures" : "lime_terracotta"
   },
   "lime_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_lime"
   },
   "lit_blast_furnace" : {
      "sound" : "stone",
      "textures" : {
         "down" : "blast_furnace_top",
         "east" : "blast_furnace_front_on",
         "north" : "blast_furnace_side",
         "south" : "blast_furnace_side",
         "up" : "blast_furnace_top",
         "west" : "blast_furnace_side"
      }
   },
   "lit_deepslate_redstone_ore" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "deepslate_redstone_ore"
   },
   "lit_furnace" : {
      "sound" : "stone",
      "textures" : {
         "down" : "furnace_top",
         "east" : "furnace_front_on",
         "north" : "furnace_side",
         "south" : "furnace_side",
         "up" : "furnace_top",
         "west" : "furnace_side"
      }
   },
   "lit_pumpkin" : {
      "sound" : "wood",
      "textures" : {
         "down" : "pumpkin_top",
         "east" : "pumpkin_side",
         "north" : "pumpkin_side",
         "south" : "pumpkin_face",
         "up" : "pumpkin_top",
         "west" : "pumpkin_side"
      }
   },
   "lit_redstone_lamp" : {
      "sound" : "glass",
      "textures" : "redstone_lamp_on"
   },
   "lit_redstone_ore" : {
      "sound" : "stone",
      "textures" : "redstone_ore"
   },
   "lit_smoker" : {
      "sound" : "stone",
      "textures" : {
         "down" : "smoker_bottom",
         "east" : "smoker_front_on",
         "north" : "smoker_side",
         "south" : "smoker_side",
         "up" : "smoker_top",
         "west" : "smoker_side"
      }
   },
   "lodestone" : {
      "sound" : "lodestone",
      "textures" : {
         "down" : "lodestone_top",
         "east" : "lodestone_side",
         "north" : "lodestone_side",
         "south" : "lodestone_side",
         "up" : "lodestone_top",
         "west" : "lodestone_side"
      }
   },
   "log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "log_top",
         "side" : "log_side",
         "up" : "log_top"
      }
   },
   "log2" : {
      "sound" : "wood",
      "textures" : {
         "down" : "log_top2",
         "side" : "log_side2",
         "up" : "log_top2"
      }
   },
   "loom" : {
      "sound" : "wood",
      "textures" : {
         "down" : "loom_bottom",
         "east" : "loom_side",
         "north" : "loom_front",
         "south" : "loom_side",
         "up" : "loom_top",
         "west" : "loom_side"
      }
   },
   "magenta_candle" : {
      "carried_textures" : "magenta_candle_carried",
      "sound" : "candle",
      "textures" : "magenta_candle"
   },
   "magenta_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "magenta_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_magenta"
   },
   "magenta_concrete" : {
      "sound" : "stone",
      "textures" : "magenta_concrete"
   },
   "magenta_concrete_powder" : {
      "sound" : "sand",
      "textures" : "magenta_concrete_powder"
   },
   "magenta_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "magenta_glazed_terracotta"
   },
   "magenta_shulker_box" : {
      "sound" : "stone",
      "textures" : "magenta_shulker_box"
   },
   "magenta_stained_glass" : {
      "sound" : "glass",
      "textures" : "magenta_stained_glass"
   },
   "magenta_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "magenta_stained_glass",
         "east" : "magenta_stained_glass_pane_top",
         "north" : "magenta_stained_glass",
         "south" : "magenta_stained_glass",
         "up" : "magenta_stained_glass",
         "west" : "magenta_stained_glass"
      }
   },
   "magenta_terracotta" : {
      "sound" : "terracotta",
      "textures" : "magenta_terracotta"
   },
   "magenta_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_magenta"
   },
   "magma" : {
      "isotropic" : true,
      "sound" : "stone",
      "textures" : "magma"
   },
   "mangrove_button" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "mangrove_door_bottom",
         "side" : "mangrove_door_top",
         "up" : "mangrove_door_bottom"
      }
   },
   "mangrove_double_slab" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_fence" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_fence_gate" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "mangrove_sign"
   },
   "mangrove_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "mangrove_leaves_carried",
      "sound" : "grass",
      "textures" : "mangrove_leaves"
   },
   "mangrove_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "mangrove_log_top",
         "side" : "mangrove_log_side",
         "up" : "mangrove_log_top"
      }
   },
   "mangrove_planks" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_pressure_plate" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_propagule" : {
      "carried_textures" : "mangrove_propagule_item",
      "sound" : "grass",
      "textures" : "mangrove_propagule"
   },
   "mangrove_roots" : {
      "sound" : "mangrove_roots",
      "textures" : {
         "down" : "mangrove_roots_top",
         "east" : "mangrove_roots_side",
         "north" : "mangrove_roots_side",
         "south" : "mangrove_roots_side",
         "up" : "mangrove_roots_top",
         "west" : "mangrove_roots_side"
      }
   },
   "mangrove_slab" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_stairs" : {
      "sound" : "wood",
      "textures" : "mangrove_planks"
   },
   "mangrove_standing_sign" : {
      "sound" : "wood",
      "textures" : "mangrove_sign"
   },
   "mangrove_trapdoor" : {
      "sound" : "wood",
      "textures" : "mangrove_trapdoor"
   },
   "mangrove_wall_sign" : {
      "sound" : "wood",
      "textures" : "mangrove_sign"
   },
   "mangrove_wood" : {
      "sound" : "wood",
      "textures" : "mangrove_log_side"
   },
   "medium_amethyst_bud" : {
      "sound" : "medium_amethyst_bud",
      "textures" : "medium_amethyst_bud"
   },
   "melon_block" : {
      "sound" : "wood",
      "textures" : {
         "down" : "melon_top",
         "side" : "melon_side",
         "up" : "melon_top"
      }
   },
   "melon_stem" : {
      "sound" : "wood",
      "textures" : "melon_stem"
   },
   "mob_spawner" : {
      "sound" : "mob_spawner",
      "textures" : "mob_spawner"
   },
   "monster_egg" : {
      "textures" : "monster_egg"
   },
   "moss_block" : {
      "sound" : "moss_block",
      "textures" : "moss_block"
   },
   "moss_carpet" : {
      "sound" : "moss_carpet",
      "textures" : "moss_block"
   },
   "mossy_cobblestone" : {
      "sound" : "stone",
      "textures" : "cobblestone_mossy"
   },
   "mossy_cobblestone_double_slab" : {
      "sound" : "stone",
      "textures" : "mossy_cobblestone_slab"
   },
   "mossy_cobblestone_slab" : {
      "sound" : "stone",
      "textures" : "mossy_cobblestone_slab"
   },
   "mossy_cobblestone_stairs" : {
      "sound" : "stone",
      "textures" : "cobblestone_mossy"
   },
   "mossy_cobblestone_wall" : {
      "sound" : "stone",
      "textures" : "mossy_cobblestone_wall"
   },
   "mossy_stone_brick_slab" : {
      "sound" : "stone",
      "textures" : "mossy_stone_brick_slab"
   },
   "mossy_stone_brick_stairs" : {
      "sound" : "stone",
      "textures" : "mossy_stone_brick"
   },
   "mossy_stone_brick_wall" : {
      "sound" : "stone",
      "textures" : "mossy_stone_brick_wall"
   },
   "mossy_stone_bricks" : {
      "sound" : "stone",
      "textures" : "mossy_stone_bricks"
   },
   "movingBlock" : {
      "textures" : "missing_tile"
   },
   "mud" : {
      "sound" : "mud",
      "textures" : "mud"
   },
   "mud_brick_double_slab" : {
      "isotropic" : false,
      "sound" : "mud_bricks",
      "textures" : "mud_bricks"
   },
   "mud_brick_slab" : {
      "isotropic" : false,
      "sound" : "mud_bricks",
      "textures" : "mud_bricks"
   },
   "mud_brick_stairs" : {
      "isotropic" : false,
      "sound" : "mud_bricks",
      "textures" : "mud_bricks"
   },
   "mud_brick_wall" : {
      "isotropic" : false,
      "sound" : "mud_bricks",
      "textures" : "mud_bricks"
   },
   "mud_bricks" : {
      "sound" : "mud_bricks",
      "textures" : "mud_bricks"
   },
   "muddy_mangrove_roots" : {
      "sound" : "muddy_mangrove_roots",
      "textures" : {
         "down" : "muddy_mangrove_roots_top",
         "east" : "muddy_mangrove_roots_side",
         "north" : "muddy_mangrove_roots_side",
         "south" : "muddy_mangrove_roots_side",
         "up" : "muddy_mangrove_roots_top",
         "west" : "muddy_mangrove_roots_side"
      }
   },
   "mushroom_stem" : {
      "sound" : "wood",
      "textures" : {
         "down" : "mushroom_stem_bottom",
         "east" : "mushroom_stem_east",
         "north" : "mushroom_stem_north",
         "south" : "mushroom_stem_south",
         "up" : "mushroom_stem_top",
         "west" : "mushroom_stem_west"
      }
   },
   "mycelium" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : {
         "down" : "mycelium_bottom",
         "side" : "mycelium_side",
         "up" : "mycelium_top"
      }
   },
   "nether_brick" : {
      "ambient_occlusion_exponent" : 1.0,
      "sound" : "nether_brick",
      "textures" : "nether_brick"
   },
   "nether_brick_double_slab" : {
      "sound" : "stone",
      "textures" : "nether_brick_slab"
   },
   "nether_brick_fence" : {
      "sound" : "nether_brick",
      "textures" : "nether_brick"
   },
   "nether_brick_slab" : {
      "sound" : "nether_brick",
      "textures" : "nether_brick_slab"
   },
   "nether_brick_stairs" : {
      "sound" : "nether_brick",
      "textures" : "nether_brick"
   },
   "nether_brick_wall" : {
      "sound" : "nether_brick",
      "textures" : "nether_brick_wall"
   },
   "nether_gold_ore" : {
      "sound" : "nether_gold_ore",
      "textures" : "nether_gold_ore"
   },
   "nether_sprouts" : {
      "sound" : "nether_sprouts",
      "textures" : "nether_sprouts"
   },
   "nether_wart" : {
      "sound" : "nether_wart",
      "textures" : "nether_wart"
   },
   "nether_wart_block" : {
      "sound" : "nether_wart",
      "textures" : "nether_wart_block"
   },
   "netherite_block" : {
      "sound" : "netherite",
      "textures" : "netherite_block"
   },
   "netherrack" : {
      "isotropic" : true,
      "sound" : "netherrack",
      "textures" : "netherrack"
   },
   "netherreactor" : {
      "sound" : "metal",
      "textures" : "reactor_core"
   },
   "normal_stone_slab" : {
      "sound" : "stone",
      "textures" : "normal_stone_slab"
   },
   "normal_stone_stairs" : {
      "sound" : "stone",
      "textures" : "stone"
   },
   "noteblock" : {
      "sound" : "wood",
      "textures" : "noteblock"
   },
   "oak_double_slab" : {
      "sound" : "wood",
      "textures" : "oak_planks"
   },
   "oak_fence" : {
      "sound" : "wood",
      "textures" : "oak_planks"
   },
   "oak_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "sign"
   },
   "oak_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "oak_leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "oak_leaves"
   },
   "oak_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "oak_log_top",
         "side" : "oak_log_side",
         "up" : "oak_log_top"
      }
   },
   "oak_sapling" : {
      "sound" : "grass",
      "textures" : "oak_sapling"
   },
   "oak_slab" : {
      "sound" : "wood",
      "textures" : "oak_planks"
   },
   "oak_stairs" : {
      "sound" : "wood",
      "textures" : "wood_oak"
   },
   "oak_wood" : {
      "carried_textures" : "oak_wood",
      "sound" : "wood",
      "textures" : "oak_wood"
   },
   "observer" : {
      "sound" : "metal",
      "textures" : {
         "down" : "observer_bottom",
         "east" : "observer_east",
         "north" : "observer_north",
         "south" : "observer_south",
         "up" : "observer_top",
         "west" : "observer_west"
      }
   },
   "obsidian" : {
      "ambient_occlusion_exponent" : 2.0,
      "isotropic" : true,
      "sound" : "stone",
      "textures" : "obsidian"
   },
   "ochre_froglight" : {
      "sound" : "froglight",
      "textures" : {
         "down" : "ochre_froglight_top",
         "side" : "ochre_froglight_side",
         "up" : "ochre_froglight_top"
      }
   },
   "open_eyeblossom" : {
      "carried_textures" : "open_eyeblossom_carried",
      "sound" : "eyeblossom",
      "textures" : "open_eyeblossom"
   },
   "orange_candle" : {
      "carried_textures" : "orange_candle_carried",
      "sound" : "candle",
      "textures" : "orange_candle"
   },
   "orange_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "orange_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_orange"
   },
   "orange_concrete" : {
      "sound" : "stone",
      "textures" : "orange_concrete"
   },
   "orange_concrete_powder" : {
      "sound" : "sand",
      "textures" : "orange_concrete_powder"
   },
   "orange_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "orange_glazed_terracotta"
   },
   "orange_shulker_box" : {
      "sound" : "stone",
      "textures" : "orange_shulker_box"
   },
   "orange_stained_glass" : {
      "sound" : "glass",
      "textures" : "orange_stained_glass"
   },
   "orange_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "orange_stained_glass",
         "east" : "orange_stained_glass_pane_top",
         "north" : "orange_stained_glass",
         "south" : "orange_stained_glass",
         "up" : "orange_stained_glass",
         "west" : "orange_stained_glass"
      }
   },
   "orange_terracotta" : {
      "sound" : "terracotta",
      "textures" : "orange_terracotta"
   },
   "orange_tulip" : {
      "sound" : "grass",
      "textures" : "orange_tulip"
   },
   "orange_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_orange"
   },
   "oxeye_daisy" : {
      "sound" : "grass",
      "textures" : "oxeye_daisy"
   },
   "oxidized_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "oxidized_chiseled_copper"
   },
   "oxidized_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_copper"
   },
   "oxidized_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "oxidized_copper_bulb"
   },
   "oxidized_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "oxidized_copper_door_bottom",
         "side" : "oxidized_copper_door_top",
         "up" : "oxidized_copper_door_bottom"
      }
   },
   "oxidized_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "oxidized_copper_grate"
   },
   "oxidized_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "oxidized_copper_trapdoor"
   },
   "oxidized_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "oxidized_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "oxidized_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "oxidized_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "packed_ice" : {
      "sound" : "glass",
      "textures" : "ice_packed"
   },
   "packed_mud" : {
      "sound" : "packed_mud",
      "textures" : "packed_mud"
   },
   "pale_hanging_moss" : {
      "sound" : "pale_hanging_moss",
      "textures" : "pale_hanging_moss"
   },
   "pale_moss_block" : {
      "sound" : "moss_block",
      "textures" : "pale_moss_block"
   },
   "pale_moss_carpet" : {
      "carried_textures" : "pale_moss_carpet_side",
      "sound" : "moss_carpet",
      "textures" : "pale_moss_block"
   },
   "pale_oak_button" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "pale_oak_door_bottom",
         "side" : "pale_oak_door_top",
         "up" : "pale_oak_door_bottom"
      }
   },
   "pale_oak_double_slab" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_fence" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_fence_gate" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_leaves" : {
      "ambient_occlusion_exponent" : 1.0,
      "sound" : "grass",
      "textures" : "pale_oak_leaves"
   },
   "pale_oak_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "pale_oak_log_top",
         "side" : "pale_oak_log_side",
         "up" : "pale_oak_log_top"
      }
   },
   "pale_oak_planks" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_pressure_plate" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_sapling" : {
      "sound" : "grass",
      "textures" : "pale_oak_sapling"
   },
   "pale_oak_slab" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_stairs" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_standing_sign" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_trapdoor" : {
      "sound" : "wood",
      "textures" : "pale_oak_trapdoor"
   },
   "pale_oak_wall_sign" : {
      "sound" : "wood",
      "textures" : "pale_oak_planks"
   },
   "pale_oak_wood" : {
      "sound" : "wood",
      "textures" : "pale_oak_log_side"
   },
   "pearlescent_froglight" : {
      "sound" : "froglight",
      "textures" : {
         "down" : "pearlescent_froglight_top",
         "side" : "pearlescent_froglight_side",
         "up" : "pearlescent_froglight_top"
      }
   },
   "peony" : {
      "carried_textures" : "peony_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "peony_bottom",
         "side" : "sunflower_additional",
         "up" : "peony_top"
      }
   },
   "petrified_oak_double_slab" : {
      "sound" : "stone",
      "textures" : "oak_planks"
   },
   "petrified_oak_slab" : {
      "sound" : "stone",
      "textures" : "oak_planks"
   },
   "piglin_head" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "pink_candle" : {
      "carried_textures" : "pink_candle_carried",
      "sound" : "candle",
      "textures" : "pink_candle"
   },
   "pink_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "pink_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_pink"
   },
   "pink_concrete" : {
      "sound" : "stone",
      "textures" : "pink_concrete"
   },
   "pink_concrete_powder" : {
      "sound" : "sand",
      "textures" : "pink_concrete_powder"
   },
   "pink_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "pink_glazed_terracotta"
   },
   "pink_petals" : {
      "carried_textures" : "pink_petals_carried",
      "sound" : "pink_petals",
      "textures" : "pink_petals"
   },
   "pink_shulker_box" : {
      "sound" : "stone",
      "textures" : "pink_shulker_box"
   },
   "pink_stained_glass" : {
      "sound" : "glass",
      "textures" : "pink_stained_glass"
   },
   "pink_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "pink_stained_glass",
         "east" : "pink_stained_glass_pane_top",
         "north" : "pink_stained_glass",
         "south" : "pink_stained_glass",
         "up" : "pink_stained_glass",
         "west" : "pink_stained_glass"
      }
   },
   "pink_terracotta" : {
      "sound" : "terracotta",
      "textures" : "pink_terracotta"
   },
   "pink_tulip" : {
      "sound" : "grass",
      "textures" : "pink_tulip"
   },
   "pink_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_pink"
   },
   "piston" : {
      "carried_textures" : {
         "down" : "piston_bottom",
         "side" : "piston_side",
         "up" : "piston_top_normal"
      },
      "sound" : "stone",
      "textures" : {
         "down" : "piston_bottom",
         "side" : "piston_side",
         "up" : "piston_top"
      }
   },
   "pistonArmCollision" : {
      "textures" : "piston_top"
   },
   "pitcher_crop" : {
      "sound" : "grass",
      "textures" : {
         "down" : "pitcher_crop_bottom",
         "east" : "pitcher_crop_upper_flower",
         "north" : "pitcher_crop_side",
         "south" : "pitcher_crop_lower_flower",
         "up" : "pitcher_crop_top",
         "west" : "pitcher_crop_upper_flower"
      }
   },
   "pitcher_plant" : {
      "carried_textures" : "pitcher_plant_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "pitcher_plant_bottom",
         "side" : "pitcher_plant_bottom",
         "up" : "pitcher_plant_top"
      }
   },
   "planks" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "player_head" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "podzol" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "gravel",
      "textures" : {
         "down" : "dirt_podzol_bottom",
         "side" : "dirt_podzol_side",
         "up" : "dirt_podzol_top"
      }
   },
   "pointed_dripstone" : {
      "sound" : "pointed_dripstone",
      "textures" : {
         "down" : "pointed_dripstone_frustum",
         "east" : "pointed_dripstone_base",
         "north" : "pointed_dripstone_tip",
         "south" : "pointed_dripstone_middle",
         "up" : "pointed_dripstone_base",
         "west" : "pointed_dripstone_merge"
      }
   },
   "polished_andesite" : {
      "sound" : "stone",
      "textures" : "polished_andesite"
   },
   "polished_andesite_double_slab" : {
      "sound" : "stone",
      "textures" : "polished_andesite_slab"
   },
   "polished_andesite_slab" : {
      "sound" : "stone",
      "textures" : "polished_andesite_slab"
   },
   "polished_andesite_stairs" : {
      "sound" : "stone",
      "textures" : "polished_andesite"
   },
   "polished_basalt" : {
      "sound" : "basalt",
      "textures" : {
         "down" : "polished_basalt_top",
         "side" : "polished_basalt_side",
         "up" : "polished_basalt_top"
      }
   },
   "polished_blackstone" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_blackstone_brick_double_slab" : {
      "sound" : "stone",
      "textures" : "polished_blackstone_bricks"
   },
   "polished_blackstone_brick_slab" : {
      "sound" : "stone",
      "textures" : "polished_blackstone_bricks"
   },
   "polished_blackstone_brick_stairs" : {
      "sound" : "stone",
      "textures" : "polished_blackstone_bricks"
   },
   "polished_blackstone_brick_wall" : {
      "sound" : "stone",
      "textures" : "polished_blackstone_bricks"
   },
   "polished_blackstone_bricks" : {
      "sound" : "stone",
      "textures" : "polished_blackstone_bricks"
   },
   "polished_blackstone_button" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_blackstone_double_slab" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_blackstone_pressure_plate" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_blackstone_slab" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_blackstone_stairs" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_blackstone_wall" : {
      "sound" : "stone",
      "textures" : "polished_blackstone"
   },
   "polished_deepslate" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "polished_deepslate"
   },
   "polished_deepslate_double_slab" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "polished_deepslate"
   },
   "polished_deepslate_slab" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "polished_deepslate"
   },
   "polished_deepslate_stairs" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "polished_deepslate"
   },
   "polished_deepslate_wall" : {
      "isotropic" : false,
      "sound" : "deepslate",
      "textures" : "polished_deepslate"
   },
   "polished_diorite" : {
      "sound" : "stone",
      "textures" : "polished_diorite"
   },
   "polished_diorite_double_slab" : {
      "sound" : "stone",
      "textures" : "polished_diorite_slab"
   },
   "polished_diorite_slab" : {
      "sound" : "stone",
      "textures" : "polished_diorite_slab"
   },
   "polished_diorite_stairs" : {
      "sound" : "stone",
      "textures" : "polished_diorite"
   },
   "polished_granite" : {
      "sound" : "stone",
      "textures" : "polished_granite"
   },
   "polished_granite_double_slab" : {
      "sound" : "stone",
      "textures" : "polished_granite_slab"
   },
   "polished_granite_slab" : {
      "sound" : "stone",
      "textures" : "polished_granite_slab"
   },
   "polished_granite_stairs" : {
      "sound" : "stone",
      "textures" : "polished_granite"
   },
   "polished_tuff" : {
      "sound" : "polished_tuff",
      "textures" : "polished_tuff"
   },
   "polished_tuff_double_slab" : {
      "sound" : "polished_tuff",
      "textures" : "polished_tuff"
   },
   "polished_tuff_slab" : {
      "sound" : "polished_tuff",
      "textures" : "polished_tuff"
   },
   "polished_tuff_stairs" : {
      "sound" : "polished_tuff",
      "textures" : "polished_tuff"
   },
   "polished_tuff_wall" : {
      "sound" : "polished_tuff",
      "textures" : "polished_tuff"
   },
   "poppy" : {
      "sound" : "grass",
      "textures" : "poppy"
   },
   "portal" : {
      "sound" : "glass",
      "textures" : "portal"
   },
   "potatoes" : {
      "sound" : "grass",
      "textures" : "potatoes"
   },
   "powder_snow" : {
      "isotropic" : false,
      "sound" : "powder_snow",
      "textures" : "powder_snow"
   },
   "powered_comparator" : {
      "sound" : "wood",
      "textures" : {
         "down" : "comparator_stone_slab",
         "side" : "comparator_stone_slab",
         "up" : "comparator_up"
      }
   },
   "powered_repeater" : {
      "sound" : "wood",
      "textures" : {
         "down" : "repeater_floor",
         "side" : "repeater_floor",
         "up" : "repeater_up"
      }
   },
   "prismarine" : {
      "sound" : "stone",
      "textures" : "flattened_prismarine"
   },
   "prismarine_brick_double_slab" : {
      "sound" : "stone",
      "textures" : "prismarine_brick_slab"
   },
   "prismarine_brick_slab" : {
      "sound" : "stone",
      "textures" : "prismarine_brick_slab"
   },
   "prismarine_bricks" : {
      "sound" : "stone",
      "textures" : "prismarine_bricks"
   },
   "prismarine_bricks_stairs" : {
      "sound" : "stone",
      "textures" : "prismarine_bricks"
   },
   "prismarine_double_slab" : {
      "sound" : "stone",
      "textures" : "prismarine_slab"
   },
   "prismarine_slab" : {
      "sound" : "stone",
      "textures" : "prismarine_slab"
   },
   "prismarine_stairs" : {
      "sound" : "stone",
      "textures" : "prismarine"
   },
   "prismarine_wall" : {
      "sound" : "stone",
      "textures" : "prismarine_wall"
   },
   "pumpkin" : {
      "sound" : "wood",
      "textures" : {
         "down" : "pumpkin_top",
         "east" : "pumpkin_side",
         "north" : "pumpkin_side",
         "south" : "pumpkin_face",
         "up" : "pumpkin_top",
         "west" : "pumpkin_side"
      }
   },
   "pumpkin_stem" : {
      "sound" : "wood",
      "textures" : "pumpkin_stem"
   },
   "purple_candle" : {
      "carried_textures" : "purple_candle_carried",
      "sound" : "candle",
      "textures" : "purple_candle"
   },
   "purple_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "purple_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_purple"
   },
   "purple_concrete" : {
      "sound" : "stone",
      "textures" : "purple_concrete"
   },
   "purple_concrete_powder" : {
      "sound" : "sand",
      "textures" : "purple_concrete_powder"
   },
   "purple_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "purple_glazed_terracotta"
   },
   "purple_shulker_box" : {
      "sound" : "stone",
      "textures" : "purple_shulker_box"
   },
   "purple_stained_glass" : {
      "sound" : "glass",
      "textures" : "purple_stained_glass"
   },
   "purple_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "purple_stained_glass",
         "east" : "purple_stained_glass_pane_top",
         "north" : "purple_stained_glass",
         "south" : "purple_stained_glass",
         "up" : "purple_stained_glass",
         "west" : "purple_stained_glass"
      }
   },
   "purple_terracotta" : {
      "sound" : "terracotta",
      "textures" : "purple_terracotta"
   },
   "purple_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_purple"
   },
   "purpur_block" : {
      "sound" : "stone",
      "textures" : "flattened_purpur_block"
   },
   "purpur_double_slab" : {
      "sound" : "stone",
      "textures" : "purpur_slab"
   },
   "purpur_pillar" : {
      "sound" : "stone",
      "textures" : {
         "down" : "purpur_pillar_top",
         "side" : "purpur_pillar_side",
         "up" : "purpur_pillar_top"
      }
   },
   "purpur_slab" : {
      "sound" : "stone",
      "textures" : "purpur_slab"
   },
   "purpur_stairs" : {
      "textures" : "stair_purpur_block"
   },
   "quartz_block" : {
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_quartz_block_top",
         "side" : "flattened_quartz_block_side",
         "up" : "flattened_quartz_block_top"
      }
   },
   "quartz_bricks" : {
      "sound" : "stone",
      "textures" : "quartz_bricks"
   },
   "quartz_double_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "quartz_slab_bottom",
         "side" : "quartz_slab_side",
         "up" : "quartz_slab_top"
      }
   },
   "quartz_ore" : {
      "sound" : "nether_gold_ore",
      "textures" : "quartz_ore"
   },
   "quartz_pillar" : {
      "sound" : "stone",
      "textures" : {
         "down" : "quartz_pillar_top",
         "side" : "quartz_pillar_side",
         "up" : "quartz_pillar_top"
      }
   },
   "quartz_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "quartz_slab_bottom",
         "side" : "quartz_slab_side",
         "up" : "quartz_slab_top"
      }
   },
   "quartz_stairs" : {
      "textures" : {
         "down" : "stair_quartz_block_bottom",
         "side" : "stair_quartz_block_side",
         "up" : "stair_quartz_block_top"
      }
   },
   "rail" : {
      "sound" : "metal",
      "textures" : {
         "down" : "rail_normal",
         "side" : "rail_normal",
         "up" : "rail_normal_turned"
      }
   },
   "raw_copper_block" : {
      "isotropic" : false,
      "sound" : "stone",
      "textures" : "raw_copper_block"
   },
   "raw_gold_block" : {
      "isotropic" : false,
      "sound" : "stone",
      "textures" : "raw_gold_block"
   },
   "raw_iron_block" : {
      "isotropic" : false,
      "sound" : "stone",
      "textures" : "raw_iron_block"
   },
   "red_candle" : {
      "carried_textures" : "red_candle_carried",
      "sound" : "candle",
      "textures" : "red_candle"
   },
   "red_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "red_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_red"
   },
   "red_concrete" : {
      "sound" : "stone",
      "textures" : "red_concrete"
   },
   "red_concrete_powder" : {
      "sound" : "sand",
      "textures" : "red_concrete_powder"
   },
   "red_flower" : {
      "sound" : "grass",
      "textures" : "red_flower"
   },
   "red_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "red_glazed_terracotta"
   },
   "red_mushroom" : {
      "sound" : "grass",
      "textures" : "mushroom_red"
   },
   "red_mushroom_block" : {
      "sound" : "wood",
      "textures" : {
         "down" : "mushroom_red_bottom",
         "east" : "mushroom_red_east",
         "north" : "mushroom_red_north",
         "south" : "mushroom_red_south",
         "up" : "mushroom_red_top",
         "west" : "mushroom_red_west"
      }
   },
   "red_nether_brick" : {
      "ambient_occlusion_exponent" : 1.0,
      "sound" : "nether_brick",
      "textures" : "red_nether_brick"
   },
   "red_nether_brick_double_slab" : {
      "sound" : "stone",
      "textures" : "red_nether_brick_slab"
   },
   "red_nether_brick_slab" : {
      "sound" : "nether_brick",
      "textures" : "red_nether_brick_slab"
   },
   "red_nether_brick_stairs" : {
      "ambient_occlusion_exponent" : 1.0,
      "sound" : "nether_brick",
      "textures" : "red_nether_brick"
   },
   "red_nether_brick_wall" : {
      "sound" : "nether_brick",
      "textures" : "red_nether_brick_wall"
   },
   "red_sand" : {
      "ambient_occlusion_exponent" : 0.550,
      "isotropic" : true,
      "sound" : "sand",
      "textures" : "red_sand"
   },
   "red_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_redsandstone_bottom",
         "side" : "flattened_redsandstone",
         "up" : "flattened_redsandstone_top"
      }
   },
   "red_sandstone_double_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "red_sandstone_slab_bottom",
         "side" : "red_sandstone_slab_side",
         "up" : "red_sandstone_slab_top"
      }
   },
   "red_sandstone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "red_sandstone_slab_bottom",
         "side" : "red_sandstone_slab_side",
         "up" : "red_sandstone_slab_top"
      }
   },
   "red_sandstone_stairs" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "textures" : {
         "down" : "redsandstone_bottom",
         "side" : "redsandstone_side",
         "up" : "redsandstone_top"
      }
   },
   "red_sandstone_wall" : {
      "sound" : "stone",
      "textures" : "red_sandstone_wall"
   },
   "red_shulker_box" : {
      "sound" : "stone",
      "textures" : "red_shulker_box"
   },
   "red_stained_glass" : {
      "sound" : "glass",
      "textures" : "red_stained_glass"
   },
   "red_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "red_stained_glass",
         "east" : "red_stained_glass_pane_top",
         "north" : "red_stained_glass",
         "south" : "red_stained_glass",
         "up" : "red_stained_glass",
         "west" : "red_stained_glass"
      }
   },
   "red_terracotta" : {
      "sound" : "terracotta",
      "textures" : "red_terracotta"
   },
   "red_tulip" : {
      "sound" : "grass",
      "textures" : "red_tulip"
   },
   "red_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_red"
   },
   "redstone_block" : {
      "sound" : "stone",
      "textures" : "redstone_block"
   },
   "redstone_lamp" : {
      "sound" : "glass",
      "textures" : "redstone_lamp_off"
   },
   "redstone_ore" : {
      "sound" : "stone",
      "textures" : "redstone_ore"
   },
   "redstone_torch" : {
      "sound" : "wood",
      "textures" : "redstone_torch_on"
   },
   "redstone_wire" : {
      "textures" : {
         "down" : "redstone_dust_line",
         "side" : "redstone_dust_line",
         "up" : "redstone_dust_cross"
      }
   },
   "reeds" : {
      "sound" : "grass",
      "textures" : "reeds"
   },
   "reinforced_deepslate" : {
      "sound" : "deepslate",
      "textures" : {
         "down" : "reinforced_deepslate_bottom",
         "side" : "reinforced_deepslate_side",
         "up" : "reinforced_deepslate_top"
      }
   },
   "repeating_command_block" : {
      "sound" : "metal",
      "textures" : {
         "down" : "command_block_repeating_conditional_side",
         "east" : "command_block_repeating_side",
         "north" : "command_block_repeating_front",
         "south" : "command_block_repeating_back",
         "up" : "command_block_repeating_conditional_side",
         "west" : "command_block_repeating_side"
      }
   },
   "reserved6" : {
      "textures" : "missing_tile"
   },
   "resin_block" : {
      "sound" : "resin",
      "textures" : "resin_block"
   },
   "resin_brick_double_slab" : {
      "sound" : "resin_brick",
      "textures" : "resin_bricks"
   },
   "resin_brick_slab" : {
      "sound" : "resin_brick",
      "textures" : "resin_bricks"
   },
   "resin_brick_stairs" : {
      "sound" : "resin_brick",
      "textures" : "resin_bricks"
   },
   "resin_brick_wall" : {
      "sound" : "resin_brick",
      "textures" : "resin_bricks"
   },
   "resin_bricks" : {
      "sound" : "resin_brick",
      "textures" : "resin_bricks"
   },
   "resin_clump" : {
      "carried_textures" : "resin_clump_carried",
      "sound" : "resin",
      "textures" : "resin_clump"
   },
   "respawn_anchor" : {
      "ambient_occlusion_exponent" : 2.0,
      "sound" : "metal",
      "textures" : {
         "down" : "respawn_anchor_bottom",
         "east" : "respawn_anchor_side",
         "north" : "respawn_anchor_side",
         "south" : "respawn_anchor_side",
         "up" : "respawn_anchor_top",
         "west" : "respawn_anchor_side"
      }
   },
   "rose_bush" : {
      "carried_textures" : "rose_bush_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "rose_bush_bottom",
         "side" : "sunflower_additional",
         "up" : "rose_bush_top"
      }
   },
   "sand" : {
      "ambient_occlusion_exponent" : 0.550,
      "isotropic" : true,
      "sound" : "sand",
      "textures" : "flattened_sand"
   },
   "sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_sandstone_bottom",
         "side" : "flattened_sandstone",
         "up" : "flattened_sandstone_top"
      }
   },
   "sandstone_double_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "sandstone_slab_bottom",
         "side" : "sandstone_slab_side",
         "up" : "sandstone_slab_top"
      }
   },
   "sandstone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "sandstone_slab_bottom",
         "side" : "sandstone_slab_side",
         "up" : "sandstone_slab_top"
      }
   },
   "sandstone_stairs" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "textures" : {
         "down" : "sandstone_bottom",
         "side" : "sandstone_side",
         "up" : "sandstone_top"
      }
   },
   "sandstone_wall" : {
      "sound" : "stone",
      "textures" : "sandstone_wall"
   },
   "sapling" : {
      "sound" : "grass",
      "textures" : "sapling"
   },
   "scaffolding" : {
      "isotropic" : false,
      "sound" : "scaffolding",
      "textures" : {
         "down" : "scaffolding_bottom",
         "side" : "scaffolding_side",
         "up" : "scaffolding_top"
      }
   },
   "sculk" : {
      "isotropic" : true,
      "sound" : "sculk",
      "textures" : "sculk"
   },
   "sculk_catalyst" : {
      "sound" : "sculk_catalyst",
      "textures" : {
         "down" : "sculk_catalyst_bottom",
         "side" : "sculk_catalyst_side",
         "up" : "sculk_catalyst_top"
      }
   },
   "sculk_sensor" : {
      "carried_textures" : {
         "down" : "sculk_sensor_tendril_inactive",
         "side" : "sculk_sensor_tendril_active",
         "up" : "sculk_sensor_tendril_inactive"
      },
      "sound" : "sculk_sensor",
      "textures" : {
         "down" : "sculk_sensor_bottom",
         "side" : "sculk_sensor_side",
         "up" : "sculk_sensor_top"
      }
   },
   "sculk_shrieker" : {
      "carried_textures" : "sculk_shrieker_inner_top",
      "sound" : "sculk_shrieker",
      "textures" : {
         "down" : "sculk_shrieker_bottom",
         "side" : "sculk_shrieker_side",
         "up" : "sculk_shrieker_top"
      }
   },
   "sculk_vein" : {
      "sound" : "sculk_vein",
      "textures" : "sculk_vein"
   },
   "seaLantern" : {
      "sound" : "glass",
      "textures" : "sea_lantern"
   },
   "sea_pickle" : {
      "carried_textures" : "sea_pickle_carried",
      "sound" : "slime",
      "textures" : "sea_pickle"
   },
   "seagrass" : {
      "carried_textures" : "seagrass_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "seagrass_tall_bot_a",
         "east" : "seagrass_tall_top_a",
         "north" : "seagrass_tall_top_a",
         "south" : "seagrass_tall_bot_b",
         "up" : "seagrass_short",
         "west" : "seagrass_tall_top_b"
      }
   },
   "short_dry_grass" : {
      "sound" : "grass",
      "textures" : "short_dry_grass"
   },
   "short_grass" : {
      "carried_textures" : "short_grass_carried",
      "sound" : "grass",
      "textures" : "short_grass"
   },
   "shroomlight" : {
      "sound" : "shroomlight",
      "textures" : "shroomlight"
   },
   "shulker_box" : {
      "sound" : "stone",
      "textures" : "shulker_box_top"
   },
   "silver_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "silver_glazed_terracotta"
   },
   "skeleton_skull" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "skull" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "slime" : {
      "sound" : "slime",
      "textures" : "slime_block"
   },
   "small_amethyst_bud" : {
      "sound" : "small_amethyst_bud",
      "textures" : "small_amethyst_bud"
   },
   "small_dripleaf_block" : {
      "sound" : "big_dripleaf",
      "textures" : {
         "down" : "small_dripleaf_side",
         "east" : "small_dripleaf_stem_bottom",
         "north" : "small_dripleaf_stem_top",
         "south" : "small_dripleaf_stem_bottom",
         "up" : "small_dripleaf_top",
         "west" : "small_dripleaf_stem_bottom"
      }
   },
   "smithing_table" : {
      "sound" : "wood",
      "textures" : {
         "down" : "smithing_table_bottom",
         "east" : "smithing_table_side",
         "north" : "smithing_table_front",
         "south" : "smithing_table_front",
         "up" : "smithing_table_top",
         "west" : "smithing_table_side"
      }
   },
   "smoker" : {
      "sound" : "stone",
      "textures" : {
         "down" : "smoker_bottom",
         "east" : "smoker_side",
         "north" : "smoker_side",
         "south" : "smoker_front_off",
         "up" : "smoker_top",
         "west" : "smoker_side"
      }
   },
   "smooth_basalt" : {
      "sound" : "basalt",
      "textures" : "smooth_basalt"
   },
   "smooth_quartz" : {
      "sound" : "stone",
      "textures" : "smooth_quartz"
   },
   "smooth_quartz_slab" : {
      "sound" : "stone",
      "textures" : "smooth_quartz_slab"
   },
   "smooth_quartz_stairs" : {
      "sound" : "stone",
      "textures" : "stair_smooth_quartz_block"
   },
   "smooth_red_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_redsandstone_top",
         "side" : "smooth_red_sandstone",
         "up" : "flattened_redsandstone_top"
      }
   },
   "smooth_red_sandstone_double_slab" : {
      "sound" : "stone",
      "textures" : "smooth_red_sandstone_slab"
   },
   "smooth_red_sandstone_slab" : {
      "sound" : "stone",
      "textures" : "smooth_red_sandstone_slab"
   },
   "smooth_red_sandstone_stairs" : {
      "sound" : "stone",
      "textures" : "smooth_red_sandstone"
   },
   "smooth_sandstone" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "stone",
      "textures" : {
         "down" : "flattened_sandstone_top",
         "side" : "smooth_sandstone",
         "up" : "flattened_sandstone_top"
      }
   },
   "smooth_sandstone_double_slab" : {
      "sound" : "stone",
      "textures" : "smooth_sandstone_slab"
   },
   "smooth_sandstone_slab" : {
      "sound" : "stone",
      "textures" : "smooth_sandstone_slab"
   },
   "smooth_sandstone_stairs" : {
      "sound" : "stone",
      "textures" : "smooth_sandstone"
   },
   "smooth_stone" : {
      "sound" : "stone",
      "textures" : "smooth_stone"
   },
   "smooth_stone_double_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "smooth_stone_slab_bottom",
         "side" : "smooth_stone_slab_side",
         "up" : "smooth_stone_slab_top"
      }
   },
   "smooth_stone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "smooth_stone_slab_bottom",
         "side" : "smooth_stone_slab_side",
         "up" : "smooth_stone_slab_top"
      }
   },
   "sniffer_egg" : {
      "carried_textures" : "sniffer_egg_carried",
      "sound" : "metal",
      "textures" : {
         "down" : "sniffer_egg_bottom",
         "east" : "sniffer_egg_east",
         "north" : "sniffer_egg_north",
         "south" : "sniffer_egg_south",
         "up" : "sniffer_egg_top",
         "west" : "sniffer_egg_west"
      }
   },
   "snow" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : true,
      "sound" : "snow",
      "textures" : "snow"
   },
   "snow_layer" : {
      "ambient_occlusion_exponent" : 1.0,
      "isotropic" : true,
      "sound" : "snow",
      "textures" : "snow"
   },
   "soul_campfire" : {
      "sound" : "wood",
      "textures" : {
         "down" : "campfire_log",
         "side" : "soul_campfire_log_lit",
         "up" : "soul_campfire_fire"
      }
   },
   "soul_fire" : {
      "sound" : "stone",
      "textures" : {
         "down" : "soul_fire_1",
         "east" : "soul_fire_0",
         "north" : "soul_fire_0",
         "south" : "soul_fire_0",
         "up" : "soul_fire_0",
         "west" : "soul_fire_0"
      }
   },
   "soul_lantern" : {
      "carried_textures" : "soul_lantern_carried",
      "sound" : "lantern",
      "textures" : "soul_lantern"
   },
   "soul_sand" : {
      "sound" : "soul_sand",
      "textures" : "soul_sand"
   },
   "soul_soil" : {
      "sound" : "soul_soil",
      "textures" : "soul_soil"
   },
   "soul_torch" : {
      "sound" : "wood",
      "textures" : "soul_torch"
   },
   "sponge" : {
      "isotropic" : true,
      "sound" : "sponge",
      "textures" : "flattened_sponge"
   },
   "spore_blossom" : {
      "sound" : "spore_blossom",
      "textures" : {
         "down" : "spore_blossom",
         "side" : "spore_blossom_base",
         "up" : "spore_blossom_base"
      }
   },
   "spruce_button" : {
      "sound" : "wood",
      "textures" : "spruce_planks"
   },
   "spruce_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "spruce_double_slab" : {
      "sound" : "wood",
      "textures" : "spruce_planks"
   },
   "spruce_fence" : {
      "sound" : "wood",
      "textures" : "spruce_planks"
   },
   "spruce_fence_gate" : {
      "sound" : "wood",
      "textures" : "wood_spruce"
   },
   "spruce_hanging_sign" : {
      "sound" : "hanging_sign",
      "textures" : "spruce_sign"
   },
   "spruce_leaves" : {
      "ambient_occlusion_exponent" : 0.80,
      "carried_textures" : "spruce_leaves_carried",
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "grass",
      "textures" : "spruce_leaves"
   },
   "spruce_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "spruce_log_top",
         "side" : "spruce_log_side",
         "up" : "spruce_log_top"
      }
   },
   "spruce_pressure_plate" : {
      "sound" : "wood",
      "textures" : "spruce_planks"
   },
   "spruce_sapling" : {
      "sound" : "grass",
      "textures" : "spruce_sapling"
   },
   "spruce_slab" : {
      "sound" : "wood",
      "textures" : "spruce_planks"
   },
   "spruce_stairs" : {
      "sound" : "wood",
      "textures" : "wood_spruce"
   },
   "spruce_standing_sign" : {
      "sound" : "wood",
      "textures" : "spruce_sign"
   },
   "spruce_trapdoor" : {
      "sound" : "wood",
      "textures" : "spruce_trapdoor"
   },
   "spruce_wall_sign" : {
      "sound" : "wood",
      "textures" : "spruce_sign"
   },
   "spruce_wood" : {
      "carried_textures" : "spruce_wood",
      "sound" : "wood",
      "textures" : "spruce_wood"
   },
   "stained_glass" : {
      "sound" : "glass",
      "textures" : "stained_glass"
   },
   "stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "stained_glass",
         "east" : "stained_glass_pane_top",
         "north" : "stained_glass",
         "south" : "stained_glass",
         "up" : "stained_glass",
         "west" : "stained_glass"
      }
   },
   "stained_hardened_clay" : {
      "isotropic" : true,
      "sound" : "stone",
      "textures" : "stained_clay"
   },
   "standing_banner" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "standing_sign" : {
      "sound" : "wood",
      "textures" : "sign"
   },
   "stickyPistonArmCollision" : {
      "textures" : "piston_top"
   },
   "sticky_piston" : {
      "carried_textures" : {
         "down" : "piston_bottom",
         "side" : "piston_side",
         "up" : "piston_top_sticky"
      },
      "sound" : "stone",
      "textures" : {
         "down" : "piston_bottom",
         "side" : "piston_side",
         "up" : "piston_top"
      }
   },
   "stone" : {
      "sound" : "stone",
      "textures" : "flattened_stone"
   },
   "stone_brick_double_slab" : {
      "sound" : "stone",
      "textures" : "stone_brick_slab"
   },
   "stone_brick_slab" : {
      "sound" : "stone",
      "textures" : "stone_brick_slab"
   },
   "stone_brick_stairs" : {
      "textures" : "stonebrick"
   },
   "stone_brick_wall" : {
      "sound" : "stone",
      "textures" : "stone_brick_wall"
   },
   "stone_bricks" : {
      "sound" : "stone",
      "textures" : "stone_bricks"
   },
   "stone_button" : {
      "sound" : "stone",
      "textures" : "stone"
   },
   "stone_pressure_plate" : {
      "sound" : "stone",
      "textures" : "stone"
   },
   "stone_slab" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom",
         "side" : "stone_slab_side",
         "up" : "stone_slab_top"
      }
   },
   "stone_slab2" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom_2",
         "side" : "stone_slab_side_2",
         "up" : "stone_slab_top_2"
      }
   },
   "stone_slab3" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom_3",
         "side" : "stone_slab_side_3",
         "up" : "stone_slab_top_3"
      }
   },
   "stone_slab4" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stone_slab_bottom_4",
         "side" : "stone_slab_side_4",
         "up" : "stone_slab_top_4"
      }
   },
   "stone_stairs" : {
      "textures" : "cobblestone"
   },
   "stonebrick" : {
      "sound" : "stone",
      "textures" : "stonebrick"
   },
   "stonecutter" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stonecutter_bottom",
         "east" : "stonecutter_other_side",
         "north" : "stonecutter_side",
         "south" : "stonecutter_side",
         "up" : "stonecutter_top",
         "west" : "stonecutter_other_side"
      }
   },
   "stonecutter_block" : {
      "sound" : "stone",
      "textures" : {
         "down" : "stonecutter2_bottom",
         "east" : "stonecutter2_saw",
         "north" : "stonecutter2_side",
         "south" : "stonecutter2_side",
         "up" : "stonecutter2_top",
         "west" : "stonecutter2_saw"
      }
   },
   "stripped_acacia_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_acacia_log_top",
         "side" : "stripped_acacia_log_side",
         "up" : "stripped_acacia_log_top"
      }
   },
   "stripped_acacia_wood" : {
      "carried_textures" : "stripped_acacia_wood",
      "sound" : "wood",
      "textures" : "stripped_acacia_wood"
   },
   "stripped_bamboo_block" : {
      "sound" : "bamboo_wood",
      "textures" : {
         "down" : "stripped_bamboo_block_top",
         "side" : "stripped_bamboo_block",
         "up" : "stripped_bamboo_block_top"
      }
   },
   "stripped_birch_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_birch_log_top",
         "side" : "stripped_birch_log_side",
         "up" : "stripped_birch_log_top"
      }
   },
   "stripped_birch_wood" : {
      "carried_textures" : "stripped_birch_wood",
      "sound" : "wood",
      "textures" : "stripped_birch_wood"
   },
   "stripped_cherry_log" : {
      "sound" : "cherry_wood",
      "textures" : {
         "down" : "stripped_cherry_log_top",
         "side" : "stripped_cherry_log_side",
         "up" : "stripped_cherry_log_top"
      }
   },
   "stripped_cherry_wood" : {
      "sound" : "cherry_wood",
      "textures" : "stripped_cherry_log_side"
   },
   "stripped_crimson_hyphae" : {
      "sound" : "stem",
      "textures" : {
         "down" : "stripped_crimson_stem_side",
         "east" : "stripped_crimson_stem_side",
         "north" : "stripped_crimson_stem_side",
         "south" : "stripped_crimson_stem_side",
         "up" : "stripped_crimson_stem_side",
         "west" : "stripped_crimson_stem_side"
      }
   },
   "stripped_crimson_stem" : {
      "sound" : "stem",
      "textures" : {
         "down" : "stripped_crimson_stem_top",
         "east" : "stripped_crimson_stem_side",
         "north" : "stripped_crimson_stem_side",
         "south" : "stripped_crimson_stem_side",
         "up" : "stripped_crimson_stem_top",
         "west" : "stripped_crimson_stem_side"
      }
   },
   "stripped_dark_oak_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_dark_oak_log_top",
         "side" : "stripped_dark_oak_log_side",
         "up" : "stripped_dark_oak_log_top"
      }
   },
   "stripped_dark_oak_wood" : {
      "carried_textures" : "stripped_dark_oak_wood",
      "sound" : "wood",
      "textures" : "stripped_dark_oak_wood"
   },
   "stripped_jungle_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_jungle_log_top",
         "side" : "stripped_jungle_log_side",
         "up" : "stripped_jungle_log_top"
      }
   },
   "stripped_jungle_wood" : {
      "carried_textures" : "stripped_jungle_wood",
      "sound" : "wood",
      "textures" : "stripped_jungle_wood"
   },
   "stripped_mangrove_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_mangrove_log_top",
         "side" : "stripped_mangrove_log_side",
         "up" : "stripped_mangrove_log_top"
      }
   },
   "stripped_mangrove_wood" : {
      "sound" : "wood",
      "textures" : "stripped_mangrove_log_side"
   },
   "stripped_oak_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_oak_log_top",
         "side" : "stripped_oak_log_side",
         "up" : "stripped_oak_log_top"
      }
   },
   "stripped_oak_wood" : {
      "carried_textures" : "stripped_oak_wood",
      "sound" : "wood",
      "textures" : "stripped_oak_wood"
   },
   "stripped_pale_oak_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_pale_oak_log_top",
         "side" : "stripped_pale_oak_log_side",
         "up" : "stripped_pale_oak_log_top"
      }
   },
   "stripped_pale_oak_wood" : {
      "sound" : "wood",
      "textures" : "stripped_pale_oak_log_side"
   },
   "stripped_spruce_log" : {
      "sound" : "wood",
      "textures" : {
         "down" : "stripped_spruce_log_top",
         "side" : "stripped_spruce_log_side",
         "up" : "stripped_spruce_log_top"
      }
   },
   "stripped_spruce_wood" : {
      "carried_textures" : "stripped_spruce_wood",
      "sound" : "wood",
      "textures" : "stripped_spruce_wood"
   },
   "stripped_warped_hyphae" : {
      "sound" : "stem",
      "textures" : {
         "down" : "stripped_warped_stem_side",
         "east" : "stripped_warped_stem_side",
         "north" : "stripped_warped_stem_side",
         "south" : "stripped_warped_stem_side",
         "up" : "stripped_warped_stem_side",
         "west" : "stripped_warped_stem_side"
      }
   },
   "stripped_warped_stem" : {
      "sound" : "stem",
      "textures" : {
         "down" : "stripped_warped_stem_top",
         "east" : "stripped_warped_stem_side",
         "north" : "stripped_warped_stem_side",
         "south" : "stripped_warped_stem_side",
         "up" : "stripped_warped_stem_top",
         "west" : "stripped_warped_stem_side"
      }
   },
   "structure_block" : {
      "textures" : "structure_block"
   },
   "structure_void" : {
      "textures" : "flattened_structure_void"
   },
   "sunflower" : {
      "carried_textures" : "sunflower_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "sunflower_bottom",
         "side" : "sunflower_additional",
         "up" : "sunflower_top"
      }
   },
   "suspicious_gravel" : {
      "sound" : "suspicious_gravel",
      "textures" : "suspicious_gravel"
   },
   "suspicious_sand" : {
      "ambient_occlusion_exponent" : 0.550,
      "sound" : "suspicious_sand",
      "textures" : "suspicious_sand"
   },
   "sweet_berry_bush" : {
      "carried_textures" : "sweet_berry_bush_carried",
      "sound" : "sweet_berry_bush",
      "textures" : {
         "down" : "sweet_berry_bush_0",
         "east" : "sweet_berry_bush_3",
         "north" : "sweet_berry_bush_2",
         "south" : "sweet_berry_bush_3",
         "up" : "sweet_berry_bush_1",
         "west" : "sweet_berry_bush_3"
      }
   },
   "tall_dry_grass" : {
      "sound" : "grass",
      "textures" : "tall_dry_grass"
   },
   "tall_grass" : {
      "carried_textures" : "tall_grass_carried",
      "sound" : "grass",
      "textures" : {
         "down" : "tall_grass_bottom",
         "side" : "sunflower_additional",
         "up" : "tall_grass_top"
      }
   },
   "tallgrass" : {
      "carried_textures" : "tallgrass_carried",
      "sound" : "grass",
      "textures" : "tallgrass"
   },
   "target" : {
      "sound" : "grass",
      "textures" : {
         "down" : "target_top",
         "side" : "target_side",
         "up" : "target_top"
      }
   },
   "tinted_glass" : {
      "sound" : "glass",
      "textures" : "tinted_glass"
   },
   "tnt" : {
      "sound" : "grass",
      "textures" : {
         "down" : "flattened_tnt_bottom",
         "side" : "flattened_tnt_side",
         "up" : "flattened_tnt_top"
      }
   },
   "torch" : {
      "sound" : "wood",
      "textures" : "torch_on"
   },
   "torchflower" : {
      "sound" : "grass",
      "textures" : "torchflower"
   },
   "torchflower_crop" : {
      "sound" : "grass",
      "textures" : "torchflower_crop"
   },
   "trapdoor" : {
      "sound" : "wood",
      "textures" : "trapdoor"
   },
   "trapped_chest" : {
      "sound" : "wood",
      "textures" : {
         "down" : "chest_inventory_top",
         "east" : "chest_inventory_side",
         "north" : "chest_inventory_side",
         "south" : "trapped_chest_inventory_front",
         "up" : "chest_inventory_top",
         "west" : "chest_inventory_side"
      }
   },
   "trial_spawner" : {
      "sound" : "trial_spawner",
      "textures" : {
         "down" : "trial_spawner_bottom",
         "side" : "trial_spawner_side",
         "up" : "trial_spawner_top"
      }
   },
   "tripWire" : {
      "textures" : "trip_wire"
   },
   "tripwire_hook" : {
      "textures" : {
         "down" : "trip_wire_base",
         "east" : "trip_wire",
         "north" : "trip_wire_source",
         "south" : "trip_wire",
         "up" : "trip_wire_source",
         "west" : "trip_wire"
      }
   },
   "tube_coral" : {
      "carried_textures" : "tube_coral",
      "sound" : "stone",
      "textures" : "tube_coral"
   },
   "tube_coral_block" : {
      "sound" : "stone",
      "textures" : "tube_coral_block"
   },
   "tube_coral_fan" : {
      "carried_textures" : "tube_coral_fan",
      "sound" : "stone",
      "textures" : {
         "down" : "tube_coral_fan",
         "side" : "tube_coral_fan",
         "up" : "tube_coral_fan"
      }
   },
   "tube_coral_wall_fan" : {
      "carried_textures" : "tube_coral_wall_fan",
      "sound" : "stone",
      "textures" : "tube_coral_wall_fan"
   },
   "tuff" : {
      "sound" : "tuff",
      "textures" : "tuff"
   },
   "tuff_brick_double_slab" : {
      "sound" : "tuff_bricks",
      "textures" : "tuff_bricks"
   },
   "tuff_brick_slab" : {
      "sound" : "tuff_bricks",
      "textures" : "tuff_bricks"
   },
   "tuff_brick_stairs" : {
      "sound" : "tuff_bricks",
      "textures" : "tuff_bricks"
   },
   "tuff_brick_wall" : {
      "sound" : "tuff_bricks",
      "textures" : "tuff_bricks"
   },
   "tuff_bricks" : {
      "sound" : "tuff_bricks",
      "textures" : "tuff_bricks"
   },
   "tuff_double_slab" : {
      "sound" : "tuff",
      "textures" : "tuff"
   },
   "tuff_slab" : {
      "sound" : "tuff",
      "textures" : "tuff"
   },
   "tuff_stairs" : {
      "sound" : "tuff",
      "textures" : "tuff"
   },
   "tuff_wall" : {
      "sound" : "tuff",
      "textures" : "tuff"
   },
   "turtle_egg" : {
      "carried_textures" : "turtle_egg_carried",
      "sound" : "turtle_egg",
      "textures" : "turtle_egg"
   },
   "twisting_vines" : {
      "sound" : "weeping_vines",
      "textures" : {
         "down" : "twisting_vines_bottom",
         "east" : "twisting_vines_base",
         "north" : "twisting_vines_base",
         "south" : "twisting_vines_base",
         "up" : "twisting_vines_base",
         "west" : "twisting_vines_base"
      }
   },
   "undyed_shulker_box" : {
      "sound" : "stone",
      "textures" : "undyed_shulker_box_top"
   },
   "unlit_redstone_torch" : {
      "sound" : "wood",
      "textures" : "redstone_torch_off"
   },
   "unpowered_comparator" : {
      "sound" : "comparator",
      "textures" : {
         "down" : "comparator_stone_slab",
         "side" : "comparator_stone_slab",
         "up" : "comparator_up"
      }
   },
   "unpowered_repeater" : {
      "sound" : "wood",
      "textures" : {
         "down" : "repeater_floor",
         "side" : "repeater_floor",
         "up" : "repeater_up"
      }
   },
   "vault" : {
      "sound" : "vault",
      "textures" : {
         "down" : "vault_bottom",
         "east" : "vault_side",
         "north" : "vault_front",
         "south" : "vault_side",
         "up" : "vault_top",
         "west" : "vault_side"
      }
   },
   "verdant_froglight" : {
      "sound" : "froglight",
      "textures" : {
         "down" : "verdant_froglight_top",
         "side" : "verdant_froglight_side",
         "up" : "verdant_froglight_top"
      }
   },
   "vine" : {
      "carried_textures" : "vine_carried",
      "sound" : "vines",
      "textures" : "vine"
   },
   "wall_banner" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "wall_sign" : {
      "sound" : "wood",
      "textures" : "sign"
   },
   "warped_button" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_door" : {
      "sound" : "nether_wood",
      "textures" : {
         "down" : "warped_door_lower",
         "side" : "warped_door_top",
         "up" : "warped_door_lower"
      }
   },
   "warped_double_slab" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_fence" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_fence_gate" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_fungus" : {
      "sound" : "fungus",
      "textures" : "nether_shroom_blue"
   },
   "warped_hanging_sign" : {
      "sound" : "nether_wood_hanging_sign",
      "textures" : "warped_sign"
   },
   "warped_hyphae" : {
      "sound" : "stem",
      "textures" : {
         "down" : "warped_stem_side",
         "east" : "warped_stem_side",
         "north" : "warped_stem_side",
         "south" : "warped_stem_side",
         "up" : "warped_stem_side",
         "west" : "warped_stem_side"
      }
   },
   "warped_nylium" : {
      "isotropic" : {
         "down" : true,
         "up" : true
      },
      "sound" : "nylium",
      "textures" : {
         "down" : "netherrack",
         "east" : "warped_nylium_side",
         "north" : "warped_nylium_side",
         "south" : "warped_nylium_side",
         "up" : "warped_nylium_top",
         "west" : "warped_nylium_side"
      }
   },
   "warped_planks" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_pressure_plate" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_roots" : {
      "sound" : "roots",
      "textures" : {
         "down" : "warped_roots",
         "east" : "warped_roots",
         "north" : "warped_roots",
         "south" : "warped_roots_pot",
         "up" : "warped_roots",
         "west" : "warped_roots"
      }
   },
   "warped_slab" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_stairs" : {
      "sound" : "nether_wood",
      "textures" : "warped_planks"
   },
   "warped_standing_sign" : {
      "sound" : "nether_wood",
      "textures" : "warped_sign"
   },
   "warped_stem" : {
      "sound" : "stem",
      "textures" : {
         "down" : "warped_stem_top",
         "east" : "warped_stem_side",
         "north" : "warped_stem_side",
         "south" : "warped_stem_side",
         "up" : "warped_stem_top",
         "west" : "warped_stem_side"
      }
   },
   "warped_trapdoor" : {
      "sound" : "nether_wood",
      "textures" : "warped_trapdoor"
   },
   "warped_wall_sign" : {
      "sound" : "nether_wood",
      "textures" : "warped_sign"
   },
   "warped_wart_block" : {
      "sound" : "nether_wart",
      "textures" : "warped_wart_block"
   },
   "water" : {
      "textures" : {
         "down" : "still_water_grey",
         "side" : "flowing_water_grey",
         "up" : "still_water_grey"
      }
   },
   "waterlily" : {
      "carried_textures" : "waterlily_carried",
      "sound" : "grass",
      "textures" : "waterlily"
   },
   "waxed_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "chiseled_copper"
   },
   "waxed_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "copper_block"
   },
   "waxed_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "copper_bulb"
   },
   "waxed_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "copper_door_bottom",
         "side" : "copper_door_top",
         "up" : "copper_door_bottom"
      }
   },
   "waxed_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "copper_grate"
   },
   "waxed_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "copper_trapdoor"
   },
   "waxed_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "waxed_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "waxed_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "waxed_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "cut_copper"
   },
   "waxed_exposed_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "exposed_chiseled_copper"
   },
   "waxed_exposed_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_copper"
   },
   "waxed_exposed_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "exposed_copper_bulb"
   },
   "waxed_exposed_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "exposed_copper_door_bottom",
         "side" : "exposed_copper_door_top",
         "up" : "exposed_copper_door_bottom"
      }
   },
   "waxed_exposed_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "exposed_copper_grate"
   },
   "waxed_exposed_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "exposed_copper_trapdoor"
   },
   "waxed_exposed_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "waxed_exposed_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "waxed_exposed_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "waxed_exposed_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "exposed_cut_copper"
   },
   "waxed_oxidized_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "oxidized_chiseled_copper"
   },
   "waxed_oxidized_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_copper"
   },
   "waxed_oxidized_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "oxidized_copper_bulb"
   },
   "waxed_oxidized_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "oxidized_copper_door_bottom",
         "side" : "oxidized_copper_door_top",
         "up" : "oxidized_copper_door_bottom"
      }
   },
   "waxed_oxidized_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "oxidized_copper_grate"
   },
   "waxed_oxidized_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "oxidized_copper_trapdoor"
   },
   "waxed_oxidized_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "waxed_oxidized_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "waxed_oxidized_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "waxed_oxidized_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "oxidized_cut_copper"
   },
   "waxed_weathered_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "weathered_chiseled_copper"
   },
   "waxed_weathered_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_copper"
   },
   "waxed_weathered_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "weathered_copper_bulb"
   },
   "waxed_weathered_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "weathered_copper_door_bottom",
         "side" : "weathered_copper_door_top",
         "up" : "weathered_copper_door_bottom"
      }
   },
   "waxed_weathered_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "weathered_copper_grate"
   },
   "waxed_weathered_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "weathered_copper_trapdoor"
   },
   "waxed_weathered_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "waxed_weathered_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "waxed_weathered_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "waxed_weathered_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "weathered_chiseled_copper" : {
      "sound" : "copper",
      "textures" : "weathered_chiseled_copper"
   },
   "weathered_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_copper"
   },
   "weathered_copper_bulb" : {
      "isotropic" : false,
      "sound" : "copper_bulb",
      "textures" : "weathered_copper_bulb"
   },
   "weathered_copper_door" : {
      "sound" : "copper",
      "textures" : {
         "down" : "weathered_copper_door_bottom",
         "side" : "weathered_copper_door_top",
         "up" : "weathered_copper_door_bottom"
      }
   },
   "weathered_copper_grate" : {
      "sound" : "copper_grate",
      "textures" : "weathered_copper_grate"
   },
   "weathered_copper_trapdoor" : {
      "sound" : "copper",
      "textures" : "weathered_copper_trapdoor"
   },
   "weathered_cut_copper" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "weathered_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "weathered_cut_copper_stairs" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "weathered_double_cut_copper_slab" : {
      "isotropic" : false,
      "sound" : "copper",
      "textures" : "weathered_cut_copper"
   },
   "web" : {
      "sound" : "web",
      "textures" : "web"
   },
   "weeping_vines" : {
      "sound" : "weeping_vines",
      "textures" : {
         "down" : "weeping_vines_bottom",
         "east" : "weeping_vines_base",
         "north" : "weeping_vines_base",
         "south" : "weeping_vines_base",
         "up" : "weeping_vines_base",
         "west" : "weeping_vines_base"
      }
   },
   "wet_sponge" : {
      "isotropic" : true,
      "sound" : "wet_sponge",
      "textures" : "wet_sponge"
   },
   "wheat" : {
      "sound" : "grass",
      "textures" : "wheat"
   },
   "white_candle" : {
      "carried_textures" : "white_candle_carried",
      "sound" : "candle",
      "textures" : "white_candle"
   },
   "white_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "white_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_white"
   },
   "white_concrete" : {
      "sound" : "stone",
      "textures" : "white_concrete"
   },
   "white_concrete_powder" : {
      "sound" : "sand",
      "textures" : "white_concrete_powder"
   },
   "white_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "white_glazed_terracotta"
   },
   "white_shulker_box" : {
      "sound" : "stone",
      "textures" : "white_shulker_box"
   },
   "white_stained_glass" : {
      "sound" : "glass",
      "textures" : "white_stained_glass"
   },
   "white_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "white_stained_glass",
         "east" : "white_stained_glass_pane_top",
         "north" : "white_stained_glass",
         "south" : "white_stained_glass",
         "up" : "white_stained_glass",
         "west" : "white_stained_glass"
      }
   },
   "white_terracotta" : {
      "sound" : "terracotta",
      "textures" : "white_terracotta"
   },
   "white_tulip" : {
      "sound" : "grass",
      "textures" : "white_tulip"
   },
   "white_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_white"
   },
   "wildflowers" : {
      "carried_textures" : "wildflowers_carried",
      "sound" : "pink_petals",
      "textures" : "wildflowers"
   },
   "wither_rose" : {
      "sound" : "grass",
      "textures" : "wither_rose"
   },
   "wither_skeleton_skull" : {
      "sound" : "stone",
      "textures" : "skull"
   },
   "wood" : {
      "sound" : "wood",
      "textures" : "wood"
   },
   "wooden_button" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "wooden_door" : {
      "sound" : "wood",
      "textures" : {
         "down" : "door_lower",
         "side" : "door_upper",
         "up" : "door_lower"
      }
   },
   "wooden_pressure_plate" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "wooden_slab" : {
      "sound" : "wood",
      "textures" : "planks"
   },
   "wool" : {
      "sound" : "cloth",
      "textures" : "wool"
   },
   "yellow_candle" : {
      "carried_textures" : "yellow_candle_carried",
      "sound" : "candle",
      "textures" : "yellow_candle"
   },
   "yellow_candle_cake" : {
      "sound" : "cloth",
      "textures" : {
         "down" : "cake_bottom",
         "east" : "cake_side",
         "north" : "cake_side",
         "south" : "cake_side",
         "up" : "cake_top",
         "west" : "cake_west"
      }
   },
   "yellow_carpet" : {
      "sound" : "cloth",
      "textures" : "wool_colored_yellow"
   },
   "yellow_concrete" : {
      "sound" : "stone",
      "textures" : "yellow_concrete"
   },
   "yellow_concrete_powder" : {
      "sound" : "sand",
      "textures" : "yellow_concrete_powder"
   },
   "yellow_flower" : {
      "sound" : "grass",
      "textures" : "yellow_flower"
   },
   "yellow_glazed_terracotta" : {
      "sound" : "stone",
      "textures" : "yellow_glazed_terracotta"
   },
   "yellow_shulker_box" : {
      "sound" : "stone",
      "textures" : "yellow_shulker_box"
   },
   "yellow_stained_glass" : {
      "sound" : "glass",
      "textures" : "yellow_stained_glass"
   },
   "yellow_stained_glass_pane" : {
      "sound" : "glass",
      "textures" : {
         "down" : "yellow_stained_glass",
         "east" : "yellow_stained_glass_pane_top",
         "north" : "yellow_stained_glass",
         "south" : "yellow_stained_glass",
         "up" : "yellow_stained_glass",
         "west" : "yellow_stained_glass"
      }
   },
   "yellow_terracotta" : {
      "sound" : "terracotta",
      "textures" : "yellow_terracotta"
   },
   "yellow_wool" : {
      "sound" : "cloth",
      "textures" : "wool_colored_yellow"
   },
   "zombie_head" : {
      "sound" : "stone",
      "textures" : "skull"
   }
}
