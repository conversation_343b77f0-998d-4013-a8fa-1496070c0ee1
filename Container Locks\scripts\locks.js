import { world, system, Scoreboard, Player, Entity, EnchantmentType, ItemComponentTypes, ItemStack, EntityInventoryComponent } from "@minecraft/server";
import { ActionFormData, ModalFormData, FormCancelationReason, MessageFormData } from "@minecraft/server-ui";
import { SerializableKinds, DynamicTable, Serializer, JsonDatabase } from "./extensions/con-database";
import './logging.js';
import './playerEnderChest.js';
import './moneyWithdrawal.js';


const LocksDB = new JsonDatabase("LocksDB");

// Store the last time a lock/unlock message was sent for each player
const messageCooldowns = new Map();

function toggleContainer(player, block) {
    const blockPosition = `${block.x},${block.y},${block.z}`;
    const owner = LocksDB.get(blockPosition);
    
    // Debounce logic: Check if enough time has passed since the last message
    const currentTime = Date.now();
    const lastMessageTime = messageCooldowns.get(player.id) || 0;
    const cooldownDuration = 1000; // 1 second cooldown (in milliseconds)

    if (owner) {
        // Container is locked - attempt to unlock
        if (owner === player.name) {
            LocksDB.delete(blockPosition);
            if (currentTime - lastMessageTime >= cooldownDuration) {
                player.sendMessage('Container unlocked!');
                messageCooldowns.set(player.id, currentTime);
            }
        } else {
            if (currentTime - lastMessageTime >= cooldownDuration) {
                player.sendMessage('You are not the owner of this container!');
                messageCooldowns.set(player.id, currentTime);
            }
        }
    } else {
        // Container is unlocked - lock it
        LocksDB.set(blockPosition, player.name);
        if (currentTime - lastMessageTime >= cooldownDuration) {
            player.sendMessage('Container locked!');
            messageCooldowns.set(player.id, currentTime);
        }
    }
}

function canAccessContainer(player, block) {
    const blockPosition = `${block.x},${block.y},${block.z}`;
    const owner = LocksDB.get(blockPosition);
    return !owner || owner === player.name;
}

// Clean up messageCooldowns when a player leaves
world.afterEvents.playerLeave.subscribe(event => {
    messageCooldowns.delete(event.playerId);
});

world.beforeEvents.itemUseOn.subscribe(data => {
    const player = data.source;
    const block = data.block;
    const itemStack = data.itemStack;

    const lockableTypes = [
        "minecraft:chest",
        "minecraft:barrel",
        "minecraft:undyed_shulker_box",
        "minecraft:black_shulker_box",
        "minecraft:blue_shulker_box",
        "minecraft:brown_shulker_box",
        "minecraft:cyan_shulker_box",
        "minecraft:gray_shulker_box",
        "minecraft:green_shulker_box",
        "minecraft:light_blue_shulker_box",
        "minecraft:light_gray_shulker_box",
        "minecraft:lime_shulker_box",
        "minecraft:magenta_shulker_box",
        "minecraft:orange_shulker_box",
        "minecraft:pink_shulker_box",
        "minecraft:purple_shulker_box",
        "minecraft:red_shulker_box",
        "minecraft:white_shulker_box",
        "minecraft:yellow_shulker_box"
    ];
    
    const currentTime = Date.now();
    const lastMessageTime = messageCooldowns.get(player.id) || 0;
    const cooldownDuration = 1000; // 1 second cooldown (in milliseconds)

    if (itemStack.typeId === "ninjos:lock" && lockableTypes.includes(block.typeId)) {
        toggleContainer(player, block);
        data.cancel = true;
    }

    // Master key functionality with debounce
    if (itemStack.typeId === "ninjos:masterlock" && lockableTypes.includes(block.typeId)) {
        if (player.hasTag("Admin")) {
            const blockPosition = `${block.x},${block.y},${block.z}`;
            const owner = LocksDB.get(blockPosition);
            if (owner) {
                LocksDB.delete(blockPosition);
                if (currentTime - lastMessageTime >= cooldownDuration) {
                    player.sendMessage('Container unlocked with the master key!');
                    messageCooldowns.set(player.id, currentTime);
                }
            } else {
                if (currentTime - lastMessageTime >= cooldownDuration) {
                    player.sendMessage('This container is already unlocked!');
                    messageCooldowns.set(player.id, currentTime);
                }
            }
        } else {
            if (currentTime - lastMessageTime >= cooldownDuration) {
                player.sendMessage('You do not have permission to use the master key.');
                messageCooldowns.set(player.id, currentTime);
            }
        }
        data.cancel = true;
    }
});

world.beforeEvents.playerBreakBlock.subscribe(event => {
    const player = event.player;
    const block = event.block;

    const lockableTypes = [
        "minecraft:chest",
        "minecraft:barrel",
        "minecraft:undyed_shulker_box",
        "minecraft:black_shulker_box",
        "minecraft:blue_shulker_box",
        "minecraft:brown_shulker_box",
        "minecraft:cyan_shulker_box",
        "minecraft:gray_shulker_box",
        "minecraft:green_shulker_box",
        "minecraft:light_blue_shulker_box",
        "minecraft:light_gray_shulker_box",
        "minecraft:lime_shulker_box",
        "minecraft:magenta_shulker_box",
        "minecraft:orange_shulker_box",
        "minecraft:pink_shulker_box",
        "minecraft:purple_shulker_box",
        "minecraft:red_shulker_box",
        "minecraft:white_shulker_box",
        "minecraft:yellow_shulker_box"
    ];
    
    if (lockableTypes.includes(block.typeId)) {
        const blockPosition = `${block.x},${block.y},${block.z}`;
        const owner = LocksDB.get(blockPosition);

        if (owner && owner === player.name) {
            LocksDB.delete(blockPosition);
        } else if (owner && player.hasTag("Admin")) {
            // Allow Admins to break locked containers
            LocksDB.delete(blockPosition);
            player.sendMessage(`You have broken a container owned by ${owner}.`);
        } else if (!owner) {
            return;
        } else {
            event.cancel = true;
            player.sendMessage(`You are not allowed to break this container. It belongs to ${owner}.`);
        }
    }
});

world.beforeEvents.playerInteractWithBlock.subscribe(event => {
    const player = event.player;
    const block = event.block;

    const lockableTypes = [
        "minecraft:chest",
        "minecraft:barrel",
        "minecraft:undyed_shulker_box",
        "minecraft:black_shulker_box",
        "minecraft:blue_shulker_box",
        "minecraft:brown_shulker_box",
        "minecraft:cyan_shulker_box",
        "minecraft:gray_shulker_box",
        "minecraft:green_shulker_box",
        "minecraft:light_blue_shulker_box",
        "minecraft:light_gray_shulker_box",
        "minecraft:lime_shulker_box",
        "minecraft:magenta_shulker_box",
        "minecraft:orange_shulker_box",
        "minecraft:pink_shulker_box",
        "minecraft:purple_shulker_box",
        "minecraft:red_shulker_box",
        "minecraft:white_shulker_box",
        "minecraft:yellow_shulker_box"
    ];
    
    if (lockableTypes.includes(block.typeId) && !canAccessContainer(player, block)) {
        if (player.hasTag("Admin")) {
            // Allow Admins to access locked containers
            player.sendMessage(`Accessing container owned by ${LocksDB.get(`${block.x},${block.y},${block.z}`)}.`);
        } else {
            event.cancel = true;
            player.sendMessage(`You are not allowed to open this container. It belongs to ${LocksDB.get(`${block.x},${block.y},${block.z}`)}.`);
        }
    }
});



