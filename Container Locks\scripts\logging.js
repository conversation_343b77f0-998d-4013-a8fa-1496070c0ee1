import { world } from "@minecraft/server";
import { ActionFormData, MessageFormData } from "@minecraft/server-ui";
import { JsonDatabase } from "./extensions/con-database";

// Database for theft attempt logs
const TheftLogDB = new JsonDatabase("TheftLogDB");
// Database for container locks (same as in main script)
const LocksDB = new JsonDatabase("LocksDB");

function isAdmin(player) {
    return player.hasTag("Admin");
}

// Function to log theft attempts
function logTheftAttempt(thief, owner, block) {
    const timestamp = new Date().toISOString();
    const blockPosition = `${block.x},${block.y},${block.z}`;
    const logEntry = {
        thief: thief.name,
        owner: owner,
        blockPosition: blockPosition,
        timestamp: timestamp
    };
    
    const currentLogs = TheftLogDB.get("theftAttempts") || [];
    currentLogs.push(logEntry);
    TheftLogDB.set("theftAttempts", currentLogs);
}

// Function to get unique thieves from logs
function getUniqueThieves() {
    const logs = TheftLogDB.get("theftAttempts") || [];
    return [...new Set(logs.map(log => log.thief))];
}

// Function to get all logs for a specific player
function getPlayerLogs(playerName) {
    const logs = TheftLogDB.get("theftAttempts") || [];
    return logs.filter(log => log.thief === playerName);
}

// Function to clear logs for a specific player
function clearPlayerLogs(playerName) {
    const logs = TheftLogDB.get("theftAttempts") || [];
    const filteredLogs = logs.filter(log => log.thief !== playerName);
    TheftLogDB.set("theftAttempts", filteredLogs);
}

// Function to clear all theft logs
function clearAllLogs() {
    TheftLogDB.set("theftAttempts", []);
}

// Function to clear all container locks
function clearAllContainerLocks(player) {
    LocksDB.clear(); // Clear the LocksDB database
    player.sendMessage("§aAll container locks have been cleared.");
}

// Function to show player-specific logs
function showPlayerLogs(player, thiefName) {
    const logs = getPlayerLogs(thiefName);
    const form = new ActionFormData()
        .title(`Logs for ${thiefName}`)
        .button("Back");

    let logText = `Theft Attempts by ${thiefName}:\n\n`;
    logs.forEach((log, index) => {
        logText += `${index + 1}. Container owned by: ${log.owner}\n   Position: ${log.blockPosition}\n   Time: ${log.timestamp}\n\n`;
    });
    form.body(logText);

    form.show(player).then(response => {
        if (response.selection === 0) {
            showTheftLogs(player);
        }
    });
}

// Function to show clear logs menu
function showClearLogsMenu(player) {
    const form = new ActionFormData()
        .title("Clear Theft Logs")
        .body("Select a player to clear their logs, or clear all logs:")
        .button("Back to Main Menu")
        .button("Clear ALL Logs");

    const thieves = getUniqueThieves();
    thieves.forEach(thief => {
        form.button(`Clear ${thief}'s Logs`);
    });

    form.show(player).then(response => {
        if (response.canceled) return;
        if (response.selection === 0) {
            showTheftLogs(player);
        } else if (response.selection === 1) {
            clearAllLogs();
            player.sendMessage("§aAll theft logs have been cleared.");
            showTheftLogs(player);
        } else if (response.selection) {
            const selectedThief = thieves[response.selection - 2];
            clearPlayerLogs(selectedThief);
            player.sendMessage(`§aLogs for ${selectedThief} have been cleared.`);
            showTheftLogs(player);
        }
    });
}

// Updated function to show theft logs with lock clearing option for Owners
function showTheftLogs(player) {
    const form = new ActionFormData()
        .title("Theft Attempts Log")
        .button("Close")
        .button("Clear Logs");

    // Add button to clear all container locks for players with Owner tag
    if (player.hasTag("Owner")) {
        form.button("Clear All Container Locks");
    }

    const thieves = getUniqueThieves();
    thieves.forEach(thief => {
        form.button(`View ${thief}'s Attempts`);
    });

    form.show(player).then(response => {
        if (response.canceled) return;
        if (response.selection === 1) {
            showClearLogsMenu(player);
        } else if (player.hasTag("Owner") && response.selection === 2) {
            // Show confirmation dialog for clearing locks
            const confirmForm = new MessageFormData()
                .title("Confirm Clear All Locks")
                .body("Are you sure you want to unlock ALL containers? This cannot be undone.")
                .button1("Cancel")
                .button2("Confirm");
            confirmForm.show(player).then(confirmResponse => {
                if (!confirmResponse.canceled && confirmResponse.selection === 1) {
                    clearAllContainerLocks(player);
                    showTheftLogs(player);
                } else {
                    showTheftLogs(player);
                }
            });
        } else if (response.selection && response.selection >= (player.hasTag("Owner") ? 3 : 2)) {
            const selectedThief = thieves[response.selection - (player.hasTag("Owner") ? 3 : 2)];
            showPlayerLogs(player, selectedThief);
        }
    });
}

world.afterEvents.itemUse.subscribe((eventData) => {
    const player = eventData.source;
    if (eventData.itemStack?.typeId === "ninjos:masterlock" && isAdmin(player) && player.isSneaking) {
        showTheftLogs(player);
    }
});

// Modify the existing playerInteractWithBlock event to log theft attempts
world.beforeEvents.playerInteractWithBlock.subscribe(event => {
    const player = event.player;
    const block = event.block;

    const lockableTypes = [
        "minecraft:chest",
        "minecraft:barrel",
        "minecraft:undyed_shulker_box",
        "minecraft:black_shulker_box",
        "minecraft:blue_shulker_box",
        "minecraft:brown_shulker_box",
        "minecraft:cyan_shulker_box",
        "minecraft:gray_shulker_box",
        "minecraft:green_shulker_box",
        "minecraft:light_blue_shulker_box",
        "minecraft:light_gray_shulker_box",
        "minecraft:lime_shulker_box",
        "minecraft:magenta_shulker_box",
        "minecraft:orange_shulker_box",
        "minecraft:pink_shulker_box",
        "minecraft:purple_shulker_box",
        "minecraft:red_shulker_box",
        "minecraft:white_shulker_box",
        "minecraft:yellow_shulker_box"
    ];

    if (lockableTypes.includes(block.typeId)) {
        const blockPosition = `${block.x},${block.y},${block.z}`;
        const owner = LocksDB.get(blockPosition);

        if (owner && owner !== player.name && !player.hasTag("Admin")) {
            event.cancel = true;
            player.sendMessage(`You are not allowed to open this container. It belongs to ${owner}.`);
            logTheftAttempt(player, owner, block);
        } else if (owner && player.hasTag("Admin")) {
            player.sendMessage(`Accessing container owned by ${owner}.`);
        }
    }
});