import { Player, system, world } from "@minecraft/server";
import { ModalFormData } from "@minecraft/server-ui";

const moneyObjective = "Money";

const coinConfig = [
  { name: "ninjos:coin_netherite", points: 1000000, displayName: "§8Netherite OG Coin §f$1,000,000" },
  { name: "ninjos:coin_diamond", points: 10000, displayName: "§bDiamond OG Coin §f$10,000" },
  { name: "ninjos:coin_gold", points: 1000, displayName: "§eGolden OG Coin §f$1,000" },
  { name: "ninjos:coin_ruby", points: 500, displayName: "§cRuby OG Coin §f$500" },
  { name: "ninjos:coin_emerald", points: 100, displayName: "§2Emerald OG Coin §f$100" },
  { name: "ninjos:coin_iron", points: 25, displayName: "§8Iron OG Coin §f$25" },
  { name: "ninjos:coin_glass", points: 5, displayName: "§7Glass OG Coin §f$5" },
  { name: "ninjos:coin_wood", points: 1, displayName: "§6Wooden OG Coin §f$1" },
  { name: "zombie:zcoin", points: 1, displayName: "§dOG Coins" }
];

/**
 * Returns the player's current Money score.
 * @param {Player} player 
 * @returns {number}
 */
function Getmoney(player) {
  const objective = world.scoreboard.getObjective(moneyObjective);
  if (!objective.hasParticipant(player)) return 0;
  return objective.getScore(player);
}

/**
 * Gets the number of empty slots in player's inventory
 * @param {Player} player 
 * @returns {number}
 */
function getEmptySlots(player) {
  const inventory = player.getComponent("minecraft:inventory").container;
  let emptySlots = 0;
  for (let i = 0; i < inventory.size; i++) {
    if (inventory.getItem(i) === undefined) {
      emptySlots++;
    }
  }
  return emptySlots;
}

/**
 * Gets the maximum stack size possible given current inventory
 * @param {Player} player 
 * @param {number} amount 
 * @returns {number}
 */
function getMaxWithdrawable(player, amount) {
  const inventory = player.getComponent("minecraft:inventory").container;
  const maxStack = 64; // Assuming all coins have a max stack size of 64
  let availableSpace = getEmptySlots(player) * maxStack;

  // Check existing coin stacks that aren't full
  for (let i = 0; i < inventory.size; i++) {
    const item = inventory.getItem(i);
    if (item && coinConfig.some(coin => coin.name === item.typeId)) {
      availableSpace += maxStack - item.amount;
    }
  }

  // Calculate how many coins are needed to withdraw the amount
  let remainingAmount = amount;
  let totalCoinsNeeded = 0;

  for (const coin of coinConfig) {
    const coinCount = Math.floor(remainingAmount / coin.points);
    remainingAmount -= coinCount * coin.points;
    totalCoinsNeeded += coinCount;
  }

  // Adjust based on inventory space
  return Math.min(amount, Math.floor(availableSpace / totalCoinsNeeded) * amount);
}

/**
 * Calculates the optimal coin distribution for a given amount
 * @param {number} amount 
 * @returns {Array<{name: string, count: number}>}
 */
function calculateCoinDistribution(amount) {
  let remaining = amount;
  const distribution = [];

  for (const coin of coinConfig) {
    const coinCount = Math.floor(remaining / coin.points);
    if (coinCount > 0) {
      distribution.push({ name: coin.name, count: coinCount });
      remaining -= coinCount * coin.points;
    }
  }

  return distribution;
}

world.afterEvents.entityHitEntity.subscribe((evd) => {
  const { hitEntity, damagingEntity } = evd;

  if (!damagingEntity || damagingEntity.typeId !== "minecraft:player") return;

  if (
    hitEntity &&
    hitEntity.typeId === "npc:npc_humans" &&
    hitEntity.hasTag("withdrawal")
  ) {
    const player = damagingEntity;

    if (Getmoney(player) <= 0) {
      return player.runCommandAsync(
        `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cYou don't have enough OG Coins to withdraw!"}]}`
      );
    }

    player.runCommandAsync(
      `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §6How Much Would You Like To Withdraw? "}]}`
    );

    system.runTimeout(() => {
      const modal = new ModalFormData()
        .title("§r§aMoney §eWithdrawal§r")
        .textField("How much money do you want to withdraw?", "Enter a number", "0");

      modal.show(player).then((res) => {
        // Check if form was canceled or response is invalid
        if (!res || res.canceled) {
          return player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cWithdrawal canceled!"}]}`
          );
        }

        // Check if formValues exists and has data
        if (!res.formValues || res.formValues.length === 0) {
          return player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cInvalid input!"}]}`
          );
        }

        let regex = /[^0-9]/g;
        if (regex.test(res.formValues[0])) {
          return player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cOnly numbers are allowed!"}]}`
          );
        }

        let requestedWithdrawal = parseInt(res.formValues[0]);
        if (requestedWithdrawal <= 0) {
          return player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cPlease enter a number greater than 0!"}]}`
          );
        }

        const currentMoney = Getmoney(player);
        if (requestedWithdrawal > currentMoney) {
          return player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cYou do not have enough OG Coins!"}]}`
          );
        }

        // Calculate maximum withdrawable amount
        const maxFit = getMaxWithdrawable(player, requestedWithdrawal);

        // If requested amount exceeds inventory space, adjust it
        let actualWithdrawal = requestedWithdrawal;
        if (requestedWithdrawal > maxFit) {
          actualWithdrawal = maxFit;
          player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §eYour inventory can only hold ${maxFit} worth of coins. Withdrawing maximum possible amount."}]}`
          );
        }

        try {
          const objective = world.scoreboard.getObjective(moneyObjective);
          objective.addScore(player, -actualWithdrawal);

          // Calculate coin distribution
          const coinDistribution = calculateCoinDistribution(actualWithdrawal);

          // Give the coins
          let withdrawalMessage = "§r§8[§aMoney §eWithdrawal§8] §aYou successfully withdrew: ";
          let firstItem = true;

          for (const { name, count } of coinDistribution) {
            if (count > 0) {
              const maxStack = 64;
              let remainingCount = count;

              while (remainingCount > 0) {
                const stackSize = Math.min(remainingCount, maxStack);
                player.runCommandAsync(`give @s ${name} ${stackSize}`);
                remainingCount -= stackSize;
              }

              const coinInfo = coinConfig.find(coin => coin.name === name);
              withdrawalMessage += `${firstItem ? "" : ", "}${count} ${coinInfo.displayName}`;
              firstItem = false;
            }
          }

          player.runCommandAsync(`tellraw @s {"rawtext":[{"text":"${withdrawalMessage}"}]}`);
        } catch (error) {
          player.runCommandAsync(
            `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cError processing withdrawal: ${error.message}"}]}`
          );
          // Add back the withdrawn amount if the command fails
          const objective = world.scoreboard.getObjective(moneyObjective);
          objective.addScore(player, actualWithdrawal);
        }
      }).catch((error) => {
        player.runCommandAsync(
          `tellraw @s {"rawtext":[{"text":"§r§8[§aMoney §eWithdrawal§8] §cError displaying form: ${error.message}"}]}`
        );
      });
    }, 2);
  }
});