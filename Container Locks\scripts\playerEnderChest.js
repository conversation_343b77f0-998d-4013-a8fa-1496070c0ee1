import { world, system, ItemStack, ItemTypes } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";

// Log script initialization
console.log("Ninjos Masterlock Addon script loaded");

// Subscribe to itemUseOn event
world.beforeEvents.itemUseOn.subscribe((eventData) => {
  const player = eventData.source;
  const item = eventData.itemStack;
  const block = eventData.block;



  // Check conditions: Admin tag, holding ninjos:masterlock, targeting ender chest
  if (!player.hasTag("Admin")) {
    return;
  }
  if (!item || item.typeId !== "ninjos:masterlock") {
    return;
  }
  if (block.typeId !== "minecraft:ender_chest") {
    return;
  }

  // Cancel default ender chest opening
  eventData.cancel = true;

  console.log(`Valid interaction by ${player.name}`);
  system.run(() => showPlayerListForm(player));
});

// Get enchantments from an item
function GetEnchants(item) {
  if (!item || !item.hasComponent("minecraft:enchantable")) return [];

  try {
    const enchantComponent = item.getComponent("minecraft:enchantable");
    if (!enchantComponent) return [];
    const enchantments = enchantComponent.getEnchantments();
    return enchantments.map((enchantment) => ({
      type: enchantment.type.id.replace("minecraft:", ""),
      level: enchantment.level,
    }));
  } catch (error) {
    console.error(`Error retrieving enchantments: ${error}`);
    return [];
  }
}

// EnderChestInspector class adapted from example
class EnderChestInspector {
  constructor(player) {
    this.player = player;
  }

  runCommand(command) {
    try {
      const commandResult = this.player.runCommand(command);
      return commandResult.successCount > 0;
    } catch (err) {
      console.warn(`Command failed: ${command}, Error: ${err}`);
      return false;
    }
  }

  hasAnyOfItem(item) {
    return this.runCommand(`testfor @s[hasitem={location=slot.enderchest,item=${item.id}}]`);
  }

  hasAtLeastItemQuantityInSlot(slot, item, quantity) {
    return this.runCommand(
      `testfor @s[hasitem={location=slot.enderchest,slot=${slot},item=${item.id},quantity=${quantity}..}]`
    );
  }

  async getEnderChestContents() {
    console.log(`Fetching ender chest contents for ${this.player.name}`);
    const enderChestContents = new Map();
    const foundTypes = new Set();

    for (const itemType of ItemTypes.getAll()) {
      if (this.hasAnyOfItem(itemType)) {
        foundTypes.add(itemType);
      }
    }

    for (let slot = 0; slot < 27; slot++) {
      for (const itemType of foundTypes) {
        if (!this.hasAtLeastItemQuantityInSlot(slot, itemType, 1)) continue;

        const qty = this.bisectExactQuantity(slot, new ItemStack(itemType));
        if (qty > 0) {
          const constructedItem = new ItemStack(itemType, qty);
          enderChestContents.set(slot, {
            amount: qty,
            slot,
            item: constructedItem,
            enchantments: [], // Placeholder: Enchantments not detectable
          });
        }
      }
    }

    console.warn(`Enchantment data unavailable for ${this.player.name}'s ender chest due to API limitations`);
    return enderChestContents;
  }

  bisectExactQuantity(slot, item) {
    let low = 1;
    let high = item.maxAmount;

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      if (this.hasAtLeastItemQuantityInSlot(slot, item.type, mid)) {
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }

    return high;
  }
}

// Show player list form
function showPlayerListForm(player) {
  console.log(`Showing player list for ${player.name}`);
  const form = new ActionFormData()
    .title("Online Players")
    .body("Select a player to manage their inventories.");

  const players = [...world.getPlayers()];
  players.forEach((p) => form.button(p.name));

  form.show(player).then((response) => {
    if (response.canceled) {
      console.log(`Player ${player.name} canceled player list form`);
      return;
    }

    const selectedPlayer = players[response.selection];
    if (!selectedPlayer) {
      console.warn(`No player selected by ${player.name}`);
      player.sendMessage("Error: Player not found.");
      return;
    }

    console.log(`Player ${player.name} selected ${selectedPlayer.name}`);
    showInventoryTypeForm(player, selectedPlayer);
  }).catch((error) => {
    console.error(`Error showing player list for ${player.name}: ${error}`);
    player.sendMessage("Error: Failed to display player list.");
  });
}

// Show inventory type selection form
function showInventoryTypeForm(player, selectedPlayer) {
  console.log(`Showing inventory type form for ${selectedPlayer.name}`);
  const form = new ActionFormData()
    .title(`Manage ${selectedPlayer.name}`)
    .body("Choose an inventory to inspect:")
    .button("Ender Chest")
    .button("Main Inventory")
    .button("§l§cBack");

  form.show(player).then((response) => {
    if (response.canceled || response.selection === 2) {
      console.log(`Player ${player.name} canceled inventory type form`);
      showPlayerListForm(player);
      return;
    }

    if (response.selection === 0) {
      inspectEnderChest(player, selectedPlayer);
    } else if (response.selection === 1) {
      inspectPlayerInventory(player, selectedPlayer);
    }
  }).catch((error) => {
    console.error(`Error showing inventory type form: ${error}`);
    player.sendMessage("Error: Failed to display inventory options.");
    showPlayerListForm(player);
  });
}

// Inspect ender chest
function inspectEnderChest(player, selectedPlayer) {
  console.log(`Inspecting ender chest for ${selectedPlayer.name}`);
  const enderChestInspector = new EnderChestInspector(selectedPlayer);

  enderChestInspector.getEnderChestContents().then((enderChestContents) => {
    const items = Array.from(enderChestContents.values());

    if (items.length === 0) {
      console.log(`Ender chest of ${selectedPlayer.name} is empty`);
      player.sendMessage(`§6${selectedPlayer.name}'s ender chest is empty.`);
      return;
    }

    showEnderChestForm(player, selectedPlayer, items);
  }).catch((error) => {
    console.error(`Failed to retrieve ender chest for ${selectedPlayer.name}: ${error}`);
    player.sendMessage(`§cFailed to retrieve ${selectedPlayer.name}'s ender chest contents.`);
  });
}

// Inspect main inventory
function inspectPlayerInventory(player, selectedPlayer) {
  console.log(`Inspecting main inventory for ${selectedPlayer.name}`);
  const inventory = selectedPlayer.getComponent("minecraft:inventory")?.container;

  if (!inventory) {
    console.warn(`Unable to fetch inventory for ${selectedPlayer.name}`);
    player.sendMessage("§cUnable to fetch inventory.");
    showPlayerListForm(player);
    return;
  }

  const items = [];
  for (let slot = 0; slot < inventory.size; slot++) {
    const item = inventory.getItem(slot);
    if (item) {
      items.push({ slot, item, enchantments: GetEnchants(item) });
    }
  }

  if (items.length === 0) {
    console.log(`Main inventory of ${selectedPlayer.name} is empty`);
    player.sendMessage(`§6${selectedPlayer.name}'s main inventory is empty.`);
    return;
  }

  showInventoryForm(player, selectedPlayer, items, "main");
}

// Show ender chest form
function showEnderChestForm(player, selectedPlayer, items) {
  console.log(`Showing ender chest form for ${selectedPlayer.name}`);
  const form = new ActionFormData()
    .title(`${selectedPlayer.name}'s Ender Chest`)
    .body("Select an item to manage. Note: Enchantments may not be displayed due to API limitations.");

  items.forEach((entry) => {
    if (!entry || !entry.item || !entry.item.typeId) {
      console.warn(`Invalid item in slot ${entry.slot} for ${selectedPlayer.name}`);
      return;
    }
    const itemName = entry.item.typeId.replace("minecraft:", "");
    form.button(`${entry.amount}x ${itemName}`);
  });
  form.button("§l§cBack");

  form.show(player).then((response) => {
    if (response.canceled || response.selection === items.length) {
      console.log(`Player ${player.name} canceled ender chest form`);
      showInventoryTypeForm(player, selectedPlayer);
      return;
    }

    const selectedEntry = items[response.selection];
    if (!selectedEntry || !selectedEntry.item) {
      console.warn(`Invalid item selected by ${player.name}`);
      player.sendMessage("§cInvalid item selected.");
      return;
    }

    showItemDetails(player, selectedPlayer, selectedEntry, items, "ender");
  }).catch((error) => {
    console.error(`Error showing ender chest form for ${selectedPlayer.name}: ${error}`);
    player.sendMessage("Error: Failed to display ender chest contents.");
  });
}

// Show inventory form
function showInventoryForm(player, selectedPlayer, items, type) {
  console.log(`Showing ${type} inventory form for ${selectedPlayer.name}`);
  const form = new ActionFormData()
    .title(`${selectedPlayer.name}'s ${type === "main" ? "Main Inventory" : "Ender Chest"}`)
    .body("Select an item to view details:");

  items.forEach((entry) => {
    if (!entry || !entry.item || !entry.item.typeId) {
      console.warn(`Invalid item in slot ${entry.slot} for ${selectedPlayer.name}`);
      return;
    }
    const itemName = entry.item.typeId.replace("minecraft:", "");
    const enchantText = type === "main" && entry.enchantments.length
      ? ` (${entry.enchantments.map((e) => `${e.type} ${e.level}`).join(", ")})`
      : "";
    form.button(`${entry.amount}x ${itemName}${enchantText}`);
  });
  form.button("§l§cBack");

  form.show(player).then((response) => {
    if (response.canceled || response.selection === items.length) {
      console.log(`Player ${player.name} canceled ${type} inventory form`);
      showInventoryTypeForm(player, selectedPlayer);
      return;
    }

    const selectedEntry = items[response.selection];
    if (!selectedEntry || !selectedEntry.item) {
      console.warn(`Invalid item selected by ${player.name}`);
      player.sendMessage("§cInvalid item selected.");
      return;
    }

    showItemDetails(player, selectedPlayer, selectedEntry, items, type);
  }).catch((error) => {
    console.error(`Error showing ${type} inventory form for ${selectedPlayer.name}: ${error}`);
    player.sendMessage("Error: Failed to display inventory contents.");
  });
}

// Show item details
function showItemDetails(player, selectedPlayer, selectedEntry, items, type) {
  const { slot, item, amount, enchantments = [] } = selectedEntry;

  if (!item || !item.typeId) {
    console.warn(`Invalid item in slot ${slot} for ${selectedPlayer.name}`);
    player.sendMessage("§cInvalid item selected.");
    return;
  }

  const itemName = item.typeId.replace("minecraft:", "");
  const enchantText = type === "main" && enchantments.length
    ? enchantments.map((e) => `§e${e.type}: Level ${e.level}`).join("\n")
    : "Unknown (API limitation for ender chest)";

  const form = new ActionFormData()
    .title(`Item: ${itemName}`)
    .body(
      `§eName: ${itemName}\n` +
      `§eAmount: ${amount}\n` +
      `§eSlot: ${slot + 1}\n` +
      `§eEnchantments:\n${enchantText}\n` +
      `Choose an action:`
    )
    .button("Take Item")
    .button("Remove Item")
    .button("§l§cBack");

  form.show(player).then((response) => {
    if (response.canceled || response.selection === 2) {
      console.log(`Player ${player.name} canceled item details`);
      type === "main"
        ? showInventoryForm(player, selectedPlayer, items, type)
        : showEnderChestForm(player, selectedPlayer, items);
      return;
    }

    const action = response.selection === 0 ? "take" : "remove";
    showConfirmationMenu(player, selectedPlayer, selectedEntry, action, items, type);
  }).catch((error) => {
    console.error(`Error showing item details for ${selectedPlayer.name}: ${error}`);
    player.sendMessage("Error: Failed to display item details.");
    type === "main"
      ? showInventoryForm(player, selectedPlayer, items, type)
      : showEnderChestForm(player, selectedPlayer, items);
  });
}

// Show confirmation menu
function showConfirmationMenu(player, selectedPlayer, selectedEntry, action, items, type) {
  const { slot, item, amount } = selectedEntry;

  if (!item || !item.typeId) {
    console.warn(`Invalid item in slot ${slot} for ${selectedPlayer.name}`);
    player.sendMessage("§cInvalid item selected.");
    type === "main"
      ? showInventoryForm(player, selectedPlayer, items, type)
      : showEnderChestForm(player, selectedPlayer, items);
    return;
  }

  const itemName = item.typeId.replace("minecraft:", "");
  const form = new ActionFormData()
    .title(`Confirm ${action === "take" ? "Take" : "Remove"}`)
    .body(`Are you sure you want to ${action} ${amount}x ${itemName}?`)
    .button("§aYes")
    .button("§cNo");

  form.show(player).then((response) => {
    if (response.canceled || response.selection === 1) {
      console.log(`Player ${player.name} canceled ${action} action`);
      showItemDetails(player, selectedPlayer, selectedEntry, items, type);
      return;
    }

    try {
      if (type === "main") {
        const inventory = selectedPlayer.getComponent("minecraft:inventory")?.container;
        if (!inventory) {
          throw new Error("Unable to fetch player inventory.");
        }

        if (action === "take") {
          const adminInventory = player.getComponent("minecraft:inventory")?.container;
          if (!adminInventory) {
            throw new Error("Unable to fetch admin inventory.");
          }
          const remaining = adminInventory.addItem(item);
          if (remaining) {
            player.sendMessage("§cYour inventory is full. Some items couldn’t be transferred.");
          } else {
            inventory.setItem(slot, null);
            player.sendMessage(`§aYou took ${amount}x ${itemName} from ${selectedPlayer.name}'s inventory.`);
          }
        } else {
          inventory.setItem(slot, null);
          player.sendMessage(`§aYou removed ${amount}x ${itemName} from ${selectedPlayer.name}'s inventory.`);
        }

        // Refresh main inventory
        const refreshedItems = [];
        for (let i = 0; i < inventory.size; i++) {
          const refreshedItem = inventory.getItem(i);
          if (refreshedItem) {
            refreshedItems.push({ slot: i, item: refreshedItem, enchantments: GetEnchants(refreshedItem) });
          }
        }
        showInventoryForm(player, selectedPlayer, refreshedItems, type);
      } else {
        if (action === "take") {
          const adminInventory = player.getComponent("minecraft:inventory")?.container;
          if (!adminInventory) {
            throw new Error("Unable to fetch admin inventory.");
          }
          // Use command to attempt preserving enchantments
          const tempSlot = 26;
          const giveCommand = `replaceitem entity "${player.name}" slot.inventory ${tempSlot} ${item.typeId} ${amount}`;
          const clearCommand = `replaceitem entity "${selectedPlayer.name}" slot.enderchest ${slot} air`;

          if (player.runCommand(giveCommand)) {
            selectedPlayer.runCommand(clearCommand);
            player.sendMessage(
              `§aYou took ${amount}x ${itemName} from ${selectedPlayer.name}'s ender chest. Note: Enchantments may not transfer due to API limitations.`
            );
          } else {
            throw new Error("Failed to transfer item to inventory.");
          }
        } else {
          const command = `replaceitem entity "${selectedPlayer.name}" slot.enderchest ${slot} air`;
          selectedPlayer.runCommand(command);
          player.sendMessage(`§aYou removed ${amount}x ${itemName} from ${selectedPlayer.name}'s ender chest.`);
        }

        // Refresh ender chest
        const enderChestInspector = new EnderChestInspector(selectedPlayer);
        enderChestInspector.getEnderChestContents().then((updatedContents) => {
          const updatedItems = Array.from(updatedContents.values());
          if (updatedItems.length === 0) {
            player.sendMessage(`§6${selectedPlayer.name}'s ender chest is empty.`);
            showInventoryTypeForm(player, selectedPlayer);
          } else {
            showEnderChestForm(player, selectedPlayer, updatedItems);
          }
        }).catch((error) => {
          console.error(`Failed to refresh ender chest: ${error}`);
          player.sendMessage("§cFailed to refresh ender chest contents.");
        });
      }
    } catch (error) {
      console.error(`Failed to ${action} item for ${selectedPlayer.name}: ${error}`);
      player.sendMessage(`§cFailed to ${action} item: ${error.message}`);
      type === "main"
        ? showInventoryForm(player, selectedPlayer, items, type)
        : showEnderChestForm(player, selectedPlayer, items);
    }
  }).catch((error) => {
    console.error(`Error showing confirmation: ${error}`);
    player.sendMessage("Error: Failed to display confirmation.");
    showItemDetails(player, selectedPlayer, selectedEntry, items, type);
  });
}