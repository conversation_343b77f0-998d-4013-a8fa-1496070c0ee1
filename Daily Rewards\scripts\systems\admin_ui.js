import { world, system, ItemStack } from "@minecraft/server";
import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { DB } from "./database.js";
import { DB_PREFIX, DEFAULT_GENERAL_SETTINGS, DEFAULT_WEEKEND_BONUS, DEFAULT_MONEY_SETTINGS, DEFAULT_ITEM_SETTINGS, DEFAULT_STRUCTURE_SETTINGS, DEFAULT_COMMAND_SETTINGS, DEFAULT_MILESTONE_SETTINGS } from './config.js';
import { ForceOpen } from './functions.js';

const db = new DB();

function showHelpTopic(player, title, body, back_function) {
    const form = new ActionFormData().title(title).body(body).button("§cBack");
    ForceOpen(player, form).then(() => back_function(player));
}

function openHelpMenu(player) {
    const form = new ActionFormData().title("§eHelp / How-To").body("Select a topic to learn more.")
        .button("§aInitial Server Setup")
        .button("§6Reward Configuration")
        .button("§bMilestone Configuration")
        .button("§9Item Blueprint Guide")
        .button("§3Player Management")
        .button("General Settings")
        .button("§cTroubleshooting")
        .button("Back to Admin Panel");

    form.show(player).then(res => {
        if (res.canceled) return;
        switch(res.selection) {
            case 0:
                const setupBody = "§e§lOne-Time Server Setup§r\n\nTo make the system work, you MUST run a few commands in your world once:\n\n1. §bCreate the scoreboard for money:§r\n§7/scoreboard objectives add money dummy \"Money\"§r\n(You can change 'money' to something else, but you must update it in the Money Rewards settings).\n\n2. §bGive yourself admin privileges:§r\n§7/tag YourPlayerName add admin§r\n(This tag is required to use the admin item).";
                showHelpTopic(player, "§aInitial Setup", setupBody, openHelpMenu);
                break;
            case 1:
                const rewardBody = "§e§lReward Configuration§r\n\nConfigure daily rewards. For any list, use commas (,) or semicolons (;) to separate entries.\n\n§cIMPORTANT:§r For Command Rewards, always surround the §b{player}§r placeholder with quotes to support names with spaces.\n\n§fExample: §7give \"{player}\" iron_ingot 10; say \"{player} got iron!\"§r";
                showHelpTopic(player, "§6Reward Config", rewardBody, openHelpMenu);
                break;
            case 2:
                const milestoneBody = "§e§lMilestone Configuration§r\n\nMilestones are special rewards for hitting an EXACT streak number. The format is §bday:command§r. Separate multiple milestones with a semicolon (§b;§r).\n\n§cIMPORTANT:§r Always surround §b{player}§r with quotes in your commands.\n\n§f§lExample:§r\n§77:give \"{player}\" minecraft:diamond 10; 30:title \"{player}\" title §d30 Day Streak!§r";
                showHelpTopic(player, "§bMilestone Config", milestoneBody, openHelpMenu);
                break;
            case 3:
                const bpBody = "§e§lItem Blueprints§r\n\nBlueprints allow you to save a custom item (with name, lore, and enchants) as a reward.\n\n§bTo Save a Blueprint:§r\nHold the custom item in your hand and type the command:\n§7/scriptevent dlr:save_item <blueprint_name>§r\n\nThe name cannot have spaces. This saved blueprint can then be chosen as a reward.";
                showHelpTopic(player, "§9Blueprint Guide", bpBody, openHelpMenu);
                break;
            case 4:
                const playerBody = "§e§lPlayer Management§r\n\n- §bView Stats:§r See a player's streak, longest streak, and last claim time.\n\n- §bAllow Claim Now:§r Resets the 24-hour cooldown for a player.\n\n- §bReset Streak to 0:§r Resets a player's daily streak.";
                showHelpTopic(player, "§3Player Management", playerBody, openHelpMenu);
                break;
            case 5:
                const generalBody = "§e§lGeneral Settings§r\n\n- §bAdmin/Claim Item:§r The item IDs to trigger the admin and player UIs.\n\n- §bClaim Entity:§r Set an NPC to be a claim station. Tag the NPC in-game with §7/tag @e[type=...,c=1] add reward_npc§r for the dialogue system to work.\n\n- §bAll Rewards Random:§r If ON, players will no longer see a choice UI and will instead get one random reward from all available pools for that day.";
                showHelpTopic(player, "General Settings", generalBody, openHelpMenu);
                break;
            case 6:
                const troubleBody = "§e§lTroubleshooting§r\n\n- §cMoney reward fails?§r The scoreboard objective probably doesn't exist. Check logs for details and use §7/scoreboard objectives add <name> dummy§r.\n\n- §cAdmin item not working?§r You need the 'admin' tag. Use §7/tag YourName add admin§r.\n\n- §cItem reward not saving?§r You must use the full, valid item ID, like §7minecraft:golden_apple§r.";
                showHelpTopic(player, "§cTroubleshooting", troubleBody, openHelpMenu);
                break;
            case 7:
                openAdminPanel(player);
                break;
        }
    });
}

async function openDashboard(player) {
    const form = new ActionFormData().title("§1Server Dashboard").body("§7Calculating server-wide stats, please wait...");
    const showPromise = form.show(player);

    const allKeys = db.getPropertyIds(`${DB_PREFIX}player_`);
    let totalStreaks = 0;
    let claimCountToday = 0;
    const now = Date.now();
    const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);
    const allPlayerData = [];

    for (const key of allKeys) {
        const pData = db.get(key);
        if (pData) {
            allPlayerData.push(pData);
            totalStreaks += pData.streak ?? 0;
            if (pData.lastClaim > twentyFourHoursAgo) {
                claimCountToday++;
            }
        }
    }

    const avgStreak = allPlayerData.length > 0 ? (totalStreaks / allPlayerData.length).toFixed(1) : 0;
    allPlayerData.sort((a, b) => (b.longestStreak ?? 0) - (a.longestStreak ?? 0));
    const top5Streaks = allPlayerData.slice(0, 5).map((p, i) => {
        return `§f${i+1}. ${p.name} - §e${p.longestStreak ?? 0} days`;
    }).join('\n');

    const body = `§fClaims in last 24h: §a${claimCountToday}\n` +
                 `§fPlayers with data: §a${allPlayerData.length}\n` +
                 `§fAverage Streak Length: §e${avgStreak}\n\n` +
                 `§l§bTop 5 Longest Streaks (All-Time):§r\n${top5Streaks}`;

    form.body(body);
    form.button("§cClose");
    await showPromise;
}

function showPlayerList(player) {
    const players = world.getPlayers().map(p => p.name);
    if(players.length === 0) {
        player.sendMessage("§cNo other players found on the server.");
        openAdminPanel(player);
        return;
    }
    const form = new ModalFormData().title("§3Player Management").dropdown("Select a player to manage", players);
    form.show(player).then(res => {
        if (!res.canceled) showPlayerStats(player, players[res.formValues[0]]);
    });
}

function showPlayerStats(player, targetPlayerName) {
    const playerData = db.get(`${DB_PREFIX}player_${targetPlayerName}`);
    if (!playerData) {
        player.sendMessage(`§cNo data found for ${targetPlayerName}.`);
        return;
    }

    let body = `§eStreak: §f${playerData.streak ?? 0}\n`;
    body += `§eLongest Streak: §f${playerData.longestStreak ?? 0}\n`;
    body += `§eLast Claim: §f${playerData.lastClaim ? new Date(playerData.lastClaim).toLocaleString() : 'Never'}\n`;
    body += `§eLast Login: §f${playerData.lastLogin ? new Date(playerData.lastLogin).toLocaleString() : 'Unknown'}\n`;

    const form = new ActionFormData().title(`§b${targetPlayerName}'s Stats`).body(body)
        .button("§2Allow Claim Now")
        .button("§cReset Streak to 0")
        .button("Back to Player List");

    form.show(player).then(res => {
        if (res.canceled) return;
        switch (res.selection) {
            case 0:
                playerData.lastClaim = 0;
                db.set(`${DB_PREFIX}player_${targetPlayerName}`, playerData);
                player.sendMessage(`§a${targetPlayerName} can now claim their reward.`);
                showPlayerStats(player, targetPlayerName);
                break;
            case 1:
                playerData.streak = 0;
                db.set(`${DB_PREFIX}player_${targetPlayerName}`, playerData);
                player.sendMessage(`§a${targetPlayerName}'s streak has been reset.`);
                showPlayerStats(player, targetPlayerName);
                break;
            case 2: showPlayerList(player); break;
        }
    });
}

function openRewardSettingsHub(player) {
    const form = new ActionFormData().title("§6Reward Settings")
        .button("§2Money Rewards").button("§bItem Rewards")
        .button("§dStructure Rewards").button("§6Command Rewards")
        .button("Back");
    form.show(player).then(res => {
        if(res.canceled) return;
        switch(res.selection){
            case 0: openMoneySettings(player); break;
            case 1: openItemSettings(player); break;
            case 2: openStructureSettings(player); break;
            case 3: openCommandSettings(player); break;
            case 4: openAdminPanel(player); break;
        }
    });
}

function openMoneySettings(player) {
    const settings = db.get(`${DB_PREFIX}moneySettings`) ?? DEFAULT_MONEY_SETTINGS;
    const form = new ModalFormData().title("§2Money Reward Settings")
        .toggle("Enable Money Rewards", { defaultValue: settings.enabled })
        .toggle("Randomize from List", { defaultValue: settings.randomize })
        .textField("Scoreboard Objective", "e.g., money", { defaultValue: settings.currencyObjective })
        .textField("Reward Amounts (CSV)", "e.g., 100,200,500", { defaultValue: settings.amounts.join(',') });
    form.show(player).then(res => {
        if(res.canceled) return openRewardSettingsHub(player);
        const [enabled, randomize, objective, amountsStr] = res.formValues;
        const amounts = amountsStr.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n) && n > 0);
        db.set(`${DB_PREFIX}moneySettings`, { enabled, randomize, currencyObjective: objective, amounts });
        player.sendMessage("§aMoney settings updated.");
        openMoneySettings(player);
    });
}

function openItemSettings(player) {
    const settings = db.get(`${DB_PREFIX}itemSettings`) ?? DEFAULT_ITEM_SETTINGS;
    const form = new ModalFormData().title("§bItem Reward Settings")
        .toggle("Enable Item Rewards", { defaultValue: settings.enabled })
        .toggle("Randomize from List", { defaultValue: settings.randomize })
        .textField("Item IDs (CSV)", "e.g., minecraft:diamond,minecraft:apple", { defaultValue: settings.items.join(',') });
    form.show(player).then(res => {
        if(res.canceled) return openRewardSettingsHub(player);
        const [enabled, randomize, itemsStr] = res.formValues;
        const items = itemsStr.split(',').map(i => i.trim()).filter(Boolean);
        
        let invalidItem = items.find(id => {
            try { new ItemStack(id, 1); return false; } catch { return true; }
        });

        if (invalidItem) {
            player.sendMessage(`§cError: "${invalidItem}" is not a valid item ID. Settings not saved.`);
        } else {
            db.set(`${DB_PREFIX}itemSettings`, { enabled, randomize, items });
            player.sendMessage("§aItem settings updated.");
        }
        openItemSettings(player);
    });
}

function openStructureSettings(player) {
    const settings = db.get(`${DB_PREFIX}structureSettings`) ?? DEFAULT_STRUCTURE_SETTINGS;
    const form = new ModalFormData().title("§dStructure Reward Settings")
        .toggle("Enable Structure Rewards", { defaultValue: settings.enabled })
        .toggle("Randomize from List", { defaultValue: settings.randomize })
        .textField("Structure Names (CSV)", "e.g., house1,ruin2", { defaultValue: settings.structures.join(',') });
    form.show(player).then(res => {
        if(res.canceled) return openRewardSettingsHub(player);
        const [enabled, randomize, structuresStr] = res.formValues;
        const structures = structuresStr.split(',').map(s => s.trim()).filter(Boolean);
        db.set(`${DB_PREFIX}structureSettings`, { enabled, randomize, structures });
        player.sendMessage("§aStructure settings updated.");
        openStructureSettings(player);
    });
}

function openCommandSettings(player) {
    const settings = db.get(`${DB_PREFIX}commandSettings`) ?? DEFAULT_COMMAND_SETTINGS;
    const form = new ModalFormData().title("§6Command Reward Settings")
        .toggle("Enable Command Rewards", { defaultValue: settings.enabled })
        .toggle("Randomize from List", { defaultValue: settings.randomize })
        .textField("Commands (separate with ;)", "e.g., give \"{player}\" diamond; say \"{player}\" is lucky!", { defaultValue: settings.commands.join('; ') });
    form.show(player).then(res => {
        if(res.canceled) return openRewardSettingsHub(player);
        const [enabled, randomize, commandsStr] = res.formValues;
        const commands = commandsStr.split(';').map(c => c.trim()).filter(Boolean);
        db.set(`${DB_PREFIX}commandSettings`, { enabled, randomize, commands });
        player.sendMessage("§aCommand settings updated.");
        openCommandSettings(player);
    });
}

function openMilestoneSettings(player) {
    const settings = db.get(`${DB_PREFIX}milestoneSettings`) ?? DEFAULT_MILESTONE_SETTINGS;
    const form = new ModalFormData().title("§bMilestone Reward Settings")
        .textField("Milestones (separate with ;)", "e.g., 7:give \"{player}\" diamond 5; 30:say \"{player}\" is a veteran!", { defaultValue: Object.entries(settings).map(([key, value]) => `${key}:${value}`).join('; ') });
    form.show(player).then(res => {
        if(res.canceled) return openAdminPanel(player);
        const [milestonesStr] = res.formValues;
        const newSettings = {};
        const entries = milestonesStr.split(';').filter(Boolean);
        for(const entry of entries){
            const parts = entry.split(':');
            const day = parseInt(parts[0]?.trim());
            const command = parts.slice(1).join(':').trim();
            if(!isNaN(day) && day > 0 && command) {
                newSettings[day] = command;
            }
        }
        db.set(`${DB_PREFIX}milestoneSettings`, newSettings);
        player.sendMessage("§aMilestone settings updated.");
        openMilestoneSettings(player);
    });
}

function openBlueprintManager(player) {
    const form = new ActionFormData().title("§9Item Blueprints");
    const blueprintKeys = db.getPropertyIds(`${DB_PREFIX}blueprint_`);
    const blueprints = blueprintKeys.map(key => key.substring(`${DB_PREFIX}blueprint_`.length));
    
    form.body("Blueprints are custom items saved as rewards.\nTo save a new one, hold the item and type:\n§e/scriptevent dlr:save_item <name>");
    
    blueprints.forEach(name => form.button(`§f${name}`));
    form.button("§cBack to Admin Panel");

    form.show(player).then(res => {
        if(res.canceled || res.selection >= blueprints.length) return openAdminPanel(player);
        
        const selectedName = blueprints[res.selection];
        const data = db.get(`${DB_PREFIX}blueprint_${selectedName}`);
        
        let detailBody = `§eName: §f${data.nameTag ?? data.typeId}\n`;
        detailBody += `§eType ID: §f${data.typeId}\n`;
        detailBody += `§eAmount: §f${data.amount}\n`;
        if (data.lore?.length) detailBody += `§eLore: §f${data.lore.join('\n')}\n`;
        if (data.enchantments?.length) detailBody += `§eEnchants: §f${data.enchantments.map(e => `${e.type} ${e.level}`).join(', ')}`;

        const editForm = new ActionFormData().title(`§9Blueprint: ${selectedName}`).body(detailBody)
            .button("§cDELETE THIS BLUEPRINT")
            .button("Back");
            
        editForm.show(player).then(editRes => {
            if(editRes.canceled || editRes.selection === 1) return openBlueprintManager(player);
            if(editRes.selection === 0) {
                 db.delete(`${DB_PREFIX}blueprint_${selectedName}`);
                 player.sendMessage(`§aBlueprint "${selectedName}" deleted.`);
                 openBlueprintManager(player);
            }
        });
    });
}

function openGeneralSettings(player) {
    const settings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;
    const bonus = db.get(`${DB_PREFIX}weekendBonus`) ?? DEFAULT_WEEKEND_BONUS;
    
    const form = new ModalFormData().title("§7General Settings")
        .toggle("Give All Rewards Randomly (No UI)", { defaultValue: settings.allRewardsRandom })
        .textField("Admin Item ID", "e.g., minecraft:compass", { defaultValue: settings.adminItem })
        .textField("Claim Item ID (optional)", "e.g., minecraft:clock", { defaultValue: settings.claimItem })
        .textField("Claim Entity ID (optional)", "e.g., minecraft:npc", { defaultValue: settings.interactEntityType })
        .dropdown("Entity Interaction Type", ["Hit", "Interact", "Both"], { defaultValue: ["Hit", "Interact", "Both"].indexOf(settings.interactionType) })
        .textField("Streak Grace Period (Hours)", "e.g., 72", { defaultValue: String(settings.streakGracePeriodHours) })
        .toggle("Enable Weekend Bonus", { defaultValue: bonus.enabled })
        .slider("Friday Bonus Multiplier (x)", 1, 3, { valueStep: 0.25, defaultValue: bonus.friday })
        .slider("Saturday Bonus Multiplier (x)", 1, 3, { valueStep: 0.25, defaultValue: bonus.saturday })
        .slider("Sunday Bonus Multiplier (x)", 1, 3, { valueStep: 0.25, defaultValue: bonus.sunday });

    form.show(player).then(res => {
        if(res.canceled) return openAdminPanel(player);
        const [allRandom, adminItem, claimItem, entityId, interactionIndex, graceHoursStr, bonusEnabled, fri, sat, sun] = res.formValues;

        db.set(`${DB_PREFIX}generalSettings`, {
            allRewardsRandom,
            adminItem,
            claimItem,
            interactEntityType: entityId,
            interactionType: ["Hit", "Interact", "Both"][interactionIndex],
            streakGracePeriodHours: parseInt(graceHoursStr) || 72
        });
        db.set(`${DB_PREFIX}weekendBonus`, { enabled: bonusEnabled, friday: fri, saturday: sat, sunday: sun });

        player.sendMessage("§aGeneral settings updated.");
        openGeneralSettings(player);
    });
}

export function openAdminPanel(player) {
    const form = new ActionFormData().title("§8DLR Admin Panel")
        .button("§1Server Dashboard")
        .button("§3Player Management")
        .button("§6Reward Settings")
        .button("§bMilestone Settings")
        .button("§9Item Blueprints")
        .button("General Settings")
        .button("§e[?] Help / How-To");

    form.show(player).then(res => {
        if (res.canceled) return;
        switch (res.selection) {
            case 0: openDashboard(player); break;
            case 1: showPlayerList(player); break;
            case 2: openRewardSettingsHub(player); break;
            case 3: openMilestoneSettings(player); break;
            case 4: openBlueprintManager(player); break;
            case 5: openGeneralSettings(player); break;
            case 6: openHelpMenu(player); break;
        }
    });
}