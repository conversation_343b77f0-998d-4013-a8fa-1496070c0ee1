export const DB_PREFIX = 'dly_';

export const DEFAULT_GENERAL_SETTINGS = {
    adminItem: 'minecraft:compass',
    claimItem: 'minecraft:clock',
    interactEntityType: 'minecraft:npc',
    interactionType: 'Both',
    streakGracePeriodHours: 72,
    allRewardsRandom: false,
};

export const DEFAULT_WEEKEND_BONUS = {
    enabled: true,
    friday: 1.25,
    saturday: 1.5,
    sunday: 1.5,
};

export const DEFAULT_MONEY_SETTINGS = {
    enabled: true,
    randomize: false,
    amounts: [100, 200, 300, 400, 500, 750, 1000],
    currencyObjective: 'money'
};

export const DEFAULT_ITEM_SETTINGS = {
    enabled: false,
    randomize: true,
    items: ['minecraft:diamond', 'minecraft:gold_ingot', 'minecraft:iron_ingot']
};

export const DEFAULT_STRUCTURE_SETTINGS = {
    enabled: false,
    randomize: true,
    structures: []
};

export const DEFAULT_COMMAND_SETTINGS = {
    enabled: false,
    randomize: false,
    commands: ['say "{player}" has claimed a command reward!']
};

export const DEFAULT_MILESTONE_SETTINGS = {
    '7': 'give "{player}" minecraft:diamond 5',
    '30': 'give "{player}" minecraft:netherite_ingot 1',
    '100': 'title "{player}" title §b100 Day Streak!',
};