import { World, world, Entity } from "@minecraft/server";
const maxPropertyLength = 32767;

const DB_Instances = new WeakMap();

export class DB {
    vectorProps = ["x", "y", "z"];
    storageType;
    __id;
    cache = new Map();

    constructor(storageType = world) {
        if (DB_Instances.has(storageType)) {
            return DB_Instances.get(storageType);
        }
        DB_Instances.set(storageType, this);

        this.storageType = storageType;
        this.__id = (storageType instanceof World) ? "WORLD_db_" : `${storageType.id}_db_`;
    }

    getPropertyIds(key = undefined) {
        const allIds = this.getPropertyIdsFromCache();
        const prefix = this.__id + (key || '');
        return allIds.filter(id => id.startsWith(prefix));
    }

    updateCache() {
        try {
            const allProps = this.storageType.getDynamicPropertyIds();
            this.cache.set("ids", allProps);
            return allProps;
        } catch (e) {
            return [];
        }
    }

    getPropertyIdsFromCache() {
        return this.cache.get("ids") || this.updateCache();
    }

    get(key) {
        const { storageType, __id } = this;
        const baseKey = __id + key;

        let value = storageType.getDynamicProperty(baseKey);

        if (typeof value !== 'string') {
            return value;
        }

        if (value.startsWith('__CHUNKED__')) {
            let completeData = '';
            let chunkIndex = 0;
            while (true) {
                const chunkKey = `${baseKey}_${chunkIndex}`;
                const chunk = storageType.getDynamicProperty(chunkKey);
                if (typeof chunk !== 'string') break; 
                completeData += chunk;
                chunkIndex++;
            }
            value = completeData;
        }

        try {
            return JSON.parse(value);
        } catch (e) {
            return value;
        }
    }

    setString(key, data) {
        const { storageType, __id } = this;
        const baseKey = __id + key;

        this.delete(key); 
        
        if (data.length > maxPropertyLength) {
            storageType.setDynamicProperty(baseKey, '__CHUNKED__');
            let chunkIndex = 0;
            for (let i = 0; i < data.length; i += maxPropertyLength) {
                const chunk = data.substring(i, i + maxPropertyLength);
                storageType.setDynamicProperty(`${baseKey}_${chunkIndex}`, chunk);
                chunkIndex++;
            }
        } else {
            storageType.setDynamicProperty(baseKey, data);
        }
    }

    set(key, value) {
        const { storageType, __id, vectorProps } = this;
        const baseKey = __id + key;
        
        this.delete(key);

        if (typeof value === "number" || typeof value === "boolean") {
            storageType.setDynamicProperty(baseKey, value);
        } else if (value && typeof value === "object" && Object.keys(value).length === 3 && vectorProps.every(prop => typeof value[prop] === 'number')) {
            storageType.setDynamicProperty(baseKey, value);
        } else {
            const stringifiedValue = JSON.stringify(value);
            this.setString(key, stringifiedValue);
        }

        this.updateCache();
        return this;
    }

    clear() {
        const { storageType } = this;
        this.getPropertyIds().forEach((propertyId) => {
            storageType.setDynamicProperty(propertyId, undefined);
        });
        this.updateCache();
    }

    delete(key) {
        const { storageType } = this;
        const props = this.getPropertyIds(key);
        if (props.length === 0) return false;

        props.forEach((propertyId) => {
            storageType.setDynamicProperty(propertyId, undefined);
        });

        this.updateCache();
        return true;
    }

    get size() {
        const uniqueKeys = new Set(this.getPropertyIds().map(prop => prop.substring(this.__id.length).split('_')[0]));
        return uniqueKeys.size;
    }

    entries() {
        return this.constructEntries();
    }

    has(key) {
        return this.getPropertyIds(key).length > 0;
    }

    forEach(func) {
        for (const [key, value] of this.entries()) {
            func(value, key, this);
        }
    }

    *keys() {
        for (const [key] of this.constructEntries(true)) {
            yield key;
        }
    }

    *values() {
        for (const [_, value] of this.constructEntries()) {
            yield value;
        }
    }

    *constructEntries(skipGet = false) {
        const { __id } = this;
        const props = this.getPropertyIds();
        const seenKeys = new Set();

        for (const prop of props) {
            const rawKey = prop.substring(__id.length);
            const key = rawKey.split('_')[0];

            if (seenKeys.has(key)) continue;
            seenKeys.add(key);

            yield [key, skipGet ? null : this.get(key)];
        }
    }

    *[Symbol.iterator]() {
        yield* this.constructEntries();
    }
}