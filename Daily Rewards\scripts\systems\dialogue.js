import { world, system } from '@minecraft/server';
import { showClaimRewardUI } from './player_ui.js';

const NPC_NAME = "§eReward Giver§r";

function say(player, message) {
    world.getDimension('overworld').runCommand(`tellraw "${player.name}" {"rawtext":[{"text":"${NPC_NAME}§f: ${message}"}]}`);
}

export function handleNpcDialogue(player, playerData) {
    if (playerData.streak > 50) {
        say(player, `The legend returns! Your ${playerData.streak}-day streak is impressive. Let's see what you've earned today.`);
    } else if (playerData.streak > 10) {
        say(player, `Welcome back! Keep up that ${playerData.streak}-day streak! Here are your rewards.`);
    } else {
        say(player, "It's good to see you again! Choose your reward for today.");
    }

    system.run(() => {
        showClaimRewardUI(player, playerData);
    });
}