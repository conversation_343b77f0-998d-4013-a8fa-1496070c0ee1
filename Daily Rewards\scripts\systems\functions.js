import { world, system } from "@minecraft/server";

export function metricNumbers(value) {
  if (typeof value !== 'number') return value;
  const types = ["", "K", "M", "B", "T", "P", "E", "Z", "Y"];
  const selectType = (Math.log10(value) / 3) | 0;
  if (selectType == 0) return value.toString();
  let scaled = value / Math.pow(10, selectType * 3);
  return scaled.toFixed(1) + types[selectType];
}

export function metricNumbersWithCommas(value) {
  if (typeof value !== 'number') return String(value);
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export function getScore(target, objectiveName, useZero = true) {
  try {
    const objective = world.scoreboard.getObjective(objectiveName);
    if (!objective) return useZero ? 0 : NaN;
    
    if (typeof target === 'string') {
        const participant = objective.getParticipants().find(p => p.displayName === target);
        if (participant) {
            return objective.getScore(participant);
        }
    } else if (target && target.scoreboardIdentity) {
        return objective.getScore(target.scoreboardIdentity);
    }
    return useZero ? 0 : NaN;
  } catch {
    return useZero ? 0 : NaN;
  }
}

export function setTimeout(func, delay) {
  let currentTick = 0;
  const tickCallback = () => {
    if (currentTick < delay) {
      currentTick++;
      system.run(tickCallback);
    } else {
      func();
    }
  };
  system.run(tickCallback);
}

export async function ForceOpen(player, form) {
  while (true) {
    const response = await form.show(player);
    if (response.cancelationReason !== "userBusy") {
      return response;
    }
    await new Promise(resolve => system.run(resolve));
  }
}

export function formatTime(ms) {
    if (ms < 60000) return 'Less than a minute';

    const seconds = Math.floor((ms / 1000) % 60);
    const minutes = Math.floor((ms / (1000 * 60)) % 60);
    const hours = Math.floor((ms / (1000 * 60 * 60)) % 24);
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));

    const parts = [];
    if (days > 0) parts.push(`${days} day${days > 1 ? 's' : ''}`);
    if (hours > 0) parts.push(`${hours} hr${hours > 1 ? 's' : ''}`);
    if (minutes > 0) parts.push(`${minutes} min${minutes > 1 ? 's' : ''}`);
    if (seconds > 0) parts.push(`${seconds} sec${seconds > 1 ? 's' : ''}`);

    return parts.join(', ');
}

export function formatTimeForAdmin(timestamp, offset) {
    const date = new Date(timestamp);
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    const newDate = new Date(utc + (3600000 * offset));
    return newDate.toLocaleString();
}