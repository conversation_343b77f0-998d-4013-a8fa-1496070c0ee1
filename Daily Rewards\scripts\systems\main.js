import { world, system, ItemStack } from "@minecraft/server";
import { DB } from "./database.js";
import { DB_PREFIX, DEFAULT_GENERAL_SETTINGS } from './config.js';
import { openAdminPanel } from "./admin_ui.js";
import { processClaimInteraction } from "./player_ui.js";

const db = new DB();

function initializePlayer(player) {
    const playerDataKey = `${DB_PREFIX}player_${player.name}`;
    const settings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;
    let playerData = db.get(playerDataKey);
    const now = Date.now();
    
    if (!playerData) {
        playerData = { name: player.name, lastClaim: 0, streak: 0, longestStreak: 0, lastLogin: now, sessionStart: now };
    } else {
        const gracePeriodMs = (settings.streakGracePeriodHours || 72) * 60 * 60 * 1000;
        const missedPeriod = now - (playerData.lastLogin || 0) > gracePeriodMs;
        if (missedPeriod) {
            playerData.streak = 0;
            player.sendMessage("§cIt's been a while! Your login streak has been reset.");
        }
        playerData.lastLogin = now;
        playerData.sessionStart = now;
    }
    db.set(playerDataKey, playerData);
    return playerData;
}

function calculateSessionDuration(player) {
    const playerDataKey = `${DB_PREFIX}player_${player.name}`;
    const playerData = db.get(playerDataKey);
    if (playerData && playerData.sessionStart) {
        playerData.lastLogout = Date.now();
        db.set(playerDataKey, playerData);
    }
}

world.afterEvents.playerSpawn.subscribe(event => {
    if (event.initialSpawn) initializePlayer(event.player);
});

world.beforeEvents.playerLeave.subscribe(event => {
    calculateSessionDuration(event.player);
});

world.afterEvents.itemUse.subscribe(event => {
    const { itemStack, source: player } = event;
    if (player.typeId !== 'minecraft:player') return;

    const settings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;
    
    if (itemStack.typeId === settings.adminItem && player.hasTag('admin')) {
        openAdminPanel(player);
    } else if (itemStack.typeId === settings.claimItem) {
        processClaimInteraction(player, 'item');
    }
});

function handleEntityInteraction(player, target) {
    const settings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;

    if (target && target.typeId === settings.interactEntityType && target.hasTag('reward_npc')) {
        processClaimInteraction(player, 'npc');
    }
}

world.afterEvents.playerInteractWithEntity.subscribe(event => {
    const settings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;
    if (settings.interactionType === 'Interact' || settings.interactionType === 'Both') {
        handleEntityInteraction(event.player, event.target);
    }
});

world.afterEvents.entityHitEntity.subscribe(event => {
    if (event.damagingEntity.typeId !== 'minecraft:player') return;
    const settings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;
     if (settings.interactionType === 'Hit' || settings.interactionType === 'Both') {
        handleEntityInteraction(event.damagingEntity, event.hitEntity);
    }
});

world.beforeEvents.chatSend.subscribe((event) => {
    const { sender: player, message } = event;
    if (!message.startsWith('/scriptevent')) return;
    if (!player.hasTag('admin')) return;

    const args = message.split(' ');
    const command = args[0];
    const blueprintCmd = args[1];
    const nameToSave = args[2];

    if (command === '/scriptevent' && blueprintCmd === 'dlr:save_item') {
        event.cancel = true;
        if (!nameToSave) return player.sendMessage("§cUsage: /scriptevent dlr:save_item <blueprint_name>");
        
        const inventory = player.getComponent('minecraft:inventory').container;
        const heldItem = inventory.getItem(player.selectedSlot);
        if (!heldItem) return player.sendMessage("§cYou must be holding an item to save it as a blueprint.");
        
        const enchantmentComponent = heldItem.getComponent('minecraft:enchantable');
        const serialized = {
            typeId: heldItem.typeId,
            amount: heldItem.amount,
            nameTag: heldItem.nameTag,
            lore: heldItem.getLore(),
            enchantments: enchantmentComponent.getEnchantments().map(e => ({ type: e.type.id, level: e.level }))
        };

        db.set(`${DB_PREFIX}blueprint_${nameToSave}`, serialized);
        player.sendMessage(`§aBlueprint "${nameToSave}" saved successfully!`);
    }
});