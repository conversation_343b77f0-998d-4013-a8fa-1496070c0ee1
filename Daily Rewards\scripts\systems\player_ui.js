import { world, system } from "@minecraft/server";
import { ActionFormData } from "@minecraft/server-ui";
import { DB } from "./database.js";
import { DB_PREFIX, DEFAULT_GENERAL_SETTINGS, DEFAULT_MONEY_SETTINGS, DEFAULT_ITEM_SETTINGS, DEFAULT_STRUCTURE_SETTINGS, DEFAULT_COMMAND_SETTINGS, DEFAULT_MILESTONE_SETTINGS, DEFAULT_WEEKEND_BONUS } from './config.js';
import { ForceOpen, formatTime, metricNumbersWithCommas } from './functions.js';
import * as rewards from './rewards.js';
import { handleNpcDialogue } from './dialogue.js';

const db = new DB();

async function _handleSuccessfulClaim(player, playerData, successMessage) {
    player.sendMessage(successMessage);
    rewards.playSuccessEffects(player);
    
    playerData.lastClaim = Date.now();
    playerData.streak++;
    if (playerData.streak > (playerData.longestStreak || 0)) {
        playerData.longestStreak = playerData.streak;
    }
    db.set(`${DB_PREFIX}player_${player.name}`, playerData);

    const milestones = db.get(`${DB_PREFIX}milestoneSettings`) ?? DEFAULT_MILESTONE_SETTINGS;
    if (milestones[playerData.streak]) {
        if (await rewards.grantCommandReward(player, milestones[playerData.streak])) {
            player.sendMessage(`§b§lMILESTONE!§r §eYou've reached a §f${playerData.streak}-day streak§e and earned a special reward!`);
        }
    }
}

async function grantMasterRandomReward(player, playerData) {
    const moneySettings = db.get(`${DB_PREFIX}moneySettings`) ?? DEFAULT_MONEY_SETTINGS;
    const itemSettings = db.get(`${DB_PREFIX}itemSettings`) ?? DEFAULT_ITEM_SETTINGS;
    const structureSettings = db.get(`${DB_PREFIX}structureSettings`) ?? DEFAULT_STRUCTURE_SETTINGS;
    const commandSettings = db.get(`${DB_PREFIX}commandSettings`) ?? DEFAULT_COMMAND_SETTINGS;
    const weekendBonusSettings = db.get(`${DB_PREFIX}weekendBonus`) ?? DEFAULT_WEEKEND_BONUS;
    const blueprintsInDb = db.getPropertyIds(`${DB_PREFIX}blueprint_`).length > 0;

    const potentialRewards = [];

    const today = new Date();
    const dayOfWeek = today.getDay();
    let bonusMultiplier = 1.0;
    if (weekendBonusSettings.enabled) {
        if (dayOfWeek === 5) bonusMultiplier = weekendBonusSettings.friday;
        if (dayOfWeek === 6) bonusMultiplier = weekendBonusSettings.saturday;
        if (dayOfWeek === 0) bonusMultiplier = weekendBonusSettings.sunday;
    }
    
    if (moneySettings.enabled && moneySettings.amounts.length > 0) {
        let amount = moneySettings.randomize ? moneySettings.amounts[Math.floor(Math.random() * moneySettings.amounts.length)] : moneySettings.amounts[playerData.streak % moneySettings.amounts.length];
        amount *= bonusMultiplier;
        potentialRewards.push({ type: 'money', value: amount, objective: moneySettings.currencyObjective });
    }
    if (itemSettings.enabled && (itemSettings.items.length > 0 || blueprintsInDb)) {
        const blueprintKeys = db.getPropertyIds(`${DB_PREFIX}blueprint_`);
        const blueprintNames = blueprintKeys.map(k => `blueprint:${k.substring(`${DB_PREFIX}blueprint_`.length)}`);
        const allItems = [...itemSettings.items, ...blueprintNames];
        if (allItems.length > 0) {
            const itemRef = itemSettings.randomize ? allItems[Math.floor(Math.random() * allItems.length)] : allItems[playerData.streak % allItems.length];
            potentialRewards.push({ type: 'item', value: itemRef });
        }
    }
    if (structureSettings.enabled && structureSettings.structures.length > 0) {
        const structure = structureSettings.randomize ? structureSettings.structures[Math.floor(Math.random() * structureSettings.structures.length)] : structureSettings.structures[playerData.streak % structureSettings.structures.length];
        potentialRewards.push({ type: 'structure', value: structure });
    }
    if (commandSettings.enabled && commandSettings.commands.length > 0) {
        const command = commandSettings.randomize ? commandSettings.commands[Math.floor(Math.random() * commandSettings.commands.length)] : commandSettings.commands[playerData.streak % commandSettings.commands.length];
        potentialRewards.push({ type: 'command', value: command });
    }
    
    if (potentialRewards.length === 0) {
        player.sendMessage("§cNo rewards are configured. Please contact an administrator.");
        return;
    }

    const chosenReward = potentialRewards[Math.floor(Math.random() * potentialRewards.length)];
    let success = false;
    let message = "";
    
    switch (chosenReward.type) {
        case 'money':
            if (await rewards.grantMoneyReward(player, chosenReward.value, chosenReward.objective)) {
                success = true;
                message = `§aYou randomly received §f${metricNumbersWithCommas(Math.round(chosenReward.value))} ${chosenReward.objective}.`;
            }
            break;
        case 'item':
            if (chosenReward.value.startsWith('blueprint:')) {
                const blueprintName = chosenReward.value.split(':')[1];
                if (rewards.grantBlueprintReward(player, blueprintName)) {
                    success = true;
                    message = `§aYou randomly received a special blueprint item: §f${blueprintName}`;
                }
            } else {
                if (await rewards.grantItemReward(player, chosenReward.value)) {
                    success = true;
                    message = `§aYou randomly received item: §f${chosenReward.value}`;
                }
            }
            break;
        case 'structure':
            if(await rewards.grantStructureReward(player, chosenReward.value)) {
                success = true;
                message = `§aYou randomly received structure: §f${chosenReward.value}`;
            }
            break;
        case 'command':
            if(await rewards.grantCommandReward(player, chosenReward.value)) {
                success = true;
                message = `§aYou randomly received a special command reward!`;
            }
            break;
    }
    
    if (success) {
        await _handleSuccessfulClaim(player, playerData, message);
    } else {
        player.sendMessage("§cFailed to grant reward. Your claim has not been used. Please try again or contact an admin if the issue persists.");
    }
}

export function processClaimInteraction(player, interactionType) {
    const playerData = db.get(`${DB_PREFIX}player_${player.name}`);
    if (!playerData) return;

    const generalSettings = db.get(`${DB_PREFIX}generalSettings`) ?? DEFAULT_GENERAL_SETTINGS;
    const now = Date.now();
    const nextClaimTime = playerData.lastClaim + (24 * 60 * 60 * 1000);

    if (now < nextClaimTime) {
        const timeRemaining = formatTime(nextClaimTime - now);
        if (interactionType === 'npc') {
            world.getDimension('overworld').runCommand(`tellraw "${player.name}" {"rawtext":[{"text":"§eReward Giver§f: Patience, friend. You can claim another reward in ${timeRemaining}."}]}`);
        } else {
            player.sendMessage(`§cYou must wait ${timeRemaining} to claim your reward.`);
        }
        return;
    }

    if (generalSettings.allRewardsRandom) {
        grantMasterRandomReward(player, playerData);
    } else {
        if (interactionType === 'npc') {
            handleNpcDialogue(player, playerData);
        } else {
            showClaimRewardUI(player, playerData);
        }
    }
}

async function showUpcomingRewardsUI(player) {
    const form = new ActionFormData();
    form.title("§1Upcoming Rewards");

    const moneySettings = db.get(`${DB_PREFIX}moneySettings`) ?? DEFAULT_MONEY_SETTINGS;
    const itemSettings = db.get(`${DB_PREFIX}itemSettings`) ?? DEFAULT_ITEM_SETTINGS;
    const structureSettings = db.get(`${DB_PREFIX}structureSettings`) ?? DEFAULT_STRUCTURE_SETTINGS;
    const commandSettings = db.get(`${DB_PREFIX}commandSettings`) ?? DEFAULT_COMMAND_SETTINGS;

    const playerData = db.get(`${DB_PREFIX}player_${player.name}`);
    if (!playerData) return;

    let body = "Rewards are shown based on your current streak for non-random rewards.\n\n";

    for (let i = 1; i <= 7; i++) {
        const futureStreak = playerData.streak + i;
        let rewardDescriptions = [];

        if (moneySettings.enabled && !moneySettings.randomize && moneySettings.amounts.length > 0) {
            const amount = moneySettings.amounts[(futureStreak - 1) % moneySettings.amounts.length];
            rewardDescriptions.push(`§2${metricNumbersWithCommas(amount)} ${moneySettings.currencyObjective}`);
        }
        if (itemSettings.enabled && !itemSettings.randomize && itemSettings.items.length > 0) {
            rewardDescriptions.push(`§b${itemSettings.items[(futureStreak - 1) % itemSettings.items.length]}`);
        }
        if (structureSettings.enabled && !structureSettings.randomize && structureSettings.structures.length > 0) {
            rewardDescriptions.push(`§d${structureSettings.structures[(futureStreak - 1) % structureSettings.structures.length]}`);
        }
        if (commandSettings.enabled && !commandSettings.randomize && commandSettings.commands.length > 0) {
            rewardDescriptions.push(`§6Command Reward`);
        }
        if (rewardDescriptions.length === 0) {
            rewardDescriptions.push("§7Random or Unset");
        }

        body += `§eDay ${futureStreak}: §f${rewardDescriptions.join(' or ')}\n`;
    }

    form.body(body);
    form.button("§cBack");
    ForceOpen(player, form).then(() => processClaimInteraction(player, 'item'));
}


export async function showClaimRewardUI(player, playerData) {
    const moneySettings = db.get(`${DB_PREFIX}moneySettings`) ?? DEFAULT_MONEY_SETTINGS;
    const itemSettings = db.get(`${DB_PREFIX}itemSettings`) ?? DEFAULT_ITEM_SETTINGS;
    const structureSettings = db.get(`${DB_PREFIX}structureSettings`) ?? DEFAULT_STRUCTURE_SETTINGS;
    const commandSettings = db.get(`${DB_PREFIX}commandSettings`) ?? DEFAULT_COMMAND_SETTINGS;
    const weekendBonusSettings = db.get(`${DB_PREFIX}weekendBonus`) ?? DEFAULT_WEEKEND_BONUS;
    const blueprintsInDb = db.getPropertyIds(`${DB_PREFIX}blueprint_`).length > 0;

    const form = new ActionFormData();
    const currentStreakDay = playerData.streak + 1;
    form.title("§1Daily Reward - Day " + currentStreakDay);

    const today = new Date();
    const dayOfWeek = today.getDay();
    let bonusMultiplier = 1.0;
    let bonusMessage = "";
    if (weekendBonusSettings.enabled) {
        if (dayOfWeek === 5 && weekendBonusSettings.friday > 1) bonusMultiplier = weekendBonusSettings.friday;
        if (dayOfWeek === 6 && weekendBonusSettings.saturday > 1) bonusMultiplier = weekendBonusSettings.saturday;
        if (dayOfWeek === 0 && weekendBonusSettings.sunday > 1) bonusMultiplier = weekendBonusSettings.sunday;
        if (bonusMultiplier > 1) {
            bonusMessage = `\n§d§lWEEKEND BONUS: §fAll money rewards are multiplied by ${bonusMultiplier}x today!`;
        }
    }

    form.body("§eYour streak: §f" + playerData.streak + " days\n§eChoose your reward for today!" + bonusMessage);

    const availableRewards = [];
    const menuOptions = [];

    if (moneySettings.enabled && moneySettings.amounts.length > 0) availableRewards.push({ type: 'money', label: '§2Money Reward' });
    if (itemSettings.enabled && (itemSettings.items.length > 0 || blueprintsInDb)) availableRewards.push({ type: 'item', label: '§bItem/Blueprint Reward' });
    if (structureSettings.enabled && structureSettings.structures.length > 0) availableRewards.push({ type: 'structure', label: '§dStructure Reward' });
    if (commandSettings.enabled && commandSettings.commands.length > 0) availableRewards.push({ type: 'command', label: '§6Command Reward' });

    if (availableRewards.length === 0) {
        player.sendMessage("§cNo rewards are configured. Please contact an administrator.");
        return;
    }

    availableRewards.forEach(reward => form.button(reward.label));

    form.button("§dView Upcoming Rewards");
    menuOptions.push('upcoming');

    const response = await ForceOpen(player, form);
    if (response.canceled) return;

    if (response.selection < availableRewards.length) {
        const choice = availableRewards[response.selection];
        let claimedSuccessfully = false;
        let message = "";

        switch (choice.type) {
            case 'money': {
                let amount = moneySettings.randomize ? moneySettings.amounts[Math.floor(Math.random() * moneySettings.amounts.length)] : moneySettings.amounts[playerData.streak % moneySettings.amounts.length];
                amount *= bonusMultiplier;
                if (await rewards.grantMoneyReward(player, amount, moneySettings.currencyObjective)) {
                    claimedSuccessfully = true;
                    message = `§aYou received §f${metricNumbersWithCommas(Math.round(amount))} ${moneySettings.currencyObjective}.`;
                }
                break;
            }
            case 'item': {
                const blueprintKeys = db.getPropertyIds(`${DB_PREFIX}blueprint_`);
                const blueprintNames = blueprintKeys.map(k => `blueprint:${k.substring(`${DB_PREFIX}blueprint_`.length)}`);
                const allItems = [...itemSettings.items, ...blueprintNames];
                if (allItems.length === 0) break;

                const itemRef = itemSettings.randomize ? allItems[Math.floor(Math.random() * allItems.length)] : allItems[playerData.streak % allItems.length];

                if (itemRef.startsWith('blueprint:')) {
                    const blueprintName = itemRef.split(':')[1];
                    if (rewards.grantBlueprintReward(player, blueprintName)) {
                        claimedSuccessfully = true;
                        message = `§aYou received a special blueprint item: §f${blueprintName}`;
                    }
                } else {
                    if (await rewards.grantItemReward(player, itemRef)) {
                        claimedSuccessfully = true;
                        message = `§aYou received item: §f${itemRef}`;
                    }
                }
                break;
            }
            case 'structure': {
                const structure = structureSettings.randomize ? structureSettings.structures[Math.floor(Math.random() * structureSettings.structures.length)] : structureSettings.structures[playerData.streak % structureSettings.structures.length];
                if (await rewards.grantStructureReward(player, structure)) {
                    claimedSuccessfully = true;
                    message = `§aYou received structure: §f${structure}`;
                }
                break;
            }
            case 'command': {
                const command = commandSettings.randomize ? commandSettings.commands[Math.floor(Math.random() * commandSettings.commands.length)] : commandSettings.commands[playerData.streak % commandSettings.commands.length];
                if (await rewards.grantCommandReward(player, command)) {
                    claimedSuccessfully = true;
                    message = `§aYou received a special command reward!`;
                }
                break;
            }
        }

        if (claimedSuccessfully) {
            await _handleSuccessfulClaim(player, playerData, message);
        } else {
            player.sendMessage("§cFailed to grant reward. Your claim has not been used. Please try again or contact an admin if the issue persists.");
        }
    } else {
        const menuChoice = menuOptions[response.selection - availableRewards.length];
        if (menuChoice === 'upcoming') {
            showUpcomingRewardsUI(player);
        }
    }
}