import { world, system, ItemStack } from "@minecraft/server";
import { DB_PREFIX } from './config.js';
import { DB } from './database.js';

const db = new DB();

export async function grantMoneyReward(player, amount, currencyObjective) {
    try {
        world.scoreboard.getObjective(currencyObjective);
    } catch (e) {
        console.error(`[DailyRewards] Scoreboard objective "${currencyObjective}" does not exist. Please create it with: /scoreboard objectives add "${currencyObjective}" dummy`);
        player.sendMessage("§cError: The server's money system is not configured correctly. Please notify an administrator.");
        return false;
    }
    const command = `scoreboard players add "${player.name}" "${currencyObjective}" ${Math.round(amount)}`;
    const result = await world.getDimension('overworld').runCommand(command);
    return result.successCount > 0;
}

export async function grantItemReward(player, itemId, amount = 1) {
    const command = `give "${player.name}" ${itemId} ${amount}`;
    const result = await world.getDimension('overworld').runCommand(command);
    return result.successCount > 0;
}

export async function grantStructureReward(player, structureName) {
    const { x, y, z } = player.location;
    const command = `structure load "${structureName}" ${Math.round(x)} ${Math.round(y)} ${Math.round(z)}`;
    const result = await world.getDimension('overworld').runCommand(command);
    return result.successCount > 0;
}

export async function grantCommandReward(player, commandTemplate) {
    const command = commandTemplate.replace(/{player}/g, player.name);
    const result = await world.getDimension('overworld').runCommand(command);
    return result.successCount > 0;
}

export function grantBlueprintReward(player, blueprintName) {
    const blueprintData = db.get(`${DB_PREFIX}blueprint_${blueprintName}`);
    if (!blueprintData) {
        console.warn(`[DailyRewards] Blueprint named "${blueprintName}" not found.`);
        return false;
    }
    try {
        const newItem = new ItemStack(blueprintData.typeId, blueprintData.amount);
        if (blueprintData.nameTag) {
            newItem.nameTag = blueprintData.nameTag;
        }
        if (blueprintData.lore && blueprintData.lore.length > 0) {
            newItem.setLore(blueprintData.lore);
        }
        const enchantable = newItem.getComponent('minecraft:enchantable');
        if (enchantable && blueprintData.enchantments && blueprintData.enchantments.length > 0) {
            for (const ench of blueprintData.enchantments) {
                 enchantable.addEnchantment({ typeId: ench.type, level: ench.level });
            }
        }
        const inventory = player.getComponent('minecraft:inventory').container;
        inventory.addItem(newItem);
        return true;
    } catch (e) {
        console.error(`Failed to create item from blueprint "${blueprintName}": ${e}`);
        return false;
    }
}

export function playSuccessEffects(player) {
    const { x, y, z } = player.location;
    try {
        player.runCommand(`particle minecraft:totem_particle ${x} ${y + 1} ${z}`);
        player.runCommand(`playsound random.levelup @s ~ ~ ~ 1 1.5`);
    } catch (e) {
        console.warn(`Could not play reward effects: ${e}`);
    }
}