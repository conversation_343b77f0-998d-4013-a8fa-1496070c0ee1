{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "emerald", "quantity": 5, "price_multiplier": 0.05}], "gives": [{"item": "enchanted_book", "aux": 0, "functions": [{"function": "enchant_with_levels", "levels": {"min": 5, "max": 10}, "treasure": false}]}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "emerald", "quantity": 10, "price_multiplier": 0.05}], "gives": [{"item": "enchanted_book", "aux": 0, "functions": [{"function": "specific_enchants", "enchants": [{"id": "protection", "level": 1}, {"id": "sharpness", "level": 1}, {"id": "efficiency", "level": 1}, {"id": "unbreaking", "level": 1}]}]}], "trader_exp": 5, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 20, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "emerald", "quantity": 20, "price_multiplier": 0.05}], "gives": [{"item": "enchanted_book", "aux": 0, "functions": [{"function": "specific_enchants", "enchants": [{"id": "protection", "level": 2}, {"id": "sharpness", "level": 2}, {"id": "efficiency", "level": 2}, {"id": "unbreaking", "level": 2}, {"id": "power", "level": 1}, {"id": "infinity", "level": 1}, {"id": "fortune", "level": 1}, {"id": "looting", "level": 1}]}]}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 30, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "emerald", "quantity": 30, "price_multiplier": 0.05}], "gives": [{"item": "enchanted_book", "aux": 0, "functions": [{"function": "specific_enchants", "enchants": [{"id": "protection", "level": 3}, {"id": "sharpness", "level": 3}, {"id": "efficiency", "level": 3}, {"id": "unbreaking", "level": 3}, {"id": "mending", "level": 1}, {"id": "power", "level": 2}, {"id": "fortune", "level": 2}, {"id": "looting", "level": 2}, {"id": "frost_walker", "level": 1}, {"id": "channeling", "level": 1}]}]}], "trader_exp": 15, "max_uses": 8, "reward_exp": true}]}]}, {"total_exp_required": 40, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "emerald", "quantity": 40, "price_multiplier": 0.05}, {"item": "book", "quantity": 1}], "gives": [{"item": "enchanted_book", "aux": 0, "functions": [{"function": "specific_enchants", "enchants": [{"id": "protection", "level": 4}, {"id": "sharpness", "level": 4}, {"id": "efficiency", "level": 4}, {"id": "unbreaking", "level": 3}, {"id": "mending", "level": 1}, {"id": "power", "level": 3}, {"id": "fortune", "level": 3}, {"id": "looting", "level": 3}, {"id": "frost_walker", "level": 2}, {"id": "channeling", "level": 1}, {"id": "soul_speed", "level": 1}, {"id": "swift_sneak", "level": 1}]}]}], "trader_exp": 20, "max_uses": 4, "reward_exp": true}]}]}]}