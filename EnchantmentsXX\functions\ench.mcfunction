execute @a[hasitem={item=z:swiftsneakiv}] ~~~ function swiftsneak/iv
execute @a[hasitem={item=z:swiftsneakv}] ~~~ function swiftsneak/v
execute @a[hasitem={item=z:lureiv}] ~~~ function lure/iv
execute @a[hasitem={item=z:lurev}] ~~~ function lure/v
execute @a[hasitem={item=z:luckseaiv}] ~~~ function lucksea/iv
execute @a[hasitem={item=z:luckseav}] ~~~ function lucksea/v
execute @a[hasitem={item=z:luckseavi}] ~~~ function lucksea/vi
execute @a[hasitem={item=z:luckseavii}] ~~~ function lucksea/vii
execute @a[hasitem={item=z:luckseaviii}] ~~~ function lucksea/viii
execute @a[hasitem={item=z:luckseaix}] ~~~ function lucksea/ix
execute @a[hasitem={item=z:luckseax}] ~~~ function lucksea/x
execute @a[hasitem={item=z:quickchargeiv}] ~~~ function quickcharge/iv
execute @a[hasitem={item=z:quickchargev}] ~~~ function quickcharge/v
execute @a[hasitem={item=z:piercingv}] ~~~ function piercing/v
execute @a[hasitem={item=z:piercingvi}] ~~~ function piercing/vi
execute @a[hasitem={item=z:piercingvii}] ~~~ function piercing/vii
execute @a[hasitem={item=z:piercingviii}] ~~~ function piercing/viii
execute @a[hasitem={item=z:piercingix}] ~~~ function piercing/ix
execute @a[hasitem={item=z:piercingx}] ~~~ function piercing/x
execute @a[hasitem={item=z:efficiencyvi}] ~~~ function efficiency/vi
execute @a[hasitem={item=z:efficiencyvii}] ~~~ function efficiency/vii
execute @a[hasitem={item=z:efficiencyviii}] ~~~ function efficiency/viii
execute @a[hasitem={item=z:efficiencyix}] ~~~ function efficiency/ix
execute @a[hasitem={item=z:efficiencyx}] ~~~ function efficiency/x
execute @a[hasitem={item=z:unbreakingiv}] ~~~ function unbreaking/iv
execute @a[hasitem={item=z:unbreakingv}] ~~~ function unbreaking/v
execute @a[hasitem={item=z:unbreakingvi}] ~~~ function unbreaking/vi
execute @a[hasitem={item=z:unbreakingvii}] ~~~ function unbreaking/vii
execute @a[hasitem={item=z:unbreakingviii}] ~~~ function unbreaking/viii
execute @a[hasitem={item=z:unbreakingix}] ~~~ function unbreaking/ix
execute @a[hasitem={item=z:unbreakingx}] ~~~ function unbreaking/x
execute @a[hasitem={item=z:fortuneiv}] ~~~ function fortune/iv
execute @a[hasitem={item=z:fortunev}] ~~~ function fortune/v
execute @a[hasitem={item=z:fortunevi}] ~~~ function fortune/vi
execute @a[hasitem={item=z:fortunevii}] ~~~ function fortune/vii
execute @a[hasitem={item=z:fortuneviii}] ~~~ function fortune/viii
execute @a[hasitem={item=z:fortuneix}] ~~~ function fortune/ix
execute @a[hasitem={item=z:fortunex}] ~~~ function fortune/x
execute @a[hasitem={item=z:sharpnessvi}] ~~~ function sharpness/vi
execute @a[hasitem={item=z:sharpnessvii}] ~~~ function sharpness/vii
execute @a[hasitem={item=z:sharpnessviii}] ~~~ function sharpness/viii
execute @a[hasitem={item=z:sharpnessix}] ~~~ function sharpness/ix
execute @a[hasitem={item=z:sharpnessx}] ~~~ function sharpness/x
execute @a[hasitem={item=z:smitevi}] ~~~ function smite/vi
execute @a[hasitem={item=z:smitevii}] ~~~ function smite/vii
execute @a[hasitem={item=z:smiteviii}] ~~~ function smite/viii
execute @a[hasitem={item=z:smiteix}] ~~~ function smite/ix
execute @a[hasitem={item=z:smitex}] ~~~ function smite/x
execute @a[hasitem={item=z:banearthropodsvi}] ~~~ function banearthropods/vi
execute @a[hasitem={item=z:banearthropodsvii}] ~~~ function banearthropods/vii
execute @a[hasitem={item=z:banearthropodsviii}] ~~~ function banearthropods/viii
execute @a[hasitem={item=z:banearthropodsix}] ~~~ function banearthropods/ix
execute @a[hasitem={item=z:banearthropodsx}] ~~~ function banearthropods/x
execute @a[hasitem={item=z:lootingiv}] ~~~ function looting/iv
execute @a[hasitem={item=z:lootingv}] ~~~ function looting/v
execute @a[hasitem={item=z:lootingvi}] ~~~ function looting/vi
execute @a[hasitem={item=z:lootingvii}] ~~~ function looting/vii
execute @a[hasitem={item=z:lootingviii}] ~~~ function looting/viii
execute @a[hasitem={item=z:lootingix}] ~~~ function looting/ix
execute @a[hasitem={item=z:lootingx}] ~~~ function looting/x
execute @a[hasitem={item=z:fireaspectiii}] ~~~ function fireaspect/iii
execute @a[hasitem={item=z:fireaspectiv}] ~~~ function fireaspect/iv
execute @a[hasitem={item=z:fireaspectv}] ~~~ function fireaspect/v
execute @a[hasitem={item=z:fireaspectvi}] ~~~ function fireaspect/vi
execute @a[hasitem={item=z:fireaspectvii}] ~~~ function fireaspect/vii
execute @a[hasitem={item=z:fireaspectviii}] ~~~ function fireaspect/viii
execute @a[hasitem={item=z:fireaspectix}] ~~~ function fireaspect/ix
execute @a[hasitem={item=z:fireaspectx}] ~~~ function fireaspect/x
execute @a[hasitem={item=z:knockbackiii}] ~~~ function knockback/iii
execute @a[hasitem={item=z:knockbackiv}] ~~~ function knockback/iv
execute @a[hasitem={item=z:knockbackv}] ~~~ function knockback/v
execute @a[hasitem={item=z:knockbackvi}] ~~~ function knockback/vi
execute @a[hasitem={item=z:knockbackvii}] ~~~ function knockback/vii
execute @a[hasitem={item=z:knockbackviii}] ~~~ function knockback/viii
execute @a[hasitem={item=z:knockbackix}] ~~~ function knockback/ix
execute @a[hasitem={item=z:knockbackx}] ~~~ function knockback/x
execute @a[hasitem={item=z:impalingvi}] ~~~ function impaling/vi
execute @a[hasitem={item=z:impalingvii}] ~~~ function impaling/vii
execute @a[hasitem={item=z:impalingviii}] ~~~ function impaling/viii
execute @a[hasitem={item=z:impalingix}] ~~~ function impaling/ix
execute @a[hasitem={item=z:impalingx}] ~~~ function impaling/x
execute @a[hasitem={item=z:riptideiv}] ~~~ function riptide/iv
execute @a[hasitem={item=z:riptidev}] ~~~ function riptide/v
execute @a[hasitem={item=z:riptidevi}] ~~~ function riptide/vi
execute @a[hasitem={item=z:riptidevii}] ~~~ function riptide/vii
execute @a[hasitem={item=z:riptideviii}] ~~~ function riptide/viii
execute @a[hasitem={item=z:riptideix}] ~~~ function riptide/ix
execute @a[hasitem={item=z:riptidex}] ~~~ function riptide/x
execute @a[hasitem={item=z:firetidei}] ~~~ function riptide/firetidei
execute @a[hasitem={item=z:loyalityiv}] ~~~ function loyality/iv
execute @a[hasitem={item=z:loyalityv}] ~~~ function loyality/v
execute @a[hasitem={item=z:loyalityvi}] ~~~ function loyality/vi
execute @a[hasitem={item=z:loyalityvii}] ~~~ function loyality/vii
execute @a[hasitem={item=z:loyalityviii}] ~~~ function loyality/viii
execute @a[hasitem={item=z:loyalityix}] ~~~ function loyality/ix
execute @a[hasitem={item=z:loyalityx}] ~~~ function loyality/x
execute @a[hasitem={item=z:punchiii}] ~~~ function punch/iii
execute @a[hasitem={item=z:punchiv}] ~~~ function punch/iv
execute @a[hasitem={item=z:punchv}] ~~~ function punch/v
execute @a[hasitem={item=z:punchvi}] ~~~ function punch/vi
execute @a[hasitem={item=z:punchvii}] ~~~ function punch/vii
execute @a[hasitem={item=z:punchviii}] ~~~ function punch/viii
execute @a[hasitem={item=z:punchix}] ~~~ function punch/ix
execute @a[hasitem={item=z:punchx}] ~~~ function punch/x
execute @a[hasitem={item=z:powervi}] ~~~ function power/vi
execute @a[hasitem={item=z:powervii}] ~~~ function power/vii
execute @a[hasitem={item=z:powerviii}] ~~~ function power/viii
execute @a[hasitem={item=z:powerix}] ~~~ function power/ix
execute @a[hasitem={item=z:powerx}] ~~~ function power/x
execute @a[hasitem={item=z:protectionv}] ~~~ function protection/v
execute @a[hasitem={item=z:protectionvi}] ~~~ function protection/vi
execute @a[hasitem={item=z:protectionvii}] ~~~ function protection/vii
execute @a[hasitem={item=z:protectionviii}] ~~~ function protection/viii
execute @a[hasitem={item=z:protectionix}] ~~~ function protection/ix
execute @a[hasitem={item=z:protectionx}] ~~~ function protection/x
execute @a[hasitem={item=z:protectionxi}] ~~~ function protection/xi
execute @a[hasitem={item=z:protectionxii}] ~~~ function protection/xii
execute @a[hasitem={item=z:protectionxiii}] ~~~ function protection/xiii
execute @a[hasitem={item=z:protectionxiv}] ~~~ function protection/xiv
execute @a[hasitem={item=z:protectionxv}] ~~~ function protection/xv
execute @a[hasitem={item=z:protectionxvi}] ~~~ function protection/xvi
execute @a[hasitem={item=z:protectionxvii}] ~~~ function protection/xvii
execute @a[hasitem={item=z:protectionxviii}] ~~~ function protection/xviii
execute @a[hasitem={item=z:protectionxix}] ~~~ function protection/xix
execute @a[hasitem={item=z:protectionxx}] ~~~ function protection/xx
execute @a[hasitem={item=z:protectionfirev}] ~~~ function protectionfire/v
execute @a[hasitem={item=z:protectionfirevi}] ~~~ function protectionfire/vi
execute @a[hasitem={item=z:protectionfirevii}] ~~~ function protectionfire/vii
execute @a[hasitem={item=z:protectionfireviii}] ~~~ function protectionfire/viii
execute @a[hasitem={item=z:protectionfireix}] ~~~ function protectionfire/ix
execute @a[hasitem={item=z:protectionfirex}] ~~~ function protectionfire/x
execute @a[hasitem={item=z:protectionexplv}] ~~~ function protectionexpl/v
execute @a[hasitem={item=z:protectionexplvi}] ~~~ function protectionexpl/vi
execute @a[hasitem={item=z:protectionexplvii}] ~~~ function protectionexpl/vii
execute @a[hasitem={item=z:protectionexplviii}] ~~~ function protectionexpl/viii
execute @a[hasitem={item=z:protectionexplix}] ~~~ function protectionexpl/ix
execute @a[hasitem={item=z:protectionexplx}] ~~~ function protectionexpl/x
execute @a[hasitem={item=z:protectionprojv}] ~~~ function protectionproj/v
execute @a[hasitem={item=z:protectionprojvi}] ~~~ function protectionproj/vi
execute @a[hasitem={item=z:protectionprojvii}] ~~~ function protectionproj/vii
execute @a[hasitem={item=z:protectionprojviii}] ~~~ function protectionproj/viii
execute @a[hasitem={item=z:protectionprojix}] ~~~ function protectionproj/ix
execute @a[hasitem={item=z:protectionprojx}] ~~~ function protectionproj/x
execute @a[hasitem={item=z:depthstrideriv}] ~~~ function depthstrider/iv
execute @a[hasitem={item=z:depthstriderv}] ~~~ function depthstrider/v
execute @a[hasitem={item=z:depthstridervi}] ~~~ function depthstrider/vi
execute @a[hasitem={item=z:depthstridervii}] ~~~ function depthstrider/vii
execute @a[hasitem={item=z:depthstriderviii}] ~~~ function depthstrider/viii
execute @a[hasitem={item=z:depthstriderix}] ~~~ function depthstrider/ix
execute @a[hasitem={item=z:depthstriderx}] ~~~ function depthstrider/x
execute @a[hasitem={item=z:featherfallingv}] ~~~ function featherfalling/v
execute @a[hasitem={item=z:featherfallingvi}] ~~~ function featherfalling/vi
execute @a[hasitem={item=z:featherfallingvii}] ~~~ function featherfalling/vii
execute @a[hasitem={item=z:featherfallingviii}] ~~~ function featherfalling/viii
execute @a[hasitem={item=z:featherfallingix}] ~~~ function featherfalling/ix
execute @a[hasitem={item=z:featherfallingx}] ~~~ function featherfalling/x
execute @a[hasitem={item=z:respirationiv}] ~~~ function respiration/iv
execute @a[hasitem={item=z:respirationv}] ~~~ function respiration/v
execute @a[hasitem={item=z:respirationvi}] ~~~ function respiration/vi
execute @a[hasitem={item=z:respirationvii}] ~~~ function respiration/vii
execute @a[hasitem={item=z:respirationviii}] ~~~ function respiration/viii
execute @a[hasitem={item=z:respirationix}] ~~~ function respiration/ix
execute @a[hasitem={item=z:respirationx}] ~~~ function respiration/x
execute @a[hasitem={item=z:thornsiv}] ~~~ function thorns/iv
execute @a[hasitem={item=z:thornsv}] ~~~ function thorns/v
execute @a[hasitem={item=z:thornsvi}] ~~~ function thorns/vi
execute @a[hasitem={item=z:thornsvii}] ~~~ function thorns/vii
execute @a[hasitem={item=z:thornsviii}] ~~~ function thorns/viii
execute @a[hasitem={item=z:thornsix}] ~~~ function thorns/ix
execute @a[hasitem={item=z:thornsx}] ~~~ function thorns/x
execute @a[hasitem={item=z:soulspeediv}] ~~~ function soulspeed/iv
execute @a[hasitem={item=z:soulspeedv}] ~~~ function soulspeed/v
execute @a[hasitem={item=z:soulspeedvi}] ~~~ function soulspeed/vi
execute @a[hasitem={item=z:soulspeedvii}] ~~~ function soulspeed/vii
execute @a[hasitem={item=z:soulspeedviii}] ~~~ function soulspeed/viii
execute @a[hasitem={item=z:soulspeedix}] ~~~ function soulspeed/ix
execute @a[hasitem={item=z:soulspeedx}] ~~~ function soulspeed/x
execute @a[hasitem={item=z:frostwalkeriii}] ~~~ function frostwalker/iii
execute @a[hasitem={item=z:frostwalkeriv}] ~~~ function frostwalker/iv
execute @a[hasitem={item=z:frostwalkerv}] ~~~ function frostwalker/v
execute @a[hasitem={item=z:frostwalkervi}] ~~~ function frostwalker/vi
execute @a[hasitem={item=z:frostwalkervii}] ~~~ function frostwalker/vii
execute @a[hasitem={item=z:frostwalkerviii}] ~~~ function frostwalker/viii
execute @a[hasitem={item=z:frostwalkerix}] ~~~ function frostwalker/ix
execute @a[hasitem={item=z:frostwalkerx}] ~~~ function frostwalker/x